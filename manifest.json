{"update_url": "https://clients2.google.com/service/update2/crx", "icons": {"16": "icon16.plasmo.754a7afc.png", "32": "icon32.plasmo.1b596b7f.png", "48": "icon48.plasmo.9c45e82d.png", "64": "icon64.plasmo.696cf0ec.png", "128": "icon128.plasmo.f5004619.png"}, "manifest_version": 3, "action": {"default_icon": {"16": "icon16.plasmo.754a7afc.png", "32": "icon32.plasmo.1b596b7f.png", "48": "icon48.plasmo.9c45e82d.png", "64": "icon64.plasmo.696cf0ec.png", "128": "icon128.plasmo.f5004619.png"}, "default_popup": "popup.html"}, "version": "5.6.2", "author": "Researchers <<EMAIL>>", "name": "Fireflies: AI meeting notes", "description": "Record & Transcribe meetings & calls directly from the browser with free unlimited transcriptions", "background": {"service_worker": "background.b78a8f7f.js"}, "permissions": ["scripting", "storage", "activeTab", "offscreen", "unlimitedStorage", "notifications", "tabs", "webNavigation"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["analytics.fe82b299.js"], "css": []}, {"matches": ["<all_urls>"], "js": ["log-sentry-errors.f7c161f0.js"], "css": []}, {"matches": ["*://meet.google.com/*-*-*"], "js": ["gmeetUserPanel.8e067572.js"], "css": ["gmeetUserPanel.09283736.css", "tabs/welcome.de8592c4.css", "tabs/welcome.ef7a11aa.css"]}, {"matches": ["*://meet.google.com/*-*-*"], "js": ["realtimePanel.621c8e3f.js"], "css": ["tabs/welcome.de8592c4.css", "tabs/welcome.ef7a11aa.css"]}, {"matches": ["<all_urls>"], "js": ["screenNotification.54aacb3c.js"], "css": ["tabs/welcome.de8592c4.css", "tabs/welcome.ef7a11aa.css"]}, {"matches": ["https://www.loom.com/share/*", "https://soapbox.wistia.com/videos/*"], "js": ["shareToFF.190acc52.js"], "css": ["tabs/welcome.de8592c4.css", "tabs/welcome.ef7a11aa.css"]}], "host_permissions": ["https://*/*"], "externally_connectable": {"matches": ["*://meet.google.com/*"]}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}, "oauth2": {"client_id": "964235282027-liib5ar4gi98crc4u3eomaa4cl0et8lg.apps.googleusercontent.com", "scopes": ["openid", "email", "profile", "https://www.googleapis.com/auth/calendar.readonly"]}, "web_accessible_resources": [{"resources": ["assets/images/*", "assets/fonts/*", "assets/ffmpeg/*", "assets/ffmpeg/*.wasm", "assets/ffmpeg/*.mp3"], "matches": ["<all_urls>"]}, {"matches": ["<all_urls>"], "resources": ["offscreen.4a2b8ac3.html", "offscreen.1b72e538.js", "realtimePanel.a08cc342.css"]}, {"matches": ["<all_urls>"], "resources": ["realtimePanel.a08cc342.css"]}, {"matches": ["*://meet.google.com/*"], "resources": ["offscreen.4a2b8ac3.html", "offscreen.1b72e538.js", "realtimePanel.a08cc342.css", "gmeetUserPanel.41b00494.css", "gmeetUserPanel.175e6fc5.css", "gmeetUserPanel.a6c5f5b3.css", "gmeetUserPanel.5aaa6ff6.css", "gmeetUserPanel.51fa1adc.css", "gmeetUserPanel.9a62dbcf.css", "gmeetUserPanel.bd758388.css", "gmeetUserPanel.2aa95f4f.css"]}, {"matches": ["*://meet.google.com/*"], "resources": ["offscreen.4a2b8ac3.html", "offscreen.1b72e538.js", "realtimePanel.a08cc342.css", "realtimePanel.4c1b134a.css", "realtimePanel.d4bbb661.css"]}, {"matches": ["<all_urls>"], "resources": ["offscreen.4a2b8ac3.html", "offscreen.1b72e538.js", "realtimePanel.a08cc342.css", "gmeetUserPanel.175e6fc5.css", "gmeetUserPanel.bd758388.css"]}, {"matches": ["https://www.loom.com/*", "https://soapbox.wistia.com/*"], "resources": ["offscreen.4a2b8ac3.html", "offscreen.1b72e538.js", "realtimePanel.a08cc342.css", "shareToFF.ea366ea2.css"]}]}