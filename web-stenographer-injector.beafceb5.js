(function(define){var __define; typeof define === "function" && (__define=define,define=null);
// modules are defined as an array
// [ module function, map of requires ]
//
// map of requires is short require name -> numeric require
//
// anything defined in a previous bundle is accessed via the
// orig method which is the require for previous bundles

(function (modules, entry, mainEntry, parcelRequireName, globalName) {
  /* eslint-disable no-undef */
  var globalObject =
    typeof globalThis !== 'undefined'
      ? globalThis
      : typeof self !== 'undefined'
      ? self
      : typeof window !== 'undefined'
      ? window
      : typeof global !== 'undefined'
      ? global
      : {};
  /* eslint-enable no-undef */

  // Save the require from previous bundle to this closure if any
  var previousRequire =
    typeof globalObject[parcelRequireName] === 'function' &&
    globalObject[parcelRequireName];

  var cache = previousRequire.cache || {};
  // Do not use `require` to prevent Webpack from trying to bundle this call
  var nodeRequire =
    typeof module !== 'undefined' &&
    typeof module.require === 'function' &&
    module.require.bind(module);

  function newRequire(name, jumped) {
    if (!cache[name]) {
      if (!modules[name]) {
        // if we cannot find the module within our internal map or
        // cache jump to the current global require ie. the last bundle
        // that was added to the page.
        var currentRequire =
          typeof globalObject[parcelRequireName] === 'function' &&
          globalObject[parcelRequireName];
        if (!jumped && currentRequire) {
          return currentRequire(name, true);
        }

        // If there are other bundles on this page the require from the
        // previous one is saved to 'previousRequire'. Repeat this as
        // many times as there are bundles until the module is found or
        // we exhaust the require chain.
        if (previousRequire) {
          return previousRequire(name, true);
        }

        // Try the node require function if it exists.
        if (nodeRequire && typeof name === 'string') {
          return nodeRequire(name);
        }

        var err = new Error("Cannot find module '" + name + "'");
        err.code = 'MODULE_NOT_FOUND';
        throw err;
      }

      localRequire.resolve = resolve;
      localRequire.cache = {};

      var module = (cache[name] = new newRequire.Module(name));

      modules[name][0].call(
        module.exports,
        localRequire,
        module,
        module.exports,
        this
      );
    }

    return cache[name].exports;

    function localRequire(x) {
      var res = localRequire.resolve(x);
      return res === false ? {} : newRequire(res);
    }

    function resolve(x) {
      var id = modules[name][1][x];
      return id != null ? id : x;
    }
  }

  function Module(moduleName) {
    this.id = moduleName;
    this.bundle = newRequire;
    this.exports = {};
  }

  newRequire.isParcelRequire = true;
  newRequire.Module = Module;
  newRequire.modules = modules;
  newRequire.cache = cache;
  newRequire.parent = previousRequire;
  newRequire.register = function (id, exports) {
    modules[id] = [
      function (require, module) {
        module.exports = exports;
      },
      {},
    ];
  };

  Object.defineProperty(newRequire, 'root', {
    get: function () {
      return globalObject[parcelRequireName];
    },
  });

  globalObject[parcelRequireName] = newRequire;

  for (var i = 0; i < entry.length; i++) {
    newRequire(entry[i]);
  }

  if (mainEntry) {
    // Expose entry point to Node, AMD or browser globals
    // Based on https://github.com/ForbesLindesay/umd/blob/master/template.js
    var mainExports = newRequire(mainEntry);

    // CommonJS
    if (typeof exports === 'object' && typeof module !== 'undefined') {
      module.exports = mainExports;

      // RequireJS
    } else if (typeof define === 'function' && define.amd) {
      define(function () {
        return mainExports;
      });

      // <script>
    } else if (globalName) {
      this[globalName] = mainExports;
    }
  }
})({"lmkzo":[function(require,module,exports) {
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getShadowHostId = exports.config = void 0;
var _webStenographer = require("ed8dd0196d0c1566");
var config = exports.config = {
    matches: [
        "*://meet.google.com/*-*-*"
    ],
    world: "MAIN",
    all_frames: true,
    run_at: "document_start"
};
var getShadowHostId = exports.getShadowHostId = function getShadowHostId() {
    return "ff-overlay-container";
};
try {
    if (!window.proxyPeerConnection) {
        console.log("[Fireflies Recorder] - injecting web-stenographer into page...");
        (0, _webStenographer.run)();
    }
} catch (e) {
    console.error("[Fireflies Recorder] - Error injecting web-stenographer into page", e);
}

},{"ed8dd0196d0c1566":"bPwOn"}],"bPwOn":[function(require,module,exports) {
/**
 * return the string payload to be injected into a page
 **/ var parcelHelpers = require("@parcel/transformer-js/src/esmodule-helpers.js");
parcelHelpers.defineInteropFlag(exports);
parcelHelpers.export(exports, "payload", ()=>payload);
/**
 * executes the web-stenographer payload function
 **/ parcelHelpers.export(exports, "run", ()=>run);
var global = arguments[3];
function payload() {
    return `!function e(t,n,r){function i(s,a){if(!n[s]){if(!t[s]){var l="function"==typeof require&&require;if(!a&&l)return l(s,!0);if(o)return o(s,!0);var c=new Error("Cannot find module '"+s+"'");throw c.code="MODULE_NOT_FOUND",c}var u=n[s]={exports:{}};t[s][0].call(u.exports,(function(e){return i(t[s][1][e]||e)}),u,u.exports,e,t,n,r)}return n[s].exports}for(var o="function"==typeof require&&require,s=0;s<r.length;s++)i(r[s]);return i}({1:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Manager=void 0;const r=e("../constants/default");n.Manager=(e,t,n,i,o,s,a=!1)=>{let l=!1,c=!1,u=!1,f=r.START_AUDIO_ID-1;const d=i=>{const d=i.detail;switch(d.cmd){case r.ServiceCommands.StartChatCapture:c=!0;break;case r.ServiceCommands.InitializeCaptions:l=!0,o.startCaptionsService();break;case r.ServiceCommands.GetUsers:a&&console.log(t.toArray()),e.notify(r.EVENT_ALL_USERS,t.toArray());break;case r.ServiceCommands.GetCaptions:a&&console.log(n.toArray()),e.notify(r.EVENT_ALL_CAPTIONS,n.toArray());break;case r.ServiceCommands.Stop:a&&console.log("Stopping captions service"),l=!1,o.stopCaptionsService();break;case r.ServiceCommands.StartRecorder:if(!o.startRecorder)break;a&&console.log("Starting recorder");const i=d.args;if(!i||i.segmentTs<0||!i.segmentTs){console.warn("Invalid recorder args segmentTs");break}let p=(null==s?void 0:s.getKeys())||[];p=p.sort(((e,t)=>t-e)),f=p[0]||r.START_AUDIO_ID-1,u=!0;try{o.startRecorder(i.segmentTs,i.type||"webrtc")}catch(t){const n=t.message+"  "+t.stack.substring(0,1e3);e.notify(r.WEB_STENOGRAPHER_ERROR,n)}break;case r.ServiceCommands.StopRecorder:if(!o.stopRecorder)break;a&&console.log("Stopping recorder"),u=!1,o.stopRecorder();break;case r.ServiceCommands.GetMetaData:e.notify(r.EVENT_METADATA,o.getMetadata());break;default:console.warn("unknown command: ",d)}},p=t=>{l&&e.notify(r.EVENT_USER,t)},h=t=>{l&&e.notify(r.EVENT_CC,t)},g=t=>{c&&e.notify(r.EVENT_CHAT,t)},m=t=>{if(u)for(a&&console.log("attempting to send audio with id",f+1,"where last id",f);s.has(f+1);)e.notify(r.EVENT_AUDIO,s.get(f+1)),f++};return{initialize:()=>{o.initialize(),t.subscribe(p),n.subscribe(h),i.subscribe(g),s&&s.subscribe(m),document.documentElement.addEventListener(r.FF_COMMAND_EVENT,d)}}}},{"../constants/default":4}],2:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.DefaultNotifier=void 0;n.DefaultNotifier=()=>({notify:(e,t)=>{const n=new window.CustomEvent(e,{detail:t});document.documentElement.dispatchEvent(n)}})},{}],3:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.createRepo=void 0;n.createRepo=()=>{const e=new Map;let t=23484;const n=new Map;return{set:(t,r)=>{e.set(t,r);for(const e of n.values())e(r)},get:t=>e.get(t),has:t=>e.has(t),toArray:()=>Array.from(e.values()),size:()=>e.size,subscribe:e=>(n.set(t,e),t++),unsubscribe:e=>{n.delete(e)},getKeys:()=>Array.from(e.keys())}}},{}],4:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ZOOM_PARTICIPANT_LIST=n.ZOOM_PARTICIPANT_WINDOW=n.ZOOM_POP_OUT_BUTTON=n.ZOOM_PARTICPANTS_BUTTON=n.ZOOM_PARENT_CAPTION_CONTAINER=n.CAPTION_CONTAINER_ID=n.PLATFORM_TYPES=n.WebMHeader=n.ServiceCommands=n.START_AUDIO_ID=n.FF_COMMAND_EVENT=n.WEB_STENOGRAPHER_LOG=n.WEB_STENOGRAPHER_ERROR=n.EVENT_ALL_CAPTIONS=n.EVENT_ALL_USERS=n.EVENT_METADATA=n.EVENT_USER=n.EVENT_AUDIO=n.EVENT_CHAT=n.EVENT_CC=void 0,n.EVENT_CC="ff-steno-notification-cc",n.EVENT_CHAT="ff-steno-notification-chat",n.EVENT_AUDIO="ff-steno-notification-audio",n.EVENT_USER="ff-steno-notification-user",n.EVENT_METADATA="ff-steno-meeting-metadata",n.EVENT_ALL_USERS="ff-steno-notification-all-users",n.EVENT_ALL_CAPTIONS="ff-steno-notification-all-captions",n.WEB_STENOGRAPHER_ERROR="ff-steno-error",n.WEB_STENOGRAPHER_LOG="ff-steno-logs",n.FF_COMMAND_EVENT="ff-command-event",n.START_AUDIO_ID=1e3,function(e){e.InitializeCaptions="initializeCaptions",e.StartChatCapture="startChatCapture",e.GetUsers="getUsers",e.GetCaptions="getCaptions",e.Stop="stopCaptions",e.StartRecorder="startRecorder",e.StopRecorder="stopRecorder",e.GetMetaData="getMetaData"}(n.ServiceCommands||(n.ServiceCommands={})),n.WebMHeader="data:webm/audio;base64,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",n.PLATFORM_TYPES={ZOOM:"Zoom",MS_TEAMS:"MsTeams",GOOGLE:"Google"},n.CAPTION_CONTAINER_ID={[n.PLATFORM_TYPES.ZOOM]:"live-transcription-subtitle",[n.PLATFORM_TYPES.MS_TEAMS]:"closed-caption-v2-virtual-list-content"},n.ZOOM_PARENT_CAPTION_CONTAINER="live-transcription-subtitle__box",n.ZOOM_PARTICPANTS_BUTTON="//button[contains(@aria-label, 'open the participants list pane')]",n.ZOOM_POP_OUT_BUTTON="//button[contains(@aria-label, 'Pop Out')]",n.ZOOM_PARTICIPANT_WINDOW="participant-window",n.ZOOM_PARTICIPANT_LIST="participants-ul"},{}],5:[function(e,t,n){"use strict";var r=e("protobufjs/minimal"),i=r.Reader,o=r.Writer,s=r.util,a=r.roots.default||(r.roots.default={});a.Caption=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.deviceSpace="",e.prototype.captionId=s.Long?s.Long.fromBits(0,0,!1):0,e.prototype.version=s.Long?s.Long.fromBits(0,0,!1):0,e.prototype.caption="",e.prototype.languageId=s.Long?s.Long.fromBits(0,0,!1):0,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.deviceSpace&&Object.hasOwnProperty.call(e,"deviceSpace")&&t.uint32(10).string(e.deviceSpace),null!=e.captionId&&Object.hasOwnProperty.call(e,"captionId")&&t.uint32(16).int64(e.captionId),null!=e.version&&Object.hasOwnProperty.call(e,"version")&&t.uint32(24).int64(e.version),null!=e.caption&&Object.hasOwnProperty.call(e,"caption")&&t.uint32(50).string(e.caption),null!=e.languageId&&Object.hasOwnProperty.call(e,"languageId")&&t.uint32(64).int64(e.languageId),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.Caption;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.deviceSpace=e.string();break;case 2:r.captionId=e.int64();break;case 3:r.version=e.int64();break;case 6:r.caption=e.string();break;case 8:r.languageId=e.int64();break;default:e.skipType(7&o)}}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.deviceSpace&&e.hasOwnProperty("deviceSpace")&&!s.isString(e.deviceSpace)?"deviceSpace: string expected":null!=e.captionId&&e.hasOwnProperty("captionId")&&!(s.isInteger(e.captionId)||e.captionId&&s.isInteger(e.captionId.low)&&s.isInteger(e.captionId.high))?"captionId: integer|Long expected":null!=e.version&&e.hasOwnProperty("version")&&!(s.isInteger(e.version)||e.version&&s.isInteger(e.version.low)&&s.isInteger(e.version.high))?"version: integer|Long expected":null!=e.caption&&e.hasOwnProperty("caption")&&!s.isString(e.caption)?"caption: string expected":null!=e.languageId&&e.hasOwnProperty("languageId")&&!(s.isInteger(e.languageId)||e.languageId&&s.isInteger(e.languageId.low)&&s.isInteger(e.languageId.high))?"languageId: integer|Long expected":null},e.fromObject=function(e){if(e instanceof a.Caption)return e;var t=new a.Caption;return null!=e.deviceSpace&&(t.deviceSpace=String(e.deviceSpace)),null!=e.captionId&&(s.Long?(t.captionId=s.Long.fromValue(e.captionId)).unsigned=!1:"string"==typeof e.captionId?t.captionId=parseInt(e.captionId,10):"number"==typeof e.captionId?t.captionId=e.captionId:"object"==typeof e.captionId&&(t.captionId=new s.LongBits(e.captionId.low>>>0,e.captionId.high>>>0).toNumber())),null!=e.version&&(s.Long?(t.version=s.Long.fromValue(e.version)).unsigned=!1:"string"==typeof e.version?t.version=parseInt(e.version,10):"number"==typeof e.version?t.version=e.version:"object"==typeof e.version&&(t.version=new s.LongBits(e.version.low>>>0,e.version.high>>>0).toNumber())),null!=e.caption&&(t.caption=String(e.caption)),null!=e.languageId&&(s.Long?(t.languageId=s.Long.fromValue(e.languageId)).unsigned=!1:"string"==typeof e.languageId?t.languageId=parseInt(e.languageId,10):"number"==typeof e.languageId?t.languageId=e.languageId:"object"==typeof e.languageId&&(t.languageId=new s.LongBits(e.languageId.low>>>0,e.languageId.high>>>0).toNumber())),t},e.toObject=function(e,t){t||(t={});var n={};if(t.defaults){if(n.deviceSpace="",s.Long){var r=new s.Long(0,0,!1);n.captionId=t.longs===String?r.toString():t.longs===Number?r.toNumber():r}else n.captionId=t.longs===String?"0":0;if(s.Long){r=new s.Long(0,0,!1);n.version=t.longs===String?r.toString():t.longs===Number?r.toNumber():r}else n.version=t.longs===String?"0":0;if(n.caption="",s.Long){r=new s.Long(0,0,!1);n.languageId=t.longs===String?r.toString():t.longs===Number?r.toNumber():r}else n.languageId=t.longs===String?"0":0}return null!=e.deviceSpace&&e.hasOwnProperty("deviceSpace")&&(n.deviceSpace=e.deviceSpace),null!=e.captionId&&e.hasOwnProperty("captionId")&&("number"==typeof e.captionId?n.captionId=t.longs===String?String(e.captionId):e.captionId:n.captionId=t.longs===String?s.Long.prototype.toString.call(e.captionId):t.longs===Number?new s.LongBits(e.captionId.low>>>0,e.captionId.high>>>0).toNumber():e.captionId),null!=e.version&&e.hasOwnProperty("version")&&("number"==typeof e.version?n.version=t.longs===String?String(e.version):e.version:n.version=t.longs===String?s.Long.prototype.toString.call(e.version):t.longs===Number?new s.LongBits(e.version.low>>>0,e.version.high>>>0).toNumber():e.version),null!=e.caption&&e.hasOwnProperty("caption")&&(n.caption=e.caption),null!=e.languageId&&e.hasOwnProperty("languageId")&&("number"==typeof e.languageId?n.languageId=t.longs===String?String(e.languageId):e.languageId:n.languageId=t.longs===String?s.Long.prototype.toString.call(e.languageId):t.longs===Number?new s.LongBits(e.languageId.low>>>0,e.languageId.high>>>0).toNumber():e.languageId),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.CaptionWrapper=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.caption=null,e.prototype.unknown="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.caption&&Object.hasOwnProperty.call(e,"caption")&&a.Caption.encode(e.caption,t.uint32(10).fork()).ldelim(),null!=e.unknown&&Object.hasOwnProperty.call(e,"unknown")&&t.uint32(18).string(e.unknown),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.CaptionWrapper;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.caption=a.Caption.decode(e,e.uint32());break;case 2:r.unknown=e.string();break;default:e.skipType(7&o)}}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.caption&&e.hasOwnProperty("caption")){var t=a.Caption.verify(e.caption);if(t)return"caption."+t}return null!=e.unknown&&e.hasOwnProperty("unknown")&&!s.isString(e.unknown)?"unknown: string expected":null},e.fromObject=function(e){if(e instanceof a.CaptionWrapper)return e;var t=new a.CaptionWrapper;if(null!=e.caption){if("object"!=typeof e.caption)throw TypeError(".CaptionWrapper.caption: object expected");t.caption=a.Caption.fromObject(e.caption)}return null!=e.unknown&&(t.unknown=String(e.unknown)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.caption=null,n.unknown=""),null!=e.caption&&e.hasOwnProperty("caption")&&(n.caption=a.Caption.toObject(e.caption,t)),null!=e.unknown&&e.hasOwnProperty("unknown")&&(n.unknown=e.unknown),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.UserDetails=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.deviceId="",e.prototype.fullName="",e.prototype.profile="",e.prototype.name="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.deviceId&&Object.hasOwnProperty.call(e,"deviceId")&&t.uint32(10).string(e.deviceId),null!=e.fullName&&Object.hasOwnProperty.call(e,"fullName")&&t.uint32(18).string(e.fullName),null!=e.profile&&Object.hasOwnProperty.call(e,"profile")&&t.uint32(26).string(e.profile),null!=e.name&&Object.hasOwnProperty.call(e,"name")&&t.uint32(234).string(e.name),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.UserDetails;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.deviceId=e.string();break;case 2:r.fullName=e.string();break;case 3:r.profile=e.string();break;case 29:r.name=e.string();break;default:e.skipType(7&o)}}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.deviceId&&e.hasOwnProperty("deviceId")&&!s.isString(e.deviceId)?"deviceId: string expected":null!=e.fullName&&e.hasOwnProperty("fullName")&&!s.isString(e.fullName)?"fullName: string expected":null!=e.profile&&e.hasOwnProperty("profile")&&!s.isString(e.profile)?"profile: string expected":null!=e.name&&e.hasOwnProperty("name")&&!s.isString(e.name)?"name: string expected":null},e.fromObject=function(e){if(e instanceof a.UserDetails)return e;var t=new a.UserDetails;return null!=e.deviceId&&(t.deviceId=String(e.deviceId)),null!=e.fullName&&(t.fullName=String(e.fullName)),null!=e.profile&&(t.profile=String(e.profile)),null!=e.name&&(t.name=String(e.name)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.deviceId="",n.fullName="",n.profile="",n.name=""),null!=e.deviceId&&e.hasOwnProperty("deviceId")&&(n.deviceId=e.deviceId),null!=e.fullName&&e.hasOwnProperty("fullName")&&(n.fullName=e.fullName),null!=e.profile&&e.hasOwnProperty("profile")&&(n.profile=e.profile),null!=e.name&&e.hasOwnProperty("name")&&(n.name=e.name),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.UserDetailsWrapper=function(){function e(e){if(this.userDetails=[],e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.userDetails=s.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=o.create()),null!=e.userDetails&&e.userDetails.length)for(var n=0;n<e.userDetails.length;++n)a.UserDetails.encode(e.userDetails[n],t.uint32(18).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.UserDetailsWrapper;e.pos<n;){var o=e.uint32();if(o>>>3==2)r.userDetails&&r.userDetails.length||(r.userDetails=[]),r.userDetails.push(a.UserDetails.decode(e,e.uint32()));else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.userDetails&&e.hasOwnProperty("userDetails")){if(!Array.isArray(e.userDetails))return"userDetails: array expected";for(var t=0;t<e.userDetails.length;++t){var n=a.UserDetails.verify(e.userDetails[t]);if(n)return"userDetails."+n}}return null},e.fromObject=function(e){if(e instanceof a.UserDetailsWrapper)return e;var t=new a.UserDetailsWrapper;if(e.userDetails){if(!Array.isArray(e.userDetails))throw TypeError(".UserDetailsWrapper.userDetails: array expected");t.userDetails=[];for(var n=0;n<e.userDetails.length;++n){if("object"!=typeof e.userDetails[n])throw TypeError(".UserDetailsWrapper.userDetails: object expected");t.userDetails[n]=a.UserDetails.fromObject(e.userDetails[n])}}return t},e.toObject=function(e,t){t||(t={});var n={};if((t.arrays||t.defaults)&&(n.userDetails=[]),e.userDetails&&e.userDetails.length){n.userDetails=[];for(var r=0;r<e.userDetails.length;++r)n.userDetails[r]=a.UserDetails.toObject(e.userDetails[r],t)}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.SpaceCollection=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.wrapper=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.wrapper&&Object.hasOwnProperty.call(e,"wrapper")&&a.UserDetailsWrapper.encode(e.wrapper,t.uint32(18).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.SpaceCollection;e.pos<n;){var o=e.uint32();if(o>>>3==2)r.wrapper=a.UserDetailsWrapper.decode(e,e.uint32());else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.wrapper&&e.hasOwnProperty("wrapper")){var t=a.UserDetailsWrapper.verify(e.wrapper);if(t)return"wrapper."+t}return null},e.fromObject=function(e){if(e instanceof a.SpaceCollection)return e;var t=new a.SpaceCollection;if(null!=e.wrapper){if("object"!=typeof e.wrapper)throw TypeError(".SpaceCollection.wrapper: object expected");t.wrapper=a.UserDetailsWrapper.fromObject(e.wrapper)}return t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.wrapper=null),null!=e.wrapper&&e.hasOwnProperty("wrapper")&&(n.wrapper=a.UserDetailsWrapper.toObject(e.wrapper,t)),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.MeetingSpaceCollectionResponse=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.spaces=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.spaces&&Object.hasOwnProperty.call(e,"spaces")&&a.SpaceCollection.encode(e.spaces,t.uint32(18).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.MeetingSpaceCollectionResponse;e.pos<n;){var o=e.uint32();if(o>>>3==2)r.spaces=a.SpaceCollection.decode(e,e.uint32());else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.spaces&&e.hasOwnProperty("spaces")){var t=a.SpaceCollection.verify(e.spaces);if(t)return"spaces."+t}return null},e.fromObject=function(e){if(e instanceof a.MeetingSpaceCollectionResponse)return e;var t=new a.MeetingSpaceCollectionResponse;if(null!=e.spaces){if("object"!=typeof e.spaces)throw TypeError(".MeetingSpaceCollectionResponse.spaces: object expected");t.spaces=a.SpaceCollection.fromObject(e.spaces)}return t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.spaces=null),null!=e.spaces&&e.hasOwnProperty("spaces")&&(n.spaces=a.SpaceCollection.toObject(e.spaces,t)),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.ChatText=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.text="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.text&&Object.hasOwnProperty.call(e,"text")&&t.uint32(10).string(e.text),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.ChatText;e.pos<n;){var o=e.uint32();if(o>>>3==1)r.text=e.string();else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.text&&e.hasOwnProperty("text")&&!s.isString(e.text)?"text: string expected":null},e.fromObject=function(e){if(e instanceof a.ChatText)return e;var t=new a.ChatText;return null!=e.text&&(t.text=String(e.text)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.text=""),null!=e.text&&e.hasOwnProperty("text")&&(n.text=e.text),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.ChatData=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.messageId="",e.prototype.deviceId="",e.prototype.timestamp=s.Long?s.Long.fromBits(0,0,!1):0,e.prototype.msg=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.messageId&&Object.hasOwnProperty.call(e,"messageId")&&t.uint32(10).string(e.messageId),null!=e.deviceId&&Object.hasOwnProperty.call(e,"deviceId")&&t.uint32(18).string(e.deviceId),null!=e.timestamp&&Object.hasOwnProperty.call(e,"timestamp")&&t.uint32(24).int64(e.timestamp),null!=e.msg&&Object.hasOwnProperty.call(e,"msg")&&a.ChatText.encode(e.msg,t.uint32(42).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.ChatData;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.messageId=e.string();break;case 2:r.deviceId=e.string();break;case 3:r.timestamp=e.int64();break;case 5:r.msg=a.ChatText.decode(e,e.uint32());break;default:e.skipType(7&o)}}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.messageId&&e.hasOwnProperty("messageId")&&!s.isString(e.messageId))return"messageId: string expected";if(null!=e.deviceId&&e.hasOwnProperty("deviceId")&&!s.isString(e.deviceId))return"deviceId: string expected";if(null!=e.timestamp&&e.hasOwnProperty("timestamp")&&!(s.isInteger(e.timestamp)||e.timestamp&&s.isInteger(e.timestamp.low)&&s.isInteger(e.timestamp.high)))return"timestamp: integer|Long expected";if(null!=e.msg&&e.hasOwnProperty("msg")){var t=a.ChatText.verify(e.msg);if(t)return"msg."+t}return null},e.fromObject=function(e){if(e instanceof a.ChatData)return e;var t=new a.ChatData;if(null!=e.messageId&&(t.messageId=String(e.messageId)),null!=e.deviceId&&(t.deviceId=String(e.deviceId)),null!=e.timestamp&&(s.Long?(t.timestamp=s.Long.fromValue(e.timestamp)).unsigned=!1:"string"==typeof e.timestamp?t.timestamp=parseInt(e.timestamp,10):"number"==typeof e.timestamp?t.timestamp=e.timestamp:"object"==typeof e.timestamp&&(t.timestamp=new s.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber())),null!=e.msg){if("object"!=typeof e.msg)throw TypeError(".ChatData.msg: object expected");t.msg=a.ChatText.fromObject(e.msg)}return t},e.toObject=function(e,t){t||(t={});var n={};if(t.defaults){if(n.messageId="",n.deviceId="",s.Long){var r=new s.Long(0,0,!1);n.timestamp=t.longs===String?r.toString():t.longs===Number?r.toNumber():r}else n.timestamp=t.longs===String?"0":0;n.msg=null}return null!=e.messageId&&e.hasOwnProperty("messageId")&&(n.messageId=e.messageId),null!=e.deviceId&&e.hasOwnProperty("deviceId")&&(n.deviceId=e.deviceId),null!=e.timestamp&&e.hasOwnProperty("timestamp")&&("number"==typeof e.timestamp?n.timestamp=t.longs===String?String(e.timestamp):e.timestamp:n.timestamp=t.longs===String?s.Long.prototype.toString.call(e.timestamp):t.longs===Number?new s.LongBits(e.timestamp.low>>>0,e.timestamp.high>>>0).toNumber():e.timestamp),null!=e.msg&&e.hasOwnProperty("msg")&&(n.msg=a.ChatText.toObject(e.msg,t)),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.ChatWrapper=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.body=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.body&&Object.hasOwnProperty.call(e,"body")&&a.ChatData.encode(e.body,t.uint32(18).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.ChatWrapper;e.pos<n;){var o=e.uint32();if(o>>>3==2)r.body=a.ChatData.decode(e,e.uint32());else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.body&&e.hasOwnProperty("body")){var t=a.ChatData.verify(e.body);if(t)return"body."+t}return null},e.fromObject=function(e){if(e instanceof a.ChatWrapper)return e;var t=new a.ChatWrapper;if(null!=e.body){if("object"!=typeof e.body)throw TypeError(".ChatWrapper.body: object expected");t.body=a.ChatData.fromObject(e.body)}return t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.body=null),null!=e.body&&e.hasOwnProperty("body")&&(n.body=a.ChatData.toObject(e.body,t)),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.Wrapper3=function(){function e(e){if(this.userDetails=[],e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.userDetails=s.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=o.create()),null!=e.userDetails&&e.userDetails.length)for(var n=0;n<e.userDetails.length;++n)a.UserDetails.encode(e.userDetails[n],t.uint32(18).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.Wrapper3;e.pos<n;){var o=e.uint32();if(o>>>3==2)r.userDetails&&r.userDetails.length||(r.userDetails=[]),r.userDetails.push(a.UserDetails.decode(e,e.uint32()));else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.userDetails&&e.hasOwnProperty("userDetails")){if(!Array.isArray(e.userDetails))return"userDetails: array expected";for(var t=0;t<e.userDetails.length;++t){var n=a.UserDetails.verify(e.userDetails[t]);if(n)return"userDetails."+n}}return null},e.fromObject=function(e){if(e instanceof a.Wrapper3)return e;var t=new a.Wrapper3;if(e.userDetails){if(!Array.isArray(e.userDetails))throw TypeError(".Wrapper3.userDetails: array expected");t.userDetails=[];for(var n=0;n<e.userDetails.length;++n){if("object"!=typeof e.userDetails[n])throw TypeError(".Wrapper3.userDetails: object expected");t.userDetails[n]=a.UserDetails.fromObject(e.userDetails[n])}}return t},e.toObject=function(e,t){t||(t={});var n={};if((t.arrays||t.defaults)&&(n.userDetails=[]),e.userDetails&&e.userDetails.length){n.userDetails=[];for(var r=0;r<e.userDetails.length;++r)n.userDetails[r]=a.UserDetails.toObject(e.userDetails[r],t)}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.Wrapper2=function(){function e(e){if(this.chat=[],e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.wrapper=null,e.prototype.chat=s.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=o.create()),null!=e.wrapper&&Object.hasOwnProperty.call(e,"wrapper")&&a.Wrapper3.encode(e.wrapper,t.uint32(10).fork()).ldelim(),null!=e.chat&&e.chat.length)for(var n=0;n<e.chat.length;++n)a.ChatWrapper.encode(e.chat[n],t.uint32(34).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.Wrapper2;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.wrapper=a.Wrapper3.decode(e,e.uint32());break;case 4:r.chat&&r.chat.length||(r.chat=[]),r.chat.push(a.ChatWrapper.decode(e,e.uint32()));break;default:e.skipType(7&o)}}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.wrapper&&e.hasOwnProperty("wrapper")&&(n=a.Wrapper3.verify(e.wrapper)))return"wrapper."+n;if(null!=e.chat&&e.hasOwnProperty("chat")){if(!Array.isArray(e.chat))return"chat: array expected";for(var t=0;t<e.chat.length;++t){var n;if(n=a.ChatWrapper.verify(e.chat[t]))return"chat."+n}}return null},e.fromObject=function(e){if(e instanceof a.Wrapper2)return e;var t=new a.Wrapper2;if(null!=e.wrapper){if("object"!=typeof e.wrapper)throw TypeError(".Wrapper2.wrapper: object expected");t.wrapper=a.Wrapper3.fromObject(e.wrapper)}if(e.chat){if(!Array.isArray(e.chat))throw TypeError(".Wrapper2.chat: array expected");t.chat=[];for(var n=0;n<e.chat.length;++n){if("object"!=typeof e.chat[n])throw TypeError(".Wrapper2.chat: object expected");t.chat[n]=a.ChatWrapper.fromObject(e.chat[n])}}return t},e.toObject=function(e,t){t||(t={});var n={};if((t.arrays||t.defaults)&&(n.chat=[]),t.defaults&&(n.wrapper=null),null!=e.wrapper&&e.hasOwnProperty("wrapper")&&(n.wrapper=a.Wrapper3.toObject(e.wrapper,t)),e.chat&&e.chat.length){n.chat=[];for(var r=0;r<e.chat.length;++r)n.chat[r]=a.ChatWrapper.toObject(e.chat[r],t)}return n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.Wrapper1=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.wrapper=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.wrapper&&Object.hasOwnProperty.call(e,"wrapper")&&a.Wrapper2.encode(e.wrapper,t.uint32(106).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.Wrapper1;e.pos<n;){var o=e.uint32();if(o>>>3==13)r.wrapper=a.Wrapper2.decode(e,e.uint32());else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.wrapper&&e.hasOwnProperty("wrapper")){var t=a.Wrapper2.verify(e.wrapper);if(t)return"wrapper."+t}return null},e.fromObject=function(e){if(e instanceof a.Wrapper1)return e;var t=new a.Wrapper1;if(null!=e.wrapper){if("object"!=typeof e.wrapper)throw TypeError(".Wrapper1.wrapper: object expected");t.wrapper=a.Wrapper2.fromObject(e.wrapper)}return t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.wrapper=null),null!=e.wrapper&&e.hasOwnProperty("wrapper")&&(n.wrapper=a.Wrapper2.toObject(e.wrapper,t)),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.CollectionMessageBody=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.wrapper=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.wrapper&&Object.hasOwnProperty.call(e,"wrapper")&&a.Wrapper1.encode(e.wrapper,t.uint32(18).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.CollectionMessageBody;e.pos<n;){var o=e.uint32();if(o>>>3==2)r.wrapper=a.Wrapper1.decode(e,e.uint32());else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.wrapper&&e.hasOwnProperty("wrapper")){var t=a.Wrapper1.verify(e.wrapper);if(t)return"wrapper."+t}return null},e.fromObject=function(e){if(e instanceof a.CollectionMessageBody)return e;var t=new a.CollectionMessageBody;if(null!=e.wrapper){if("object"!=typeof e.wrapper)throw TypeError(".CollectionMessageBody.wrapper: object expected");t.wrapper=a.Wrapper1.fromObject(e.wrapper)}return t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.wrapper=null),null!=e.wrapper&&e.hasOwnProperty("wrapper")&&(n.wrapper=a.Wrapper1.toObject(e.wrapper,t)),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.CollectionMessage=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.body=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.body&&Object.hasOwnProperty.call(e,"body")&&a.CollectionMessageBody.encode(e.body,t.uint32(10).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.CollectionMessage;e.pos<n;){var o=e.uint32();if(o>>>3==1)r.body=a.CollectionMessageBody.decode(e,e.uint32());else e.skipType(7&o)}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.body&&e.hasOwnProperty("body")){var t=a.CollectionMessageBody.verify(e.body);if(t)return"body."+t}return null},e.fromObject=function(e){if(e instanceof a.CollectionMessage)return e;var t=new a.CollectionMessage;if(null!=e.body){if("object"!=typeof e.body)throw TypeError(".CollectionMessage.body: object expected");t.body=a.CollectionMessageBody.fromObject(e.body)}return t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.body=null),null!=e.body&&e.hasOwnProperty("body")&&(n.body=a.CollectionMessageBody.toObject(e.body,t)),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),a.ResolveMeeting=function(){function e(e){if(e)for(var t=Object.keys(e),n=0;n<t.length;++n)null!=e[t[n]]&&(this[t[n]]=e[t[n]])}return e.prototype.spaceId="",e.prototype.meetingId="",e.prototype.hangoutsUrl="",e.prototype.title="",e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=o.create()),null!=e.spaceId&&Object.hasOwnProperty.call(e,"spaceId")&&t.uint32(10).string(e.spaceId),null!=e.meetingId&&Object.hasOwnProperty.call(e,"meetingId")&&t.uint32(18).string(e.meetingId),null!=e.hangoutsUrl&&Object.hasOwnProperty.call(e,"hangoutsUrl")&&t.uint32(26).string(e.hangoutsUrl),null!=e.title&&Object.hasOwnProperty.call(e,"title")&&t.uint32(58).string(e.title),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof i||(e=i.create(e));for(var n=void 0===t?e.len:e.pos+t,r=new a.ResolveMeeting;e.pos<n;){var o=e.uint32();switch(o>>>3){case 1:r.spaceId=e.string();break;case 2:r.meetingId=e.string();break;case 3:r.hangoutsUrl=e.string();break;case 7:r.title=e.string();break;default:e.skipType(7&o)}}return r},e.decodeDelimited=function(e){return e instanceof i||(e=new i(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.spaceId&&e.hasOwnProperty("spaceId")&&!s.isString(e.spaceId)?"spaceId: string expected":null!=e.meetingId&&e.hasOwnProperty("meetingId")&&!s.isString(e.meetingId)?"meetingId: string expected":null!=e.hangoutsUrl&&e.hasOwnProperty("hangoutsUrl")&&!s.isString(e.hangoutsUrl)?"hangoutsUrl: string expected":null!=e.title&&e.hasOwnProperty("title")&&!s.isString(e.title)?"title: string expected":null},e.fromObject=function(e){if(e instanceof a.ResolveMeeting)return e;var t=new a.ResolveMeeting;return null!=e.spaceId&&(t.spaceId=String(e.spaceId)),null!=e.meetingId&&(t.meetingId=String(e.meetingId)),null!=e.hangoutsUrl&&(t.hangoutsUrl=String(e.hangoutsUrl)),null!=e.title&&(t.title=String(e.title)),t},e.toObject=function(e,t){t||(t={});var n={};return t.defaults&&(n.spaceId="",n.meetingId="",n.hangoutsUrl="",n.title=""),null!=e.spaceId&&e.hasOwnProperty("spaceId")&&(n.spaceId=e.spaceId),null!=e.meetingId&&e.hasOwnProperty("meetingId")&&(n.meetingId=e.meetingId),null!=e.hangoutsUrl&&e.hasOwnProperty("hangoutsUrl")&&(n.hangoutsUrl=e.hangoutsUrl),null!=e.title&&e.hasOwnProperty("title")&&(n.title=e.title),n},e.prototype.toJSON=function(){return this.constructor.toObject(this,r.util.toJSONOptions)},e}(),t.exports=a},{"protobufjs/minimal":43}],6:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.tryTo=n.debug=n.pad=n.xpath=n.getCommonAncestor=n.parents=void 0;n.parents=e=>{const t=[e];for(;e;e=e.parentNode)t.unshift(e);return t};n.getCommonAncestor=(e,t)=>{const r=(0,n.parents)(e),i=(0,n.parents)(t);if(r[0]===i[0])for(let e=0;e<r.length;e++)if(r[e]!==i[e])return r[e-1]};n.xpath=(e,t=document)=>document.evaluate(e,t,null,XPathResult.FIRST_ORDERED_NODE_TYPE).singleNodeValue;n.pad=e=>e<10?"0"+e:e.toString();n.debug=(...e)=>{console.log(...e)};n.tryTo=(e,t)=>async(...n)=>{try{return await e(...n)}catch(e){console.error("error "+t+":",e)}}},{}],7:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getWebMHeader=n.base64ToBlob=n.unzip=n.isGzip=void 0;const r=e("../constants/default"),i=e("pako");function o(e){if(!e||e.length<3)return!1;const t=[31,139,8];return e.slice(0,3).every(((e,n)=>e===t[n]))}function s(e){return new Promise(((t,n)=>{try{const n=e.split(","),r=n[0].split(":")[1],i=window.atob(n[1]),o=i.length,s=new Uint8Array(o);for(let e=0;e<o;++e)s[e]=i.charCodeAt(e);t(new Blob([s],{type:r.split(";")[0]}))}catch(e){n(e)}}))}n.isGzip=o,n.unzip=function(e){const t=new Uint8Array(e);if(o(t))try{return(0,i.inflate)(t)}catch(e){console.log(e)}return t},n.base64ToBlob=s,n.getWebMHeader=function(){return s(r.WebMHeader)}},{"../constants/default":4,pako:27}],8:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});const r=e("./platforms/google"),i=e("./platforms/zoom"),o=e("./platforms/msteams"),s=e("./components/repository"),a=e("./proxies"),l=e("./components/manager"),c=e("./components/notifier"),u=e("./constants/default");!function(){if(window.has_injected_stenographer)return;window.has_injected_stenographer=!0;const e=(0,s.createRepo)(),t=(0,s.createRepo)(),n=(0,s.createRepo)(),f=(0,s.createRepo)(),d=window.PLATFORM_TYPE||u.PLATFORM_TYPES.GOOGLE;let p;switch(d){case u.PLATFORM_TYPES.ZOOM:p=(0,i.Zoom)(e,t);break;case u.PLATFORM_TYPES.MS_TEAMS:p=(0,o.MsTeams)(e,t);break;default:p=(0,r.GoogleMeets)(e,t,f,n,(0,a.WebRtcProxy)(),(0,a.FetchProxy)(),(0,a.RtcSenderProxy)())}(0,l.Manager)((0,c.DefaultNotifier)(),e,t,n,p,f,!1).initialize(),console.log("Stenographer initialized for "+d+" at: "+Date.now())}()},{"./components/manager":1,"./components/notifier":2,"./components/repository":3,"./constants/default":4,"./platforms/google":9,"./platforms/msteams":10,"./platforms/zoom":11,"./proxies":13}],9:[function(e,t,n){(function(t){(function(){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.GoogleMeets=void 0;const r=e("../proxies"),i=e("../lib/utils"),o=e("../decoder/decoder"),s=e("../constants/default"),a=e("../components/notifier");n.GoogleMeets=(e,n,l,c,u,f,d,p=!1)=>{let h,g,m=2643,y=!1,b=null,w=s.START_AUDIO_ID,_=null;const v=[],O=new Map,E=(0,r.XhrProxy)();let k=null,A=null;const S=(0,a.DefaultNotifier)(),x=[],T=t=>{p&&console.log("collection message: ",t);const n=(0,i.unzip)(t.data),r=o.CollectionMessage.decode(n);if(r.body&&r.body.wrapper&&r.body.wrapper.wrapper){if(r.body.wrapper.wrapper.chat){const t=r.body.wrapper.wrapper.chat;for(const n of t){const t=e.get(n.body.deviceId);c.set(n.body.messageId,Object.assign(Object.assign({},n.body),{user:{name:(null==t?void 0:t.name)||"",fullName:(null==t?void 0:t.fullName)||"",image:(null==t?void 0:t.image)||"",id:(null==t?void 0:t.id)||""}}))}}if(r.body.wrapper.wrapper.wrapper){const t=r.body.wrapper.wrapper.wrapper.userDetails;if(t)for(const n of t)e.set(n.deviceId,{id:n.deviceId,name:n.name,fullName:n.fullName,image:n.profile})}}},I=(e,t)=>{"collections"===t.channel.label&&(window.proxyPeerConnection=e,p&&console.log("data channel message: ",t),t.channel.addEventListener("message",T))};let R=65110;const C=[],N=new Map,D=t=>{try{const s=(0,i.unzip)(t.data),a=o.CaptionWrapper.decode(s);if(""!=a.unknown)return void console.log("unknown data found: ",(r=s,[...new Uint8Array(r)].map((e=>e.toString(16).padStart(2,"0"))).join("")));if(C.length>50){const e=C.shift();N.delete(e)}const l=a.caption.captionId+"/"+a.caption.deviceSpace;let c=N.get(l);c||(c=R++,C.push(l),N.set(l,c));const u=n.has(c);let f;const d=e.get(a.caption.deviceSpace),p=O.get(d.id)||-1;if(c>p&&O.set(d.id,c),u){const e=n.get(c);let t=Date.now();e.messageId<p&&(t=e.endTs),f=Object.assign(Object.assign({},e),{endTs:t,caption:a.caption.caption,sequence:a.caption.version,updatedAt:Date.now()})}else f={messageId:c,receivedCaptionId:a.caption.captionId,caption:a.caption.caption,sequence:a.caption.version,firstReceiveTs:Date.now(),updatedAt:Date.now(),endTs:Date.now(),user:{id:d.id,name:d.name,fullName:d.fullName,image:d.image}};n.set(a.caption.captionId,f)}catch(e){console.log(e);const t=e.message+" "+e.stack.substring(0,1e3);S.notify(s.WEB_STENOGRAPHER_ERROR,"CaptionMessage "+t)}var r},P=async t=>{p&&console.log("sync meeting space collections: extraction start");try{const n=await t.text(),r=Uint8Array.from(window.atob(n),(e=>e.charCodeAt(0))),i=o.MeetingSpaceCollectionResponse.decode(r);if(i.spaces&&i.spaces.wrapper&&i.spaces.wrapper.userDetails){const t=i.spaces.wrapper.userDetails;for(const n of t)e.set(n.deviceId,{id:n.deviceId,name:n.name,fullName:n.fullName,image:n.profile})}}catch(e){const t=e.message+" "+e.stack.substring(0,1e3);S.notify(s.WEB_STENOGRAPHER_ERROR,"SynMeetingSpaceCollection "+t),console.log(e)}},B=async t=>{p&&console.log("trying to capture sent comment data");try{const n=await t.text(),r=Uint8Array.from(window.atob(n),(e=>e.charCodeAt(0))),i=o.ChatData.decode(r);if(!i)return;const s=e.get(i.deviceId);c.set(i.messageId,Object.assign(Object.assign({},i),{user:{name:(null==s?void 0:s.name)||"",fullName:(null==s?void 0:s.fullName)||"",image:(null==s?void 0:s.image)||"",id:(null==s?void 0:s.id)||""}}))}catch(e){const t=e.message+" "+e.stack.substring(0,1e3);S.notify(s.WEB_STENOGRAPHER_ERROR,"SendComment "+t),console.log(e)}},j=async e=>{const n=await e.text(),r=t.from(n,"base64"),i=o.ResolveMeeting.decode(Uint8Array.from(r));A={kind:"fallback",summary:i.title,hangoutLink:i.hangoutsUrl}},M=e=>{y||("closing"===e.readyState||"closed"===e.readyState?L():setTimeout((()=>{M(e)}),1e3))},L=()=>{if(!window.proxyPeerConnection)return console.error("no proxy peer connection found"),void S.notify(s.WEB_STENOGRAPHER_ERROR,"stCaptionService NoProxyPeerConnection");y=!1,["disconnected","failed","closed"].includes(window.proxyPeerConnection.connectionState)||window.proxyPeerConnection.createDataChannel("captions",{ordered:!0,maxRetransmits:100,id:m++})},U=e=>{_?0!==e.getAudioTracks().length?h?_.createMediaStreamSource(e).connect(h):S.notify(s.WEB_STENOGRAPHER_ERROR,"addStreamToDestination no audio destination"):p&&console.log("stream doesn't have audio tracks stream::",e.id):S.notify(s.WEB_STENOGRAPHER_ERROR,"addStreamToDestination no audio context")},z=(e,t)=>{if(0!=t.streams.length&&"closed"!==e.connectionState)for(const e of t.streams)e.getAudioTracks().length>0&&(v.push(e),U(e))},W=e=>{const t=new MediaStream;t.addTrack(e),v.push(t),x.push(t),U(t)},Z=e=>{l.set(w++,e.data)},F=e=>{try{if(!_)return S.notify(s.WEB_STENOGRAPHER_ERROR,"WebRTC has no audio context"),p&&console.log("no audio context"),!1;h=_.createMediaStreamDestination();for(const e of v)U(e);return H(e),!0}catch(e){const t=e.message+" "+e.stack.substring(0,1e3);S.notify(s.WEB_STENOGRAPHER_ERROR,"WebRTC::AUR  "+t),console.error(e)}},H=async e=>{if(_){_&&"running"!==_.state&&(S.notify(s.WEB_STENOGRAPHER_ERROR,"audio context is not running. trying to wake it up"),await _.resume()),g=new MediaRecorder(h.stream,{mimeType:"audio/webm"}),window.ff_media_recorder=g,g.addEventListener("dataavailable",Z),w!==s.START_AUDIO_ID&&(S.notify(s.WEB_STENOGRAPHER_ERROR,"audio id is not correct, got: "+w+" expected: "+s.START_AUDIO_ID),(0,i.getWebMHeader)().then((e=>{const t="adding header to audio. header size:"+(e&&e.size)+" with id "+w;console.log(t),S.notify(s.WEB_STENOGRAPHER_LOG,t),l.set(w,e),w++})));try{g.start(e)}catch(e){const t=e.message+" "+e.stack.substring(0,1e3);S.notify(s.WEB_STENOGRAPHER_ERROR,"web-stenographer start audio recording failed: "+t)}S.notify(s.WEB_STENOGRAPHER_LOG,"audio recorder started")}else S.notify(s.WEB_STENOGRAPHER_ERROR,"no audio context")},G=function(e){const t=document.getElementsByTagName("audio");if(0===t.length)throw new Error("No audio elements found on page");if(!_)throw new Error("No audio context");h=_.createMediaStreamDestination();for(const e of x)U(e);for(const e of t){const t=e.srcObject;_.createMediaStreamSource(t).connect(h)}return H(e),!0};const V=()=>{_&&window.ff_audio_context?console.log("skipping audio context creation, context already exists"):(_=new AudioContext,window.ff_audio_context=_)};return{initialize:()=>{try{V(),d.initialize(),d.register({onReplaceTrack:W});const e=u.initialize();u.register({logChannelArgs:!1,peerMessages:[{event:"datachannel",callback:I},{event:"track",callback:z}],channelListeners:[{label:"captions",callback:D,monitor:M}]});const t=document.createElement("meta");t.setAttribute("id","ff-proxy-check"),t.setAttribute("name","hasCreatedProxies"),t.setAttribute("content",String(e)),(document.head||document.documentElement).prepend(t),window.addEventListener("load",(()=>{console.log("Google::window load event"),S.notify(s.WEB_STENOGRAPHER_LOG,"Audio::window load event"),V()})),f.initialize(),f.register([{url:"https://meet.google.com/$rpc/google.rtc.meetings.v1.MeetingSpaceService/SyncMeetingSpaceCollections",callback:P},{url:"https://meet.google.com/$rpc/google.rtc.meetings.v1.MeetingMessageService/CreateMeetingMessage",callback:B},{url:"https://meet.google.com/$rpc/google.rtc.meetings.v1.MeetingSpaceService/ResolveMeetingSpace",callback:j}]),E.initialize(),E.register({methods:[{url:"https://clients6.google.com/calendar/v3/calendars",callback:()=>{console.log("received meeting meta data")},resp:e=>{try{k=JSON.parse(e)}catch(e){console.error(e)}}}]})}catch(e){const t=e.message+" "+e.stack.substring(0,1e3);S.notify(s.WEB_STENOGRAPHER_ERROR,"initializer failed with: "+t)}},startCaptionsService:L,stopCaptionsService:()=>{y=!0,b&&(b.close(),b=null)},startRecorder:(e,t="webrtc")=>{switch(console.log("stenographer: start recorder::"+t),t){case"webrtc":return F(e);case"html":return G(e)}},stopRecorder:()=>{g&&g.stop()},getMetadata:()=>k||A?{status:!0,data:k||A}:{status:!1,data:null}}}}).call(this)}).call(this,e("buffer").Buffer)},{"../components/notifier":2,"../constants/default":4,"../decoder/decoder":5,"../lib/utils":7,"../proxies":13,buffer:25}],10:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.MsTeams=void 0;const r=e("../components/notifier"),i=e("../constants/default"),o=e("../lib/helpers");n.MsTeams=(e,t)=>{const n=(0,r.DefaultNotifier)();let s=null,a=null,l=0;const c=[],u=new WeakMap,f=(e,n,r)=>{var i;const o=t.get(n);if(o){const s=e.querySelector(".ui-chat__message__author"),a=(null===(i=null==s?void 0:s.textContent)||void 0===i?void 0:i.trim())||null,l=Date.now(),c=Object.assign(Object.assign({},o),{caption:r,updatedAt:l,endTs:l,user:a?{id:a,name:a,fullName:a,image:null}:null});t.set(n,c)}},d=e=>{var n,r,i,o;for(const s of e)if("childList"===s.type){const e=Array.from(s.addedNodes);for(const i of e)if(i instanceof HTMLElement){const e=i.querySelector(".ui-chat__item__message div[id]"),o=null==e?void 0:e.id;if(o&&c.includes(o)){console.log("captionId refound");continue}c.push(o);const s=i.querySelector('[data-tid="closed-caption-message-content"]');if(s instanceof HTMLElement){const e=s.querySelector('[data-tid="closed-caption-text"]');if(e){const i=null===(n=e.textContent)||void 0===n?void 0:n.trim();if(i){const e=s.querySelector(".ui-chat__message__author"),n=(null===(r=null==e?void 0:e.textContent)||void 0===r?void 0:r.trim())||null,o=Date.now(),a={messageId:l++,receivedCaptionId:l,caption:i,sequence:o,firstReceiveTs:o,updatedAt:o,endTs:o,user:n?{id:n,name:n,fullName:n,image:null}:null};t.set(a.messageId,a),u.set(s,a.messageId)}}}}}else if("characterData"===s.type){const e=null===(i=s.target.parentElement)||void 0===i?void 0:i.closest('[data-tid="closed-caption-message-content"]');if(e instanceof HTMLElement){const t=u.get(e);if(void 0!==t){const n=e.querySelector('[data-tid="closed-caption-text"]'),r=null===(o=null==n?void 0:n.textContent)||void 0===o?void 0:o.trim();r&&f(e,t,r)}}}},p=()=>{var e,t;const r=document.querySelector('[data-tid="'+i.CAPTION_CONTAINER_ID[i.PLATFORM_TYPES.MS_TEAMS]+'"]')||(null===(t=null===(e=document.getElementsByTagName("iframe")[0])||void 0===e?void 0:e.contentWindow)||void 0===t?void 0:t.document.querySelector('[data-tid="'+i.CAPTION_CONTAINER_ID[i.PLATFORM_TYPES.MS_TEAMS]+'"]'));if(!r)return a&&(a.disconnect(),a=null),void n.notify(i.WEB_STENOGRAPHER_ERROR,"Captions container not found");a||(a=new MutationObserver((0,o.tryTo)(d,"handleCaptionChange")),a.observe(r,{childList:!0,subtree:!0,characterData:!0,characterDataOldValue:!0}),n.notify(i.WEB_STENOGRAPHER_LOG,"Caption observer attached successfully"))};return{initialize:()=>{var e;try{window.addEventListener("load",(()=>{n.notify(i.WEB_STENOGRAPHER_LOG,"MS Teams::window load event")})),n.notify(i.WEB_STENOGRAPHER_LOG,"MS Teams module initialized")}catch(t){const r=t.message+" "+(null===(e=t.stack)||void 0===e?void 0:e.substring(0,1e3));n.notify(i.WEB_STENOGRAPHER_ERROR,"MS Teams initializer failed with: "+r)}},startCaptionsService:()=>{s&&(clearInterval(s),s=null),s=setInterval((0,o.tryTo)(p,"attach to captions"),1e3),n.notify(i.WEB_STENOGRAPHER_LOG,"Caption service started")},stopCaptionsService:()=>{s&&(clearInterval(s),s=null),a&&(a.disconnect(),a=null),n.notify(i.WEB_STENOGRAPHER_LOG,"Caption service stopped")}}}},{"../components/notifier":2,"../constants/default":4,"../lib/helpers":6}],11:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Zoom=void 0;const r=e("../components/notifier"),i=e("../constants/default"),o=e("../lib/helpers");n.Zoom=(e,t)=>{const n=(0,r.DefaultNotifier)();let s=null,a=null;const l=new Map,c=new Map;let u=0,f=0;const d=e=>{var n;for(const r of e)if("characterData"===r.type&&"#text"===r.target.nodeName){const e=r.target,i=e.parentNode.parentElement.getAttribute("data-caption-container-id"),o=null===(n=r.oldValue)||void 0===n?void 0:n.trim();let s=null==e?void 0:e.textContent.trim();const{shouldAdd:a,oldValueEndsAt:d}=p(o,s);if(!a)return;if(d>0&&u<=1&&(s=s.slice(d+1)),s){const e=Date.now();let n=null;if(i&&c.has(i)){const e=c.get(i);n={id:i,name:e,fullName:e,image:null}}const r=l.get(i),o=null==r?void 0:r.currentCaptionId;if(console.log("Last Caption:",r),o&&u>1&&d>0){console.log("Multi speaker mode");const n=t.get(o);if(console.log(n),n)return n.caption=s,n.endTs=e,void t.set(o,n)}console.log("Single speaker mode");const a={messageId:f++,receivedCaptionId:f,caption:s,sequence:e,firstReceiveTs:e,updatedAt:e,endTs:e,user:n};t.set(a.messageId,a),l.set(i,Object.assign(Object.assign({},r),{currentCaptionId:a.messageId}))}}},p=(e,t,n=20,r=5)=>{const i=e.length<=n+r,o=i?e:e.slice(-n-r,-r),s=t.toLowerCase().indexOf(o.toLowerCase());if(-1===s)return console.log("Substring not found"),t.length>e.length?{shouldAdd:!1,oldValueEndsAt:0}:{shouldAdd:!0,oldValueEndsAt:0};return{shouldAdd:!0,oldValueEndsAt:i?s+o.length-1:s+o.length+r-1}},h=(e,t)=>{if(l.get(t))return;const n=new MutationObserver((0,o.tryTo)(d,"handleCaptionChange"));n.observe(e,{attributes:!0,subtree:!0,characterData:!0,characterDataOldValue:!0}),l.set(t,{observer:n,currentCaption:""}),console.log("Caption containers attached")},g=()=>{var e,t;console.log("Reached to setup parent observer");const n=document.getElementsByClassName(i.ZOOM_PARENT_CAPTION_CONTAINER)[0]||(null===(t=null===(e=document.getElementsByTagName("iframe")[0])||void 0===e?void 0:e.contentWindow)||void 0===t?void 0:t.document.getElementsByClassName(i.ZOOM_PARENT_CAPTION_CONTAINER)[0]);if(!n)return void console.log("Parent element not found");console.log("Parent Observer Found"),s=new MutationObserver(y),s.observe(n,{childList:!0,subtree:!1}),console.log("Parent observer attached");const r=n.children;u=r.length,console.log("concurrentSpeakers:",u);for(const e of r){const t=m(e);t&&(e.setAttribute("data-caption-container-id",t),h(e,t))}n.style.display="none"},m=e=>{var t;if(console.log(e),!e.children[0])return null;const n=e.getAttribute("data-caption-container-id");if(n)return n;const r=null!==(t=e.children[0].getAttribute("src"))&&void 0!==t?t:e.children[0].style.backgroundColor+e.children[0].innerText;if(!r)return null;return btoa(r).slice(0,13)},y=e=>{for(const t of e)"childList"===t.type&&(u=u+t.addedNodes.length-t.removedNodes.length,t.removedNodes.forEach((e=>{if(e.nodeType===Node.ELEMENT_NODE){const t=e.getAttribute("data-caption-container-id");if(t&&l.has(t)){const e=l.get(t);e&&(e.observer.disconnect(),l.delete(t),console.log("Caption container removed"))}}})),t.addedNodes.forEach((e=>{if(e.nodeType===Node.ELEMENT_NODE){const t=e,n=m(t);n&&(t.setAttribute("data-caption-container-id",n),h(t,n))}})))},b=()=>{const e=document.evaluate(i.ZOOM_PARTICPANTS_BUTTON,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;console.log("Element found:",e),e instanceof HTMLElement?(e.click(),console.log("Element clicked successfully!")):console.error("Error: Element not found with XPath:",i.ZOOM_PARTICPANTS_BUTTON)},w=()=>{const e=document.evaluate(i.ZOOM_POP_OUT_BUTTON,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;e instanceof HTMLElement?(e.click(),console.log("Element clicked successfully!")):console.error("Error: Element not found with XPath:",i.ZOOM_POP_OUT_BUTTON)},_=()=>{var e,t;const n=document.getElementById(i.ZOOM_PARTICIPANT_LIST)||(null===(t=null===(e=document.getElementsByTagName("iframe")[0])||void 0===e?void 0:e.contentWindow)||void 0===t?void 0:t.document.getElementById(i.ZOOM_PARTICIPANT_LIST));if(!n)return void console.log("Participant Panel not found");const r=n.children[0];a=new MutationObserver(v),a.observe(r,{childList:!0,subtree:!1});for(const e of r.children)O(e.children[0].children[0].children[0].children[0]);(()=>{var e,t;(document.getElementById(i.ZOOM_PARTICIPANT_WINDOW)||(null===(t=null===(e=document.getElementsByTagName("iframe")[0])||void 0===e?void 0:e.contentWindow)||void 0===t?void 0:t.document.getElementById(i.ZOOM_PARTICIPANT_WINDOW))).style.display="none"})()},v=e=>{for(const t of e)"childList"===t.type&&t.addedNodes.forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&O(e.children[0].children[0].children[0].children[0])}))},O=e=>{const t=m(e);if(t&&(e.setAttribute("data-caption-container-id",t),!c.has(t))){const n=e.children[1].textContent.split("(")[0].trim();console.log("Speaker:",n),c.set(t,n)}};return{initialize:()=>{var e;try{window.addEventListener("load",(()=>{n.notify(i.WEB_STENOGRAPHER_LOG,"Zoom::window load event")})),n.notify(i.WEB_STENOGRAPHER_LOG,"Zoom module initialized")}catch(t){const r=t.message+" "+(null===(e=t.stack)||void 0===e?void 0:e.substring(0,1e3));n.notify(i.WEB_STENOGRAPHER_ERROR,"Zoom initializer failed with: "+r)}},startCaptionsService:()=>{console.log("Starting caption service"),setTimeout((()=>{g(),b(),setTimeout(w,500),setTimeout(_,1500),n.notify(i.WEB_STENOGRAPHER_LOG,"Caption service started")}),500)},stopCaptionsService:()=>{s&&(s.disconnect(),s=null),l.forEach((e=>{e.observer.disconnect()})),l.clear(),a&&(a.disconnect(),a=null),n.notify(i.WEB_STENOGRAPHER_LOG,"Caption service stopped")}}}},{"../components/notifier":2,"../constants/default":4,"../lib/helpers":6}],12:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.FetchProxy=void 0,n.FetchProxy=function(e){let t=[];return{initialize:()=>{if(!window.fetch)return!1;e&&e.debug&&console.log("fetch initialized");const n=window.fetch;return window.fetch=function(...e){return new Promise(((r,i)=>{n.apply(this,e).then((e=>{for(const n of t)if(e.url===n.url)try{const t=e.clone();n.callback(t)}catch(e){console.error("failed calling proxy for fetch with error",e)}r(e)})).catch((e=>{i(e)}))}))},!0},register:e=>{t=[...t,...e]}}}},{}],13:[function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(n,"__esModule",{value:!0}),i(e("./fetch-proxy"),n),i(e("./xhr-proxy"),n),i(e("./rtc-proxy"),n),i(e("./rtc-sender-proxy"),n)},{"./fetch-proxy":12,"./rtc-proxy":14,"./rtc-sender-proxy":15,"./xhr-proxy":16}],14:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.WebRtcProxy=void 0,n.WebRtcProxy=function(e){let t={peerMessages:[],logChannelArgs:!1,channelListeners:[]};return{initialize:()=>{if(!window.RTCPeerConnection)return!1;window.ff_channels||(window.ff_channels={});const n=window.RTCPeerConnection,r=n.prototype.createDataChannel;return r&&(n.prototype.createDataChannel=function(){t.logChannelArgs&&console.log("creating channel args",arguments);try{const e=r.apply(this,arguments);if(e&&t.channelListeners.length>0){const n=t.channelListeners.find((t=>t.label===e.label));n&&(e.addEventListener("message",n.callback),n.monitor&&n.monitor(e)),window.ff_channels[e.label]=e}return e}catch(e){console.log(e)}}),window.RTCPeerConnection=function(r,i){const o=new n(r,i);e&&e.debug&&console.log("created peer connection",o);for(const e of t.peerMessages)o.addEventListener(e.event,(t=>{e.callback(o,t)}));return o},window.RTCPeerConnection.prototype=n.prototype,!0},register:e=>{t={peerMessages:[...t.peerMessages,...e.peerMessages],logChannelArgs:e.logChannelArgs,channelListeners:[...t.channelListeners,...e.channelListeners]}}}}},{}],15:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RtcSenderProxy=void 0,n.RtcSenderProxy=function(e){let t=null;return{initialize:()=>{if(!window.RTCRtpSender)return!1;const n=window.RTCRtpSender.prototype.replaceTrack;return window.RTCRtpSender.prototype.replaceTrack=function(r){return e&&e.debug&&console.log("replacing track",r),t&&t.onReplaceTrack&&t.onReplaceTrack(r),n.apply(this,arguments)},!0},register:e=>{t=e}}}},{}],16:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.XhrProxy=void 0,n.XhrProxy=function(e){let t={methods:[]};return{initialize:()=>{if(!window.XMLHttpRequest)return!1;const e=window.XMLHttpRequest.prototype.open,n=window.XMLHttpRequest.prototype.send;return window.XMLHttpRequest.prototype.open=function(n,r,...i){for(const e of t.methods)if(r.startsWith(e.url)){this.__currentURL=r,this.__proxy_callback=e.callback,this.__resp_cb=e.resp;break}e.apply(this,[n,r,...i])},window.XMLHttpRequest.prototype.send=function(e,...t){if(this.__currentURL)try{this.__proxy_callback(e)}catch(e){console.error(e)}n.apply(this,[e,...t]),this.addEventListener("readystatechange",(()=>{this.__currentURL&&this.__resp_cb&&this.readyState==XMLHttpRequest.DONE&&this.__resp_cb(this.responseText)}))},!0},register:e=>{t={methods:[...t.methods,...e.methods]}}}}},{}],17:[function(e,t,n){"use strict";t.exports=function(e,t){var n=new Array(arguments.length-1),r=0,i=2,o=!0;for(;i<arguments.length;)n[r++]=arguments[i++];return new Promise((function(i,s){n[r]=function(e){if(o)if(o=!1,e)s(e);else{for(var t=new Array(arguments.length-1),n=0;n<t.length;)t[n++]=arguments[n];i.apply(null,t)}};try{e.apply(t||null,n)}catch(e){o&&(o=!1,s(e))}}))}},{}],18:[function(e,t,n){"use strict";var r=n;r.length=function(e){var t=e.length;if(!t)return 0;for(var n=0;--t%4>1&&"="===e.charAt(t);)++n;return Math.ceil(3*e.length)/4-n};for(var i=new Array(64),o=new Array(123),s=0;s<64;)o[i[s]=s<26?s+65:s<52?s+71:s<62?s-4:s-59|43]=s++;r.encode=function(e,t,n){for(var r,o=null,s=[],a=0,l=0;t<n;){var c=e[t++];switch(l){case 0:s[a++]=i[c>>2],r=(3&c)<<4,l=1;break;case 1:s[a++]=i[r|c>>4],r=(15&c)<<2,l=2;break;case 2:s[a++]=i[r|c>>6],s[a++]=i[63&c],l=0}a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,s)),a=0)}return l&&(s[a++]=i[r],s[a++]=61,1===l&&(s[a++]=61)),o?(a&&o.push(String.fromCharCode.apply(String,s.slice(0,a))),o.join("")):String.fromCharCode.apply(String,s.slice(0,a))};var a="invalid encoding";r.decode=function(e,t,n){for(var r,i=n,s=0,l=0;l<e.length;){var c=e.charCodeAt(l++);if(61===c&&s>1)break;if(void 0===(c=o[c]))throw Error(a);switch(s){case 0:r=c,s=1;break;case 1:t[n++]=r<<2|(48&c)>>4,r=c,s=2;break;case 2:t[n++]=(15&r)<<4|(60&c)>>2,r=c,s=3;break;case 3:t[n++]=(3&r)<<6|c,s=0}}if(1===s)throw Error(a);return n-i},r.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}},{}],19:[function(e,t,n){"use strict";function r(){this._listeners={}}t.exports=r,r.prototype.on=function(e,t,n){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:n||this}),this},r.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var n=this._listeners[e],r=0;r<n.length;)n[r].fn===t?n.splice(r,1):++r;return this},r.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var n=[],r=1;r<arguments.length;)n.push(arguments[r++]);for(r=0;r<t.length;)t[r].fn.apply(t[r++].ctx,n)}return this}},{}],20:[function(e,t,n){"use strict";function r(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),n=new Uint8Array(t.buffer),r=128===n[3];function i(e,r,i){t[0]=e,r[i]=n[0],r[i+1]=n[1],r[i+2]=n[2],r[i+3]=n[3]}function o(e,r,i){t[0]=e,r[i]=n[3],r[i+1]=n[2],r[i+2]=n[1],r[i+3]=n[0]}function s(e,r){return n[0]=e[r],n[1]=e[r+1],n[2]=e[r+2],n[3]=e[r+3],t[0]}function a(e,r){return n[3]=e[r],n[2]=e[r+1],n[1]=e[r+2],n[0]=e[r+3],t[0]}e.writeFloatLE=r?i:o,e.writeFloatBE=r?o:i,e.readFloatLE=r?s:a,e.readFloatBE=r?a:s}():function(){function t(e,t,n,r){var i=t<0?1:0;if(i&&(t=-t),0===t)e(1/t>0?0:2147483648,n,r);else if(isNaN(t))e(2143289344,n,r);else if(t>34028234663852886e22)e((i<<31|2139095040)>>>0,n,r);else if(t<11754943508222875e-54)e((i<<31|Math.round(t/1401298464324817e-60))>>>0,n,r);else{var o=Math.floor(Math.log(t)/Math.LN2);e((i<<31|o+127<<23|8388607&Math.round(t*Math.pow(2,-o)*8388608))>>>0,n,r)}}function n(e,t,n){var r=e(t,n),i=2*(r>>31)+1,o=r>>>23&255,s=8388607&r;return 255===o?s?NaN:i*(1/0):0===o?1401298464324817e-60*i*s:i*Math.pow(2,o-150)*(s+8388608)}e.writeFloatLE=t.bind(null,i),e.writeFloatBE=t.bind(null,o),e.readFloatLE=n.bind(null,s),e.readFloatBE=n.bind(null,a)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),n=new Uint8Array(t.buffer),r=128===n[7];function i(e,r,i){t[0]=e,r[i]=n[0],r[i+1]=n[1],r[i+2]=n[2],r[i+3]=n[3],r[i+4]=n[4],r[i+5]=n[5],r[i+6]=n[6],r[i+7]=n[7]}function o(e,r,i){t[0]=e,r[i]=n[7],r[i+1]=n[6],r[i+2]=n[5],r[i+3]=n[4],r[i+4]=n[3],r[i+5]=n[2],r[i+6]=n[1],r[i+7]=n[0]}function s(e,r){return n[0]=e[r],n[1]=e[r+1],n[2]=e[r+2],n[3]=e[r+3],n[4]=e[r+4],n[5]=e[r+5],n[6]=e[r+6],n[7]=e[r+7],t[0]}function a(e,r){return n[7]=e[r],n[6]=e[r+1],n[5]=e[r+2],n[4]=e[r+3],n[3]=e[r+4],n[2]=e[r+5],n[1]=e[r+6],n[0]=e[r+7],t[0]}e.writeDoubleLE=r?i:o,e.writeDoubleBE=r?o:i,e.readDoubleLE=r?s:a,e.readDoubleBE=r?a:s}():function(){function t(e,t,n,r,i,o){var s=r<0?1:0;if(s&&(r=-r),0===r)e(0,i,o+t),e(1/r>0?0:2147483648,i,o+n);else if(isNaN(r))e(0,i,o+t),e(2146959360,i,o+n);else if(r>17976931348623157e292)e(0,i,o+t),e((s<<31|2146435072)>>>0,i,o+n);else{var a;if(r<22250738585072014e-324)e((a=r/5e-324)>>>0,i,o+t),e((s<<31|a/4294967296)>>>0,i,o+n);else{var l=Math.floor(Math.log(r)/Math.LN2);1024===l&&(l=1023),e(4503599627370496*(a=r*Math.pow(2,-l))>>>0,i,o+t),e((s<<31|l+1023<<20|1048576*a&1048575)>>>0,i,o+n)}}}function n(e,t,n,r,i){var o=e(r,i+t),s=e(r,i+n),a=2*(s>>31)+1,l=s>>>20&2047,c=4294967296*(1048575&s)+o;return 2047===l?c?NaN:a*(1/0):0===l?5e-324*a*c:a*Math.pow(2,l-1075)*(c+4503599627370496)}e.writeDoubleLE=t.bind(null,i,0,4),e.writeDoubleBE=t.bind(null,o,4,0),e.readDoubleLE=n.bind(null,s,0,4),e.readDoubleBE=n.bind(null,a,4,0)}(),e}function i(e,t,n){t[n]=255&e,t[n+1]=e>>>8&255,t[n+2]=e>>>16&255,t[n+3]=e>>>24}function o(e,t,n){t[n]=e>>>24,t[n+1]=e>>>16&255,t[n+2]=e>>>8&255,t[n+3]=255&e}function s(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function a(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}t.exports=r(r)},{}],21:[function(require,module,exports){"use strict";function inquire(moduleName){try{var mod=require(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(e){}return null}module.exports=inquire},{}],22:[function(e,t,n){"use strict";t.exports=function(e,t,n){var r=n||8192,i=r>>>1,o=null,s=r;return function(n){if(n<1||n>i)return e(n);s+n>r&&(o=e(r),s=0);var a=t.call(o,s,s+=n);return 7&s&&(s=1+(7|s)),a}}},{}],23:[function(e,t,n){"use strict";var r=n;r.length=function(e){for(var t=0,n=0,r=0;r<e.length;++r)(n=e.charCodeAt(r))<128?t+=1:n<2048?t+=2:55296==(64512&n)&&56320==(64512&e.charCodeAt(r+1))?(++r,t+=4):t+=3;return t},r.read=function(e,t,n){if(n-t<1)return"";for(var r,i=null,o=[],s=0;t<n;)(r=e[t++])<128?o[s++]=r:r>191&&r<224?o[s++]=(31&r)<<6|63&e[t++]:r>239&&r<365?(r=((7&r)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,o[s++]=55296+(r>>10),o[s++]=56320+(1023&r)):o[s++]=(15&r)<<12|(63&e[t++])<<6|63&e[t++],s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,o)),s=0);return i?(s&&i.push(String.fromCharCode.apply(String,o.slice(0,s))),i.join("")):String.fromCharCode.apply(String,o.slice(0,s))},r.write=function(e,t,n){for(var r,i,o=n,s=0;s<e.length;++s)(r=e.charCodeAt(s))<128?t[n++]=r:r<2048?(t[n++]=r>>6|192,t[n++]=63&r|128):55296==(64512&r)&&56320==(64512&(i=e.charCodeAt(s+1)))?(r=65536+((1023&r)<<10)+(1023&i),++s,t[n++]=r>>18|240,t[n++]=r>>12&63|128,t[n++]=r>>6&63|128,t[n++]=63&r|128):(t[n++]=r>>12|224,t[n++]=r>>6&63|128,t[n++]=63&r|128);return n-o}},{}],24:[function(e,t,n){"use strict";n.byteLength=function(e){var t=c(e),n=t[0],r=t[1];return 3*(n+r)/4-r},n.toByteArray=function(e){var t,n,r=c(e),s=r[0],a=r[1],l=new o(function(e,t,n){return 3*(t+n)/4-n}(0,s,a)),u=0,f=a>0?s-4:s;for(n=0;n<f;n+=4)t=i[e.charCodeAt(n)]<<18|i[e.charCodeAt(n+1)]<<12|i[e.charCodeAt(n+2)]<<6|i[e.charCodeAt(n+3)],l[u++]=t>>16&255,l[u++]=t>>8&255,l[u++]=255&t;2===a&&(t=i[e.charCodeAt(n)]<<2|i[e.charCodeAt(n+1)]>>4,l[u++]=255&t);1===a&&(t=i[e.charCodeAt(n)]<<10|i[e.charCodeAt(n+1)]<<4|i[e.charCodeAt(n+2)]>>2,l[u++]=t>>8&255,l[u++]=255&t);return l},n.fromByteArray=function(e){for(var t,n=e.length,i=n%3,o=[],s=16383,a=0,l=n-i;a<l;a+=s)o.push(u(e,a,a+s>l?l:a+s));1===i?(t=e[n-1],o.push(r[t>>2]+r[t<<4&63]+"==")):2===i&&(t=(e[n-2]<<8)+e[n-1],o.push(r[t>>10]+r[t>>4&63]+r[t<<2&63]+"="));return o.join("")};for(var r=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,l=s.length;a<l;++a)r[a]=s[a],i[s.charCodeAt(a)]=a;function c(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function u(e,t,n){for(var i,o,s=[],a=t;a<n;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},{}],25:[function(e,t,n){(function(t){(function(){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
"use strict";var t=e("base64-js"),r=e("ieee754");n.Buffer=s,n.SlowBuffer=function(e){+e!=e&&(e=0);return s.alloc(+e)},n.INSPECT_MAX_BYTES=50;var i=**********;function o(e){if(e>i)throw new RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return t.__proto__=s.prototype,t}function s(e,t,n){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return c(e)}return a(e,t,n)}function a(e,t,n){if("string"==typeof e)return function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!s.isEncoding(t))throw new TypeError("Unknown encoding: "+t);var n=0|d(e,t),r=o(n),i=r.write(e,t);i!==n&&(r=r.slice(0,i));return r}(e,t);if(ArrayBuffer.isView(e))return u(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(z(e,ArrayBuffer)||e&&z(e.buffer,ArrayBuffer))return function(e,t,n){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(n||0))throw new RangeError('"length" is outside of buffer bounds');var r;r=void 0===t&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,t):new Uint8Array(e,t,n);return r.__proto__=s.prototype,r}(e,t,n);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');var r=e.valueOf&&e.valueOf();if(null!=r&&r!==e)return s.from(r,t,n);var i=function(e){if(s.isBuffer(e)){var t=0|f(e.length),n=o(t);return 0===n.length||e.copy(n,0,0,t),n}if(void 0!==e.length)return"number"!=typeof e.length||W(e.length)?o(0):u(e);if("Buffer"===e.type&&Array.isArray(e.data))return u(e.data)}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return s.from(e[Symbol.toPrimitive]("string"),t,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return l(e),o(e<0?0:0|f(e))}function u(e){for(var t=e.length<0?0:0|f(e.length),n=o(t),r=0;r<t;r+=1)n[r]=255&e[r];return n}function f(e){if(e>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|e}function d(e,t){if(s.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||z(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var n=e.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return M(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return L(e).length;default:if(i)return r?-1:M(e).length;t=(""+t).toLowerCase(),i=!0}}function p(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return T(this,t,n);case"utf8":case"utf-8":return k(this,t,n);case"ascii":return S(this,t,n);case"latin1":case"binary":return x(this,t,n);case"base64":return E(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function h(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function g(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),W(n=+n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=s.from(t,r)),s.isBuffer(t))return 0===t.length?-1:m(e,t,n,r,i);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):m(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function m(e,t,n,r,i){var o,s=1,a=e.length,l=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;s=2,a/=2,l/=2,n/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var u=-1;for(o=n;o<a;o++)if(c(e,o)===c(t,-1===u?0:o-u)){if(-1===u&&(u=o),o-u+1===l)return u*s}else-1!==u&&(o-=o-u),u=-1}else for(n+l>a&&(n=a-l),o=n;o>=0;o--){for(var f=!0,d=0;d<l;d++)if(c(e,o+d)!==c(t,d)){f=!1;break}if(f)return o}return-1}function y(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r))>i&&(r=i):r=i;var o=t.length;r>o/2&&(r=o/2);for(var s=0;s<r;++s){var a=parseInt(t.substr(2*s,2),16);if(W(a))return s;e[n+s]=a}return s}function b(e,t,n,r){return U(M(t,e.length-n),e,n,r)}function w(e,t,n,r){return U(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function _(e,t,n,r){return w(e,t,n,r)}function v(e,t,n,r){return U(L(t),e,n,r)}function O(e,t,n,r){return U(function(e,t){for(var n,r,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)r=(n=e.charCodeAt(s))>>8,i=n%256,o.push(i),o.push(r);return o}(t,e.length-n),e,n,r)}function E(e,n,r){return 0===n&&r===e.length?t.fromByteArray(e):t.fromByteArray(e.slice(n,r))}function k(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o,s,a,l,c=e[i],u=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=n)switch(f){case 1:c<128&&(u=c);break;case 2:128==(192&(o=e[i+1]))&&(l=(31&c)<<6|63&o)>127&&(u=l);break;case 3:o=e[i+1],s=e[i+2],128==(192&o)&&128==(192&s)&&(l=(15&c)<<12|(63&o)<<6|63&s)>2047&&(l<55296||l>57343)&&(u=l);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(l=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(u=l)}null===u?(u=65533,f=1):u>65535&&(u-=65536,r.push(u>>>10&1023|55296),u=56320|1023&u),r.push(u),i+=f}return function(e){var t=e.length;if(t<=A)return String.fromCharCode.apply(String,e);var n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=A));return n}(r)}n.kMaxLength=i,s.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(e){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by 'buffer' v5.x. Use 'buffer' v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),s.poolSize=8192,s.from=function(e,t,n){return a(e,t,n)},s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,s.alloc=function(e,t,n){return function(e,t,n){return l(e),e<=0?o(e):void 0!==t?"string"==typeof n?o(e).fill(t,n):o(e).fill(t):o(e)}(e,t,n)},s.allocUnsafe=function(e){return c(e)},s.allocUnsafeSlow=function(e){return c(e)},s.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==s.prototype},s.compare=function(e,t){if(z(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),z(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(e)||!s.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var n=e.length,r=t.length,i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=s.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var o=e[n];if(z(o,Uint8Array)&&(o=s.from(o)),!s.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},s.byteLength=d,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)h(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)h(this,t,t+3),h(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)h(this,t,t+7),h(this,t+1,t+6),h(this,t+2,t+5),h(this,t+3,t+4);return this},s.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?k(this,0,e):p.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(e){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",t=n.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},s.prototype.compare=function(e,t,n,r,i){if(z(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(r>>>=0),a=(n>>>=0)-(t>>>=0),l=Math.min(o,a),c=this.slice(r,i),u=e.slice(t,n),f=0;f<l;++f)if(c[f]!==u[f]){o=c[f],a=u[f];break}return o<a?-1:a<o?1:0},s.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},s.prototype.indexOf=function(e,t,n){return g(this,e,t,n,!0)},s.prototype.lastIndexOf=function(e,t,n){return g(this,e,t,n,!1)},s.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return y(this,e,t,n);case"utf8":case"utf-8":return b(this,e,t,n);case"ascii":return w(this,e,t,n);case"latin1":case"binary":return _(this,e,t,n);case"base64":return v(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return O(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var A=4096;function S(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function x(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function T(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=j(e[o]);return i}function I(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function R(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function C(e,t,n,r,i,o){if(!s.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function N(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function D(e,t,n,i,o){return t=+t,n>>>=0,o||N(e,0,n,4),r.write(e,t,n,i,23,4),n+4}function P(e,t,n,i,o){return t=+t,n>>>=0,o||N(e,0,n,8),r.write(e,t,n,i,52,8),n+8}s.prototype.slice=function(e,t){var n=this.length;(e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);var r=this.subarray(e,t);return r.__proto__=s.prototype,r},s.prototype.readUIntLE=function(e,t,n){e>>>=0,t>>>=0,n||R(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},s.prototype.readUIntBE=function(e,t,n){e>>>=0,t>>>=0,n||R(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},s.prototype.readUInt8=function(e,t){return e>>>=0,t||R(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return e>>>=0,t||R(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return e>>>=0,t||R(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return e>>>=0,t||R(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},s.prototype.readUInt32BE=function(e,t){return e>>>=0,t||R(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,n){e>>>=0,t>>>=0,n||R(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*t)),r},s.prototype.readIntBE=function(e,t,n){e>>>=0,t>>>=0,n||R(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},s.prototype.readInt8=function(e,t){return e>>>=0,t||R(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},s.prototype.readInt16LE=function(e,t){e>>>=0,t||R(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt16BE=function(e,t){e>>>=0,t||R(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt32LE=function(e,t){return e>>>=0,t||R(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return e>>>=0,t||R(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return e>>>=0,t||R(e,4,this.length),r.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return e>>>=0,t||R(e,4,this.length),r.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return e>>>=0,t||R(e,8,this.length),r.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return e>>>=0,t||R(e,8,this.length),r.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,n,r){(e=+e,t>>>=0,n>>>=0,r)||C(this,e,t,n,Math.pow(2,8*n)-1,0);var i=1,o=0;for(this[t]=255&e;++o<n&&(i*=256);)this[t+o]=e/i&255;return t+n},s.prototype.writeUIntBE=function(e,t,n,r){(e=+e,t>>>=0,n>>>=0,r)||C(this,e,t,n,Math.pow(2,8*n)-1,0);var i=n-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+n},s.prototype.writeUInt8=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,1,255,0),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeUInt16BE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeUInt32LE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},s.prototype.writeUInt32BE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t>>>=0,!r){var i=Math.pow(2,8*n-1);C(this,e,t,n,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<n&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+n},s.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t>>>=0,!r){var i=Math.pow(2,8*n-1);C(this,e,t,n,i-1,-i)}var o=n-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+n},s.prototype.writeInt8=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeInt16BE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeInt32LE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,4,**********,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},s.prototype.writeInt32BE=function(e,t,n){return e=+e,t>>>=0,n||C(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeFloatLE=function(e,t,n){return D(this,e,t,!0,n)},s.prototype.writeFloatBE=function(e,t,n){return D(this,e,t,!1,n)},s.prototype.writeDoubleLE=function(e,t,n){return P(this,e,t,!0,n)},s.prototype.writeDoubleBE=function(e,t,n){return P(this,e,t,!1,n)},s.prototype.copy=function(e,t,n,r){if(!s.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i=r-n;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,n,r);else if(this===e&&n<t&&t<r)for(var o=i-1;o>=0;--o)e[o+t]=this[o+n];else Uint8Array.prototype.set.call(e,this.subarray(n,r),t);return i},s.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(1===e.length){var i=e.charCodeAt(0);("utf8"===r&&i<128||"latin1"===r)&&(e=i)}}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var o;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=s.isBuffer(e)?e:s.from(e,r),l=a.length;if(0===l)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(o=0;o<n-t;++o)this[o+t]=a[o%l]}return this};var B=/[^+/0-9A-Za-z-_]/g;function j(e){return e<16?"0"+e.toString(16):e.toString(16)}function M(e,t){var n;t=t||1/0;for(var r=e.length,i=null,o=[],s=0;s<r;++s){if((n=e.charCodeAt(s))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function L(e){return t.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(B,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function U(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function z(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function W(e){return e!=e}}).call(this)}).call(this,e("buffer").Buffer)},{"base64-js":24,buffer:25,ieee754:26}],26:[function(e,t,n){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
n.read=function(e,t,n,r,i){var o,s,a=8*i-r-1,l=(1<<a)-1,c=l>>1,u=-7,f=n?i-1:0,d=n?-1:1,p=e[t+f];for(f+=d,o=p&(1<<-u)-1,p>>=-u,u+=a;u>0;o=256*o+e[t+f],f+=d,u-=8);for(s=o&(1<<-u)-1,o>>=-u,u+=r;u>0;s=256*s+e[t+f],f+=d,u-=8);if(0===o)o=1-c;else{if(o===l)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,r),o-=c}return(p?-1:1)*s*Math.pow(2,o-r)},n.write=function(e,t,n,r,i,o){var s,a,l,c=8*o-i-1,u=(1<<c)-1,f=u>>1,d=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:o-1,h=r?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=u):(s=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-s))<1&&(s--,l*=2),(t+=s+f>=1?d/l:d*Math.pow(2,1-f))*l>=2&&(s++,l/=2),s+f>=u?(a=0,s=u):s+f>=1?(a=(t*l-1)*Math.pow(2,i),s+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;e[n+p]=255&a,p+=h,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[n+p]=255&s,p+=h,s/=256,c-=8);e[n+p-h]|=128*g}},{}],27:[function(e,t,n){"use strict";const{Deflate:r,deflate:i,deflateRaw:o,gzip:s}=e("./lib/deflate"),{Inflate:a,inflate:l,inflateRaw:c,ungzip:u}=e("./lib/inflate"),f=e("./lib/zlib/constants");t.exports.Deflate=r,t.exports.deflate=i,t.exports.deflateRaw=o,t.exports.gzip=s,t.exports.Inflate=a,t.exports.inflate=l,t.exports.inflateRaw=c,t.exports.ungzip=u,t.exports.constants=f},{"./lib/deflate":28,"./lib/inflate":29,"./lib/zlib/constants":33}],28:[function(e,t,n){"use strict";const r=e("./zlib/deflate"),i=e("./utils/common"),o=e("./utils/strings"),s=e("./zlib/messages"),a=e("./zlib/zstream"),l=Object.prototype.toString,{Z_NO_FLUSH:c,Z_SYNC_FLUSH:u,Z_FULL_FLUSH:f,Z_FINISH:d,Z_OK:p,Z_STREAM_END:h,Z_DEFAULT_COMPRESSION:g,Z_DEFAULT_STRATEGY:m,Z_DEFLATED:y}=e("./zlib/constants");function b(e){this.options=i.assign({level:g,method:y,chunkSize:16384,windowBits:15,memLevel:8,strategy:m},e||{});let t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0;let n=r.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==p)throw new Error(s[n]);if(t.header&&r.deflateSetHeader(this.strm,t.header),t.dictionary){let e;if(e="string"==typeof t.dictionary?o.string2buf(t.dictionary):"[object ArrayBuffer]"===l.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,n=r.deflateSetDictionary(this.strm,e),n!==p)throw new Error(s[n]);this._dict_set=!0}}function w(e,t){const n=new b(t);if(n.push(e,!0),n.err)throw n.msg||s[n.err];return n.result}b.prototype.push=function(e,t){const n=this.strm,i=this.options.chunkSize;let s,a;if(this.ended)return!1;for(a=t===~~t?t:!0===t?d:c,"string"==typeof e?n.input=o.string2buf(e):"[object ArrayBuffer]"===l.call(e)?n.input=new Uint8Array(e):n.input=e,n.next_in=0,n.avail_in=n.input.length;;)if(0===n.avail_out&&(n.output=new Uint8Array(i),n.next_out=0,n.avail_out=i),(a===u||a===f)&&n.avail_out<=6)this.onData(n.output.subarray(0,n.next_out)),n.avail_out=0;else{if(s=r.deflate(n,a),s===h)return n.next_out>0&&this.onData(n.output.subarray(0,n.next_out)),s=r.deflateEnd(this.strm),this.onEnd(s),this.ended=!0,s===p;if(0!==n.avail_out){if(a>0&&n.next_out>0)this.onData(n.output.subarray(0,n.next_out)),n.avail_out=0;else if(0===n.avail_in)break}else this.onData(n.output)}return!0},b.prototype.onData=function(e){this.chunks.push(e)},b.prototype.onEnd=function(e){e===p&&(this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},t.exports.Deflate=b,t.exports.deflate=w,t.exports.deflateRaw=function(e,t){return(t=t||{}).raw=!0,w(e,t)},t.exports.gzip=function(e,t){return(t=t||{}).gzip=!0,w(e,t)},t.exports.constants=e("./zlib/constants")},{"./utils/common":30,"./utils/strings":31,"./zlib/constants":33,"./zlib/deflate":35,"./zlib/messages":40,"./zlib/zstream":42}],29:[function(e,t,n){"use strict";const r=e("./zlib/inflate"),i=e("./utils/common"),o=e("./utils/strings"),s=e("./zlib/messages"),a=e("./zlib/zstream"),l=e("./zlib/gzheader"),c=Object.prototype.toString,{Z_NO_FLUSH:u,Z_FINISH:f,Z_OK:d,Z_STREAM_END:p,Z_NEED_DICT:h,Z_STREAM_ERROR:g,Z_DATA_ERROR:m,Z_MEM_ERROR:y}=e("./zlib/constants");function b(e){this.options=i.assign({chunkSize:65536,windowBits:15,to:""},e||{});const t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0;let n=r.inflateInit2(this.strm,t.windowBits);if(n!==d)throw new Error(s[n]);if(this.header=new l,r.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=o.string2buf(t.dictionary):"[object ArrayBuffer]"===c.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(n=r.inflateSetDictionary(this.strm,t.dictionary),n!==d)))throw new Error(s[n])}function w(e,t){const n=new b(t);if(n.push(e),n.err)throw n.msg||s[n.err];return n.result}b.prototype.push=function(e,t){const n=this.strm,i=this.options.chunkSize,s=this.options.dictionary;let a,l,b;if(this.ended)return!1;for(l=t===~~t?t:!0===t?f:u,"[object ArrayBuffer]"===c.call(e)?n.input=new Uint8Array(e):n.input=e,n.next_in=0,n.avail_in=n.input.length;;){for(0===n.avail_out&&(n.output=new Uint8Array(i),n.next_out=0,n.avail_out=i),a=r.inflate(n,l),a===h&&s&&(a=r.inflateSetDictionary(n,s),a===d?a=r.inflate(n,l):a===m&&(a=h));n.avail_in>0&&a===p&&n.state.wrap>0&&0!==e[n.next_in];)r.inflateReset(n),a=r.inflate(n,l);switch(a){case g:case m:case h:case y:return this.onEnd(a),this.ended=!0,!1}if(b=n.avail_out,n.next_out&&(0===n.avail_out||a===p))if("string"===this.options.to){let e=o.utf8border(n.output,n.next_out),t=n.next_out-e,r=o.buf2string(n.output,e);n.next_out=t,n.avail_out=i-t,t&&n.output.set(n.output.subarray(e,e+t),0),this.onData(r)}else this.onData(n.output.length===n.next_out?n.output:n.output.subarray(0,n.next_out));if(a!==d||0!==b){if(a===p)return a=r.inflateEnd(this.strm),this.onEnd(a),this.ended=!0,!0;if(0===n.avail_in)break}}return!0},b.prototype.onData=function(e){this.chunks.push(e)},b.prototype.onEnd=function(e){e===d&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},t.exports.Inflate=b,t.exports.inflate=w,t.exports.inflateRaw=function(e,t){return(t=t||{}).raw=!0,w(e,t)},t.exports.ungzip=w,t.exports.constants=e("./zlib/constants")},{"./utils/common":30,"./utils/strings":31,"./zlib/constants":33,"./zlib/gzheader":36,"./zlib/inflate":38,"./zlib/messages":40,"./zlib/zstream":42}],30:[function(e,t,n){"use strict";const r=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);t.exports.assign=function(e){const t=Array.prototype.slice.call(arguments,1);for(;t.length;){const n=t.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(const t in n)r(n,t)&&(e[t]=n[t])}}return e},t.exports.flattenChunks=e=>{let t=0;for(let n=0,r=e.length;n<r;n++)t+=e[n].length;const n=new Uint8Array(t);for(let t=0,r=0,i=e.length;t<i;t++){let i=e[t];n.set(i,r),r+=i.length}return n}},{}],31:[function(e,t,n){"use strict";let r=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){r=!1}const i=new Uint8Array(256);for(let e=0;e<256;e++)i[e]=e>=252?6:e>=248?5:e>=240?4:e>=224?3:e>=192?2:1;i[254]=i[254]=1,t.exports.string2buf=e=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(e);let t,n,r,i,o,s=e.length,a=0;for(i=0;i<s;i++)n=e.charCodeAt(i),55296==(64512&n)&&i+1<s&&(r=e.charCodeAt(i+1),56320==(64512&r)&&(n=65536+(n-55296<<10)+(r-56320),i++)),a+=n<128?1:n<2048?2:n<65536?3:4;for(t=new Uint8Array(a),o=0,i=0;o<a;i++)n=e.charCodeAt(i),55296==(64512&n)&&i+1<s&&(r=e.charCodeAt(i+1),56320==(64512&r)&&(n=65536+(n-55296<<10)+(r-56320),i++)),n<128?t[o++]=n:n<2048?(t[o++]=192|n>>>6,t[o++]=128|63&n):n<65536?(t[o++]=224|n>>>12,t[o++]=128|n>>>6&63,t[o++]=128|63&n):(t[o++]=240|n>>>18,t[o++]=128|n>>>12&63,t[o++]=128|n>>>6&63,t[o++]=128|63&n);return t};t.exports.buf2string=(e,t)=>{const n=t||e.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(e.subarray(0,t));let o,s;const a=new Array(2*n);for(s=0,o=0;o<n;){let t=e[o++];if(t<128){a[s++]=t;continue}let r=i[t];if(r>4)a[s++]=65533,o+=r-1;else{for(t&=2===r?31:3===r?15:7;r>1&&o<n;)t=t<<6|63&e[o++],r--;r>1?a[s++]=65533:t<65536?a[s++]=t:(t-=65536,a[s++]=55296|t>>10&1023,a[s++]=56320|1023&t)}}return((e,t)=>{if(t<65534&&e.subarray&&r)return String.fromCharCode.apply(null,e.length===t?e:e.subarray(0,t));let n="";for(let r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n})(a,s)},t.exports.utf8border=(e,t)=>{(t=t||e.length)>e.length&&(t=e.length);let n=t-1;for(;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+i[e[n]]>t?n:t}},{}],32:[function(e,t,n){"use strict";t.exports=(e,t,n,r)=>{let i=65535&e|0,o=e>>>16&65535|0,s=0;for(;0!==n;){s=n>2e3?2e3:n,n-=s;do{i=i+t[r++]|0,o=o+i|0}while(--s);i%=65521,o%=65521}return i|o<<16|0}},{}],33:[function(e,t,n){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],34:[function(e,t,n){"use strict";const r=new Uint32Array((()=>{let e,t=[];for(var n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t})());t.exports=(e,t,n,i)=>{const o=r,s=i+n;e^=-1;for(let n=i;n<s;n++)e=e>>>8^o[255&(e^t[n])];return-1^e}},{}],35:[function(e,t,n){"use strict";const{_tr_init:r,_tr_stored_block:i,_tr_flush_block:o,_tr_tally:s,_tr_align:a}=e("./trees"),l=e("./adler32"),c=e("./crc32"),u=e("./messages"),{Z_NO_FLUSH:f,Z_PARTIAL_FLUSH:d,Z_FULL_FLUSH:p,Z_FINISH:h,Z_BLOCK:g,Z_OK:m,Z_STREAM_END:y,Z_STREAM_ERROR:b,Z_DATA_ERROR:w,Z_BUF_ERROR:_,Z_DEFAULT_COMPRESSION:v,Z_FILTERED:O,Z_HUFFMAN_ONLY:E,Z_RLE:k,Z_FIXED:A,Z_DEFAULT_STRATEGY:S,Z_UNKNOWN:x,Z_DEFLATED:T}=e("./constants"),I=258,R=262,C=103,N=113,D=666,P=(e,t)=>(e.msg=u[t],t),B=e=>(e<<1)-(e>4?9:0),j=e=>{let t=e.length;for(;--t>=0;)e[t]=0};let M=(e,t,n)=>(t<<e.hash_shift^n)&e.hash_mask;const L=e=>{const t=e.state;let n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(e.output.set(t.pending_buf.subarray(t.pending_out,t.pending_out+n),e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))},U=(e,t)=>{o(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,L(e.strm)},z=(e,t)=>{e.pending_buf[e.pending++]=t},W=(e,t)=>{e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t},Z=(e,t,n,r)=>{let i=e.avail_in;return i>r&&(i=r),0===i?0:(e.avail_in-=i,t.set(e.input.subarray(e.next_in,e.next_in+i),n),1===e.state.wrap?e.adler=l(e.adler,t,i,n):2===e.state.wrap&&(e.adler=c(e.adler,t,i,n)),e.next_in+=i,e.total_in+=i,i)},F=(e,t)=>{let n,r,i=e.max_chain_length,o=e.strstart,s=e.prev_length,a=e.nice_match;const l=e.strstart>e.w_size-R?e.strstart-(e.w_size-R):0,c=e.window,u=e.w_mask,f=e.prev,d=e.strstart+I;let p=c[o+s-1],h=c[o+s];e.prev_length>=e.good_match&&(i>>=2),a>e.lookahead&&(a=e.lookahead);do{if(n=t,c[n+s]===h&&c[n+s-1]===p&&c[n]===c[o]&&c[++n]===c[o+1]){o+=2,n++;do{}while(c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&c[++o]===c[++n]&&o<d);if(r=I-(d-o),o=d-I,r>s){if(e.match_start=t,s=r,r>=a)break;p=c[o+s-1],h=c[o+s]}}}while((t=f[t&u])>l&&0!=--i);return s<=e.lookahead?s:e.lookahead},H=e=>{const t=e.w_size;let n,r,i,o,s;do{if(o=e.window_size-e.lookahead-e.strstart,e.strstart>=t+(t-R)){e.window.set(e.window.subarray(t,t+t),0),e.match_start-=t,e.strstart-=t,e.block_start-=t,r=e.hash_size,n=r;do{i=e.head[--n],e.head[n]=i>=t?i-t:0}while(--r);r=t,n=r;do{i=e.prev[--n],e.prev[n]=i>=t?i-t:0}while(--r);o+=t}if(0===e.strm.avail_in)break;if(r=Z(e.strm,e.window,e.strstart+e.lookahead,o),e.lookahead+=r,e.lookahead+e.insert>=3)for(s=e.strstart-e.insert,e.ins_h=e.window[s],e.ins_h=M(e,e.ins_h,e.window[s+1]);e.insert&&(e.ins_h=M(e,e.ins_h,e.window[s+3-1]),e.prev[s&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=s,s++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<R&&0!==e.strm.avail_in)},G=(e,t)=>{let n,r;for(;;){if(e.lookahead<R){if(H(e),e.lookahead<R&&t===f)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=M(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-R&&(e.match_length=F(e,n)),e.match_length>=3)if(r=s(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=M(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=M(e,e.ins_h,e.window[e.strstart+1]);else r=s(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(U(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,t===h?(U(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(U(e,!1),0===e.strm.avail_out)?1:2},V=(e,t)=>{let n,r,i;for(;;){if(e.lookahead<R){if(H(e),e.lookahead<R&&t===f)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=M(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-R&&(e.match_length=F(e,n),e.match_length<=5&&(e.strategy===O||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-3,r=s(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=M(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,r&&(U(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if(r=s(e,0,e.window[e.strstart-1]),r&&U(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=s(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,t===h?(U(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(U(e,!1),0===e.strm.avail_out)?1:2};function q(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}const J=[new q(0,0,0,0,((e,t)=>{let n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(H(e),0===e.lookahead&&t===f)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;const r=e.block_start+n;if((0===e.strstart||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,U(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-R&&(U(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===h?(U(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(U(e,!1),e.strm.avail_out),1)})),new q(4,4,8,4,G),new q(4,5,16,8,G),new q(4,6,32,32,G),new q(4,4,16,16,V),new q(8,16,32,32,V),new q(8,16,128,128,V),new q(8,32,128,256,V),new q(32,128,258,1024,V),new q(32,258,258,4096,V)];function Y(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=T,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),j(this.dyn_ltree),j(this.dyn_dtree),j(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),j(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),j(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const Q=e=>{if(!e||!e.state)return P(e,b);e.total_in=e.total_out=0,e.data_type=x;const t=e.state;return t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:N,e.adler=2===t.wrap?0:1,t.last_flush=f,r(t),m},X=e=>{const t=Q(e);var n;return t===m&&((n=e.state).window_size=2*n.w_size,j(n.head),n.max_lazy_match=J[n.level].max_lazy,n.good_match=J[n.level].good_length,n.nice_match=J[n.level].nice_length,n.max_chain_length=J[n.level].max_chain,n.strstart=0,n.block_start=0,n.lookahead=0,n.insert=0,n.match_length=n.prev_length=2,n.match_available=0,n.ins_h=0),t},K=(e,t,n,r,i,o)=>{if(!e)return b;let s=1;if(t===v&&(t=6),r<0?(s=0,r=-r):r>15&&(s=2,r-=16),i<1||i>9||n!==T||r<8||r>15||t<0||t>9||o<0||o>A)return P(e,b);8===r&&(r=9);const a=new Y;return e.state=a,a.strm=e,a.wrap=s,a.gzhead=null,a.w_bits=r,a.w_size=1<<a.w_bits,a.w_mask=a.w_size-1,a.hash_bits=i+7,a.hash_size=1<<a.hash_bits,a.hash_mask=a.hash_size-1,a.hash_shift=~~((a.hash_bits+3-1)/3),a.window=new Uint8Array(2*a.w_size),a.head=new Uint16Array(a.hash_size),a.prev=new Uint16Array(a.w_size),a.lit_bufsize=1<<i+6,a.pending_buf_size=4*a.lit_bufsize,a.pending_buf=new Uint8Array(a.pending_buf_size),a.d_buf=1*a.lit_bufsize,a.l_buf=3*a.lit_bufsize,a.level=t,a.strategy=o,a.method=n,X(e)};t.exports.deflateInit=(e,t)=>K(e,t,T,15,8,S),t.exports.deflateInit2=K,t.exports.deflateReset=X,t.exports.deflateResetKeep=Q,t.exports.deflateSetHeader=(e,t)=>e&&e.state?2!==e.state.wrap?b:(e.state.gzhead=t,m):b,t.exports.deflate=(e,t)=>{let n,r;if(!e||!e.state||t>g||t<0)return e?P(e,b):b;const o=e.state;if(!e.output||!e.input&&0!==e.avail_in||o.status===D&&t!==h)return P(e,0===e.avail_out?_:b);o.strm=e;const l=o.last_flush;if(o.last_flush=t,42===o.status)if(2===o.wrap)e.adler=0,z(o,31),z(o,139),z(o,8),o.gzhead?(z(o,(o.gzhead.text?1:0)+(o.gzhead.hcrc?2:0)+(o.gzhead.extra?4:0)+(o.gzhead.name?8:0)+(o.gzhead.comment?16:0)),z(o,255&o.gzhead.time),z(o,o.gzhead.time>>8&255),z(o,o.gzhead.time>>16&255),z(o,o.gzhead.time>>24&255),z(o,9===o.level?2:o.strategy>=E||o.level<2?4:0),z(o,255&o.gzhead.os),o.gzhead.extra&&o.gzhead.extra.length&&(z(o,255&o.gzhead.extra.length),z(o,o.gzhead.extra.length>>8&255)),o.gzhead.hcrc&&(e.adler=c(e.adler,o.pending_buf,o.pending,0)),o.gzindex=0,o.status=69):(z(o,0),z(o,0),z(o,0),z(o,0),z(o,0),z(o,9===o.level?2:o.strategy>=E||o.level<2?4:0),z(o,3),o.status=N);else{let t=T+(o.w_bits-8<<4)<<8,n=-1;n=o.strategy>=E||o.level<2?0:o.level<6?1:6===o.level?2:3,t|=n<<6,0!==o.strstart&&(t|=32),t+=31-t%31,o.status=N,W(o,t),0!==o.strstart&&(W(o,e.adler>>>16),W(o,65535&e.adler)),e.adler=1}if(69===o.status)if(o.gzhead.extra){for(n=o.pending;o.gzindex<(65535&o.gzhead.extra.length)&&(o.pending!==o.pending_buf_size||(o.gzhead.hcrc&&o.pending>n&&(e.adler=c(e.adler,o.pending_buf,o.pending-n,n)),L(e),n=o.pending,o.pending!==o.pending_buf_size));)z(o,255&o.gzhead.extra[o.gzindex]),o.gzindex++;o.gzhead.hcrc&&o.pending>n&&(e.adler=c(e.adler,o.pending_buf,o.pending-n,n)),o.gzindex===o.gzhead.extra.length&&(o.gzindex=0,o.status=73)}else o.status=73;if(73===o.status)if(o.gzhead.name){n=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>n&&(e.adler=c(e.adler,o.pending_buf,o.pending-n,n)),L(e),n=o.pending,o.pending===o.pending_buf_size)){r=1;break}r=o.gzindex<o.gzhead.name.length?255&o.gzhead.name.charCodeAt(o.gzindex++):0,z(o,r)}while(0!==r);o.gzhead.hcrc&&o.pending>n&&(e.adler=c(e.adler,o.pending_buf,o.pending-n,n)),0===r&&(o.gzindex=0,o.status=91)}else o.status=91;if(91===o.status)if(o.gzhead.comment){n=o.pending;do{if(o.pending===o.pending_buf_size&&(o.gzhead.hcrc&&o.pending>n&&(e.adler=c(e.adler,o.pending_buf,o.pending-n,n)),L(e),n=o.pending,o.pending===o.pending_buf_size)){r=1;break}r=o.gzindex<o.gzhead.comment.length?255&o.gzhead.comment.charCodeAt(o.gzindex++):0,z(o,r)}while(0!==r);o.gzhead.hcrc&&o.pending>n&&(e.adler=c(e.adler,o.pending_buf,o.pending-n,n)),0===r&&(o.status=C)}else o.status=C;if(o.status===C&&(o.gzhead.hcrc?(o.pending+2>o.pending_buf_size&&L(e),o.pending+2<=o.pending_buf_size&&(z(o,255&e.adler),z(o,e.adler>>8&255),e.adler=0,o.status=N)):o.status=N),0!==o.pending){if(L(e),0===e.avail_out)return o.last_flush=-1,m}else if(0===e.avail_in&&B(t)<=B(l)&&t!==h)return P(e,_);if(o.status===D&&0!==e.avail_in)return P(e,_);if(0!==e.avail_in||0!==o.lookahead||t!==f&&o.status!==D){let n=o.strategy===E?((e,t)=>{let n;for(;;){if(0===e.lookahead&&(H(e),0===e.lookahead)){if(t===f)return 1;break}if(e.match_length=0,n=s(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(U(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===h?(U(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(U(e,!1),0===e.strm.avail_out)?1:2})(o,t):o.strategy===k?((e,t)=>{let n,r,i,o;const a=e.window;for(;;){if(e.lookahead<=I){if(H(e),e.lookahead<=I&&t===f)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(i=e.strstart-1,r=a[i],r===a[++i]&&r===a[++i]&&r===a[++i])){o=e.strstart+I;do{}while(r===a[++i]&&r===a[++i]&&r===a[++i]&&r===a[++i]&&r===a[++i]&&r===a[++i]&&r===a[++i]&&r===a[++i]&&i<o);e.match_length=I-(o-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(n=s(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=s(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(U(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===h?(U(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(U(e,!1),0===e.strm.avail_out)?1:2})(o,t):J[o.level].func(o,t);if(3!==n&&4!==n||(o.status=D),1===n||3===n)return 0===e.avail_out&&(o.last_flush=-1),m;if(2===n&&(t===d?a(o):t!==g&&(i(o,0,0,!1),t===p&&(j(o.head),0===o.lookahead&&(o.strstart=0,o.block_start=0,o.insert=0))),L(e),0===e.avail_out))return o.last_flush=-1,m}return t!==h?m:o.wrap<=0?y:(2===o.wrap?(z(o,255&e.adler),z(o,e.adler>>8&255),z(o,e.adler>>16&255),z(o,e.adler>>24&255),z(o,255&e.total_in),z(o,e.total_in>>8&255),z(o,e.total_in>>16&255),z(o,e.total_in>>24&255)):(W(o,e.adler>>>16),W(o,65535&e.adler)),L(e),o.wrap>0&&(o.wrap=-o.wrap),0!==o.pending?m:y)},t.exports.deflateEnd=e=>{if(!e||!e.state)return b;const t=e.state.status;return 42!==t&&69!==t&&73!==t&&91!==t&&t!==C&&t!==N&&t!==D?P(e,b):(e.state=null,t===N?P(e,w):m)},t.exports.deflateSetDictionary=(e,t)=>{let n=t.length;if(!e||!e.state)return b;const r=e.state,i=r.wrap;if(2===i||1===i&&42!==r.status||r.lookahead)return b;if(1===i&&(e.adler=l(e.adler,t,n,0)),r.wrap=0,n>=r.w_size){0===i&&(j(r.head),r.strstart=0,r.block_start=0,r.insert=0);let e=new Uint8Array(r.w_size);e.set(t.subarray(n-r.w_size,n),0),t=e,n=r.w_size}const o=e.avail_in,s=e.next_in,a=e.input;for(e.avail_in=n,e.next_in=0,e.input=t,H(r);r.lookahead>=3;){let e=r.strstart,t=r.lookahead-2;do{r.ins_h=M(r,r.ins_h,r.window[e+3-1]),r.prev[e&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=e,e++}while(--t);r.strstart=e,r.lookahead=2,H(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,e.next_in=s,e.input=a,e.avail_in=o,r.wrap=i,m},t.exports.deflateInfo="pako deflate (from Nodeca project)"},{"./adler32":32,"./constants":33,"./crc32":34,"./messages":40,"./trees":41}],36:[function(e,t,n){"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],37:[function(e,t,n){"use strict";t.exports=function(e,t){let n,r,i,o,s,a,l,c,u,f,d,p,h,g,m,y,b,w,_,v,O,E,k,A;const S=e.state;n=e.next_in,k=e.input,r=n+(e.avail_in-5),i=e.next_out,A=e.output,o=i-(t-e.avail_out),s=i+(e.avail_out-257),a=S.dmax,l=S.wsize,c=S.whave,u=S.wnext,f=S.window,d=S.hold,p=S.bits,h=S.lencode,g=S.distcode,m=(1<<S.lenbits)-1,y=(1<<S.distbits)-1;e:do{p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),b=h[d&m];t:for(;;){if(w=b>>>24,d>>>=w,p-=w,w=b>>>16&255,0===w)A[i++]=65535&b;else{if(!(16&w)){if(0==(64&w)){b=h[(65535&b)+(d&(1<<w)-1)];continue t}if(32&w){S.mode=12;break e}e.msg="invalid literal/length code",S.mode=30;break e}_=65535&b,w&=15,w&&(p<w&&(d+=k[n++]<<p,p+=8),_+=d&(1<<w)-1,d>>>=w,p-=w),p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),b=g[d&y];n:for(;;){if(w=b>>>24,d>>>=w,p-=w,w=b>>>16&255,!(16&w)){if(0==(64&w)){b=g[(65535&b)+(d&(1<<w)-1)];continue n}e.msg="invalid distance code",S.mode=30;break e}if(v=65535&b,w&=15,p<w&&(d+=k[n++]<<p,p+=8,p<w&&(d+=k[n++]<<p,p+=8)),v+=d&(1<<w)-1,v>a){e.msg="invalid distance too far back",S.mode=30;break e}if(d>>>=w,p-=w,w=i-o,v>w){if(w=v-w,w>c&&S.sane){e.msg="invalid distance too far back",S.mode=30;break e}if(O=0,E=f,0===u){if(O+=l-w,w<_){_-=w;do{A[i++]=f[O++]}while(--w);O=i-v,E=A}}else if(u<w){if(O+=l+u-w,w-=u,w<_){_-=w;do{A[i++]=f[O++]}while(--w);if(O=0,u<_){w=u,_-=w;do{A[i++]=f[O++]}while(--w);O=i-v,E=A}}}else if(O+=u-w,w<_){_-=w;do{A[i++]=f[O++]}while(--w);O=i-v,E=A}for(;_>2;)A[i++]=E[O++],A[i++]=E[O++],A[i++]=E[O++],_-=3;_&&(A[i++]=E[O++],_>1&&(A[i++]=E[O++]))}else{O=i-v;do{A[i++]=A[O++],A[i++]=A[O++],A[i++]=A[O++],_-=3}while(_>2);_&&(A[i++]=A[O++],_>1&&(A[i++]=A[O++]))}break}}break}}while(n<r&&i<s);_=p>>3,n-=_,p-=_<<3,d&=(1<<p)-1,e.next_in=n,e.next_out=i,e.avail_in=n<r?r-n+5:5-(n-r),e.avail_out=i<s?s-i+257:257-(i-s),S.hold=d,S.bits=p}},{}],38:[function(e,t,n){"use strict";const r=e("./adler32"),i=e("./crc32"),o=e("./inffast"),s=e("./inftrees"),{Z_FINISH:a,Z_BLOCK:l,Z_TREES:c,Z_OK:u,Z_STREAM_END:f,Z_NEED_DICT:d,Z_STREAM_ERROR:p,Z_DATA_ERROR:h,Z_MEM_ERROR:g,Z_BUF_ERROR:m,Z_DEFLATED:y}=e("./constants"),b=12,w=30,_=e=>(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24);function v(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const O=e=>{if(!e||!e.state)return p;const t=e.state;return e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(852),t.distcode=t.distdyn=new Int32Array(592),t.sane=1,t.back=-1,u},E=e=>{if(!e||!e.state)return p;const t=e.state;return t.wsize=0,t.whave=0,t.wnext=0,O(e)},k=(e,t)=>{let n;if(!e||!e.state)return p;const r=e.state;return t<0?(n=0,t=-t):(n=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?p:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,E(e))},A=(e,t)=>{if(!e)return p;const n=new v;e.state=n,n.window=null;const r=k(e,t);return r!==u&&(e.state=null),r};let S,x,T=!0;const I=e=>{if(T){S=new Int32Array(512),x=new Int32Array(32);let t=0;for(;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(s(1,e.lens,0,288,S,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;s(2,e.lens,0,32,x,0,e.work,{bits:5}),T=!1}e.lencode=S,e.lenbits=9,e.distcode=x,e.distbits=5},R=(e,t,n,r)=>{let i;const o=e.state;return null===o.window&&(o.wsize=1<<o.wbits,o.wnext=0,o.whave=0,o.window=new Uint8Array(o.wsize)),r>=o.wsize?(o.window.set(t.subarray(n-o.wsize,n),0),o.wnext=0,o.whave=o.wsize):(i=o.wsize-o.wnext,i>r&&(i=r),o.window.set(t.subarray(n-r,n-r+i),o.wnext),(r-=i)?(o.window.set(t.subarray(n-r,n),0),o.wnext=r,o.whave=o.wsize):(o.wnext+=i,o.wnext===o.wsize&&(o.wnext=0),o.whave<o.wsize&&(o.whave+=i))),0};t.exports.inflateReset=E,t.exports.inflateReset2=k,t.exports.inflateResetKeep=O,t.exports.inflateInit=e=>A(e,15),t.exports.inflateInit2=A,t.exports.inflate=(e,t)=>{let n,v,O,E,k,A,S,x,T,C,N,D,P,B,j,M,L,U,z,W,Z,F,H=0;const G=new Uint8Array(4);let V,q;const J=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return p;n=e.state,n.mode===b&&(n.mode=13),k=e.next_out,O=e.output,S=e.avail_out,E=e.next_in,v=e.input,A=e.avail_in,x=n.hold,T=n.bits,C=A,N=S,F=u;e:for(;;)switch(n.mode){case 1:if(0===n.wrap){n.mode=13;break}for(;T<16;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(2&n.wrap&&35615===x){n.check=0,G[0]=255&x,G[1]=x>>>8&255,n.check=i(n.check,G,2,0),x=0,T=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&x)<<8)+(x>>8))%31){e.msg="incorrect header check",n.mode=w;break}if((15&x)!==y){e.msg="unknown compression method",n.mode=w;break}if(x>>>=4,T-=4,Z=8+(15&x),0===n.wbits)n.wbits=Z;else if(Z>n.wbits){e.msg="invalid window size",n.mode=w;break}n.dmax=1<<n.wbits,e.adler=n.check=1,n.mode=512&x?10:b,x=0,T=0;break;case 2:for(;T<16;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(n.flags=x,(255&n.flags)!==y){e.msg="unknown compression method",n.mode=w;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=w;break}n.head&&(n.head.text=x>>8&1),512&n.flags&&(G[0]=255&x,G[1]=x>>>8&255,n.check=i(n.check,G,2,0)),x=0,T=0,n.mode=3;case 3:for(;T<32;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}n.head&&(n.head.time=x),512&n.flags&&(G[0]=255&x,G[1]=x>>>8&255,G[2]=x>>>16&255,G[3]=x>>>24&255,n.check=i(n.check,G,4,0)),x=0,T=0,n.mode=4;case 4:for(;T<16;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}n.head&&(n.head.xflags=255&x,n.head.os=x>>8),512&n.flags&&(G[0]=255&x,G[1]=x>>>8&255,n.check=i(n.check,G,2,0)),x=0,T=0,n.mode=5;case 5:if(1024&n.flags){for(;T<16;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}n.length=x,n.head&&(n.head.extra_len=x),512&n.flags&&(G[0]=255&x,G[1]=x>>>8&255,n.check=i(n.check,G,2,0)),x=0,T=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&(D=n.length,D>A&&(D=A),D&&(n.head&&(Z=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Uint8Array(n.head.extra_len)),n.head.extra.set(v.subarray(E,E+D),Z)),512&n.flags&&(n.check=i(n.check,v,D,E)),A-=D,E+=D,n.length-=D),n.length))break e;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===A)break e;D=0;do{Z=v[E+D++],n.head&&Z&&n.length<65536&&(n.head.name+=String.fromCharCode(Z))}while(Z&&D<A);if(512&n.flags&&(n.check=i(n.check,v,D,E)),A-=D,E+=D,Z)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===A)break e;D=0;do{Z=v[E+D++],n.head&&Z&&n.length<65536&&(n.head.comment+=String.fromCharCode(Z))}while(Z&&D<A);if(512&n.flags&&(n.check=i(n.check,v,D,E)),A-=D,E+=D,Z)break e}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;T<16;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(x!==(65535&n.check)){e.msg="header crc mismatch",n.mode=w;break}x=0,T=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=b;break;case 10:for(;T<32;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}e.adler=n.check=_(x),x=0,T=0,n.mode=11;case 11:if(0===n.havedict)return e.next_out=k,e.avail_out=S,e.next_in=E,e.avail_in=A,n.hold=x,n.bits=T,d;e.adler=n.check=1,n.mode=b;case b:if(t===l||t===c)break e;case 13:if(n.last){x>>>=7&T,T-=7&T,n.mode=27;break}for(;T<3;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}switch(n.last=1&x,x>>>=1,T-=1,3&x){case 0:n.mode=14;break;case 1:if(I(n),n.mode=20,t===c){x>>>=2,T-=2;break e}break;case 2:n.mode=17;break;case 3:e.msg="invalid block type",n.mode=w}x>>>=2,T-=2;break;case 14:for(x>>>=7&T,T-=7&T;T<32;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if((65535&x)!=(x>>>16^65535)){e.msg="invalid stored block lengths",n.mode=w;break}if(n.length=65535&x,x=0,T=0,n.mode=15,t===c)break e;case 15:n.mode=16;case 16:if(D=n.length,D){if(D>A&&(D=A),D>S&&(D=S),0===D)break e;O.set(v.subarray(E,E+D),k),A-=D,E+=D,S-=D,k+=D,n.length-=D;break}n.mode=b;break;case 17:for(;T<14;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(n.nlen=257+(31&x),x>>>=5,T-=5,n.ndist=1+(31&x),x>>>=5,T-=5,n.ncode=4+(15&x),x>>>=4,T-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=w;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;T<3;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}n.lens[J[n.have++]]=7&x,x>>>=3,T-=3}for(;n.have<19;)n.lens[J[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,V={bits:n.lenbits},F=s(0,n.lens,0,19,n.lencode,0,n.work,V),n.lenbits=V.bits,F){e.msg="invalid code lengths set",n.mode=w;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;H=n.lencode[x&(1<<n.lenbits)-1],j=H>>>24,M=H>>>16&255,L=65535&H,!(j<=T);){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(L<16)x>>>=j,T-=j,n.lens[n.have++]=L;else{if(16===L){for(q=j+2;T<q;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(x>>>=j,T-=j,0===n.have){e.msg="invalid bit length repeat",n.mode=w;break}Z=n.lens[n.have-1],D=3+(3&x),x>>>=2,T-=2}else if(17===L){for(q=j+3;T<q;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}x>>>=j,T-=j,Z=0,D=3+(7&x),x>>>=3,T-=3}else{for(q=j+7;T<q;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}x>>>=j,T-=j,Z=0,D=11+(127&x),x>>>=7,T-=7}if(n.have+D>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=w;break}for(;D--;)n.lens[n.have++]=Z}}if(n.mode===w)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=w;break}if(n.lenbits=9,V={bits:n.lenbits},F=s(1,n.lens,0,n.nlen,n.lencode,0,n.work,V),n.lenbits=V.bits,F){e.msg="invalid literal/lengths set",n.mode=w;break}if(n.distbits=6,n.distcode=n.distdyn,V={bits:n.distbits},F=s(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,V),n.distbits=V.bits,F){e.msg="invalid distances set",n.mode=w;break}if(n.mode=20,t===c)break e;case 20:n.mode=21;case 21:if(A>=6&&S>=258){e.next_out=k,e.avail_out=S,e.next_in=E,e.avail_in=A,n.hold=x,n.bits=T,o(e,N),k=e.next_out,O=e.output,S=e.avail_out,E=e.next_in,v=e.input,A=e.avail_in,x=n.hold,T=n.bits,n.mode===b&&(n.back=-1);break}for(n.back=0;H=n.lencode[x&(1<<n.lenbits)-1],j=H>>>24,M=H>>>16&255,L=65535&H,!(j<=T);){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(M&&0==(240&M)){for(U=j,z=M,W=L;H=n.lencode[W+((x&(1<<U+z)-1)>>U)],j=H>>>24,M=H>>>16&255,L=65535&H,!(U+j<=T);){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}x>>>=U,T-=U,n.back+=U}if(x>>>=j,T-=j,n.back+=j,n.length=L,0===M){n.mode=26;break}if(32&M){n.back=-1,n.mode=b;break}if(64&M){e.msg="invalid literal/length code",n.mode=w;break}n.extra=15&M,n.mode=22;case 22:if(n.extra){for(q=n.extra;T<q;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}n.length+=x&(1<<n.extra)-1,x>>>=n.extra,T-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;H=n.distcode[x&(1<<n.distbits)-1],j=H>>>24,M=H>>>16&255,L=65535&H,!(j<=T);){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(0==(240&M)){for(U=j,z=M,W=L;H=n.distcode[W+((x&(1<<U+z)-1)>>U)],j=H>>>24,M=H>>>16&255,L=65535&H,!(U+j<=T);){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}x>>>=U,T-=U,n.back+=U}if(x>>>=j,T-=j,n.back+=j,64&M){e.msg="invalid distance code",n.mode=w;break}n.offset=L,n.extra=15&M,n.mode=24;case 24:if(n.extra){for(q=n.extra;T<q;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}n.offset+=x&(1<<n.extra)-1,x>>>=n.extra,T-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=w;break}n.mode=25;case 25:if(0===S)break e;if(D=N-S,n.offset>D){if(D=n.offset-D,D>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=w;break}D>n.wnext?(D-=n.wnext,P=n.wsize-D):P=n.wnext-D,D>n.length&&(D=n.length),B=n.window}else B=O,P=k-n.offset,D=n.length;D>S&&(D=S),S-=D,n.length-=D;do{O[k++]=B[P++]}while(--D);0===n.length&&(n.mode=21);break;case 26:if(0===S)break e;O[k++]=n.length,S--,n.mode=21;break;case 27:if(n.wrap){for(;T<32;){if(0===A)break e;A--,x|=v[E++]<<T,T+=8}if(N-=S,e.total_out+=N,n.total+=N,N&&(e.adler=n.check=n.flags?i(n.check,O,N,k-N):r(n.check,O,N,k-N)),N=S,(n.flags?x:_(x))!==n.check){e.msg="incorrect data check",n.mode=w;break}x=0,T=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;T<32;){if(0===A)break e;A--,x+=v[E++]<<T,T+=8}if(x!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=w;break}x=0,T=0}n.mode=29;case 29:F=f;break e;case w:F=h;break e;case 31:return g;default:return p}return e.next_out=k,e.avail_out=S,e.next_in=E,e.avail_in=A,n.hold=x,n.bits=T,(n.wsize||N!==e.avail_out&&n.mode<w&&(n.mode<27||t!==a))&&R(e,e.output,e.next_out,N-e.avail_out)?(n.mode=31,g):(C-=e.avail_in,N-=e.avail_out,e.total_in+=C,e.total_out+=N,n.total+=N,n.wrap&&N&&(e.adler=n.check=n.flags?i(n.check,O,N,e.next_out-N):r(n.check,O,N,e.next_out-N)),e.data_type=n.bits+(n.last?64:0)+(n.mode===b?128:0)+(20===n.mode||15===n.mode?256:0),(0===C&&0===N||t===a)&&F===u&&(F=m),F)},t.exports.inflateEnd=e=>{if(!e||!e.state)return p;let t=e.state;return t.window&&(t.window=null),e.state=null,u},t.exports.inflateGetHeader=(e,t)=>{if(!e||!e.state)return p;const n=e.state;return 0==(2&n.wrap)?p:(n.head=t,t.done=!1,u)},t.exports.inflateSetDictionary=(e,t)=>{const n=t.length;let i,o,s;return e&&e.state?(i=e.state,0!==i.wrap&&11!==i.mode?p:11===i.mode&&(o=1,o=r(o,t,n,0),o!==i.check)?h:(s=R(e,t,n,n),s?(i.mode=31,g):(i.havedict=1,u))):p},t.exports.inflateInfo="pako inflate (from Nodeca project)"},{"./adler32":32,"./constants":33,"./crc32":34,"./inffast":37,"./inftrees":39}],39:[function(e,t,n){"use strict";const r=15,i=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),o=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),s=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),a=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);t.exports=(e,t,n,l,c,u,f,d)=>{const p=d.bits;let h,g,m,y,b,w,_=0,v=0,O=0,E=0,k=0,A=0,S=0,x=0,T=0,I=0,R=null,C=0;const N=new Uint16Array(16),D=new Uint16Array(16);let P,B,j,M=null,L=0;for(_=0;_<=r;_++)N[_]=0;for(v=0;v<l;v++)N[t[n+v]]++;for(k=p,E=r;E>=1&&0===N[E];E--);if(k>E&&(k=E),0===E)return c[u++]=20971520,c[u++]=20971520,d.bits=1,0;for(O=1;O<E&&0===N[O];O++);for(k<O&&(k=O),x=1,_=1;_<=r;_++)if(x<<=1,x-=N[_],x<0)return-1;if(x>0&&(0===e||1!==E))return-1;for(D[1]=0,_=1;_<r;_++)D[_+1]=D[_]+N[_];for(v=0;v<l;v++)0!==t[n+v]&&(f[D[t[n+v]]++]=v);if(0===e?(R=M=f,w=19):1===e?(R=i,C-=257,M=o,L-=257,w=256):(R=s,M=a,w=-1),I=0,v=0,_=O,b=u,A=k,S=0,m=-1,T=1<<k,y=T-1,1===e&&T>852||2===e&&T>592)return 1;for(;;){P=_-S,f[v]<w?(B=0,j=f[v]):f[v]>w?(B=M[L+f[v]],j=R[C+f[v]]):(B=96,j=0),h=1<<_-S,g=1<<A,O=g;do{g-=h,c[b+(I>>S)+g]=P<<24|B<<16|j|0}while(0!==g);for(h=1<<_-1;I&h;)h>>=1;if(0!==h?(I&=h-1,I+=h):I=0,v++,0==--N[_]){if(_===E)break;_=t[n+f[v]]}if(_>k&&(I&y)!==m){for(0===S&&(S=k),b+=O,A=_-S,x=1<<A;A+S<E&&(x-=N[A+S],!(x<=0));)A++,x<<=1;if(T+=1<<A,1===e&&T>852||2===e&&T>592)return 1;m=I&y,c[m]=k<<24|A<<16|b-u|0}}return 0!==I&&(c[b+I]=_-S<<24|64<<16|0),d.bits=k,0}},{}],40:[function(e,t,n){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],41:[function(e,t,n){"use strict";function r(e){let t=e.length;for(;--t>=0;)e[t]=0}const i=256,o=286,s=30,a=15,l=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),c=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),u=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),f=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),d=new Array(576);r(d);const p=new Array(60);r(p);const h=new Array(512);r(h);const g=new Array(256);r(g);const m=new Array(29);r(m);const y=new Array(s);function b(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}let w,_,v;function O(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}r(y);const E=e=>e<256?h[e]:h[256+(e>>>7)],k=(e,t)=>{e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255},A=(e,t,n)=>{e.bi_valid>16-n?(e.bi_buf|=t<<e.bi_valid&65535,k(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=n-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)},S=(e,t,n)=>{A(e,n[2*t],n[2*t+1])},x=(e,t)=>{let n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1},T=(e,t,n)=>{const r=new Array(16);let i,o,s=0;for(i=1;i<=a;i++)r[i]=s=s+n[i-1]<<1;for(o=0;o<=t;o++){let t=e[2*o+1];0!==t&&(e[2*o]=x(r[t]++,t))}},I=e=>{let t;for(t=0;t<o;t++)e.dyn_ltree[2*t]=0;for(t=0;t<s;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0},R=e=>{e.bi_valid>8?k(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},C=(e,t,n,r)=>{const i=2*t,o=2*n;return e[i]<e[o]||e[i]===e[o]&&r[t]<=r[n]},N=(e,t,n)=>{const r=e.heap[n];let i=n<<1;for(;i<=e.heap_len&&(i<e.heap_len&&C(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!C(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r},D=(e,t,n)=>{let r,o,s,a,u=0;if(0!==e.last_lit)do{r=e.pending_buf[e.d_buf+2*u]<<8|e.pending_buf[e.d_buf+2*u+1],o=e.pending_buf[e.l_buf+u],u++,0===r?S(e,o,t):(s=g[o],S(e,s+i+1,t),a=l[s],0!==a&&(o-=m[s],A(e,o,a)),r--,s=E(r),S(e,s,n),a=c[s],0!==a&&(r-=y[s],A(e,r,a)))}while(u<e.last_lit);S(e,256,t)},P=(e,t)=>{const n=t.dyn_tree,r=t.stat_desc.static_tree,i=t.stat_desc.has_stree,o=t.stat_desc.elems;let s,l,c,u=-1;for(e.heap_len=0,e.heap_max=573,s=0;s<o;s++)0!==n[2*s]?(e.heap[++e.heap_len]=u=s,e.depth[s]=0):n[2*s+1]=0;for(;e.heap_len<2;)c=e.heap[++e.heap_len]=u<2?++u:0,n[2*c]=1,e.depth[c]=0,e.opt_len--,i&&(e.static_len-=r[2*c+1]);for(t.max_code=u,s=e.heap_len>>1;s>=1;s--)N(e,n,s);c=o;do{s=e.heap[1],e.heap[1]=e.heap[e.heap_len--],N(e,n,1),l=e.heap[1],e.heap[--e.heap_max]=s,e.heap[--e.heap_max]=l,n[2*c]=n[2*s]+n[2*l],e.depth[c]=(e.depth[s]>=e.depth[l]?e.depth[s]:e.depth[l])+1,n[2*s+1]=n[2*l+1]=c,e.heap[1]=c++,N(e,n,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],((e,t)=>{const n=t.dyn_tree,r=t.max_code,i=t.stat_desc.static_tree,o=t.stat_desc.has_stree,s=t.stat_desc.extra_bits,l=t.stat_desc.extra_base,c=t.stat_desc.max_length;let u,f,d,p,h,g,m=0;for(p=0;p<=a;p++)e.bl_count[p]=0;for(n[2*e.heap[e.heap_max]+1]=0,u=e.heap_max+1;u<573;u++)f=e.heap[u],p=n[2*n[2*f+1]+1]+1,p>c&&(p=c,m++),n[2*f+1]=p,f>r||(e.bl_count[p]++,h=0,f>=l&&(h=s[f-l]),g=n[2*f],e.opt_len+=g*(p+h),o&&(e.static_len+=g*(i[2*f+1]+h)));if(0!==m){do{for(p=c-1;0===e.bl_count[p];)p--;e.bl_count[p]--,e.bl_count[p+1]+=2,e.bl_count[c]--,m-=2}while(m>0);for(p=c;0!==p;p--)for(f=e.bl_count[p];0!==f;)d=e.heap[--u],d>r||(n[2*d+1]!==p&&(e.opt_len+=(p-n[2*d+1])*n[2*d],n[2*d+1]=p),f--)}})(e,t),T(n,u,e.bl_count)},B=(e,t,n)=>{let r,i,o=-1,s=t[1],a=0,l=7,c=4;for(0===s&&(l=138,c=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=s,s=t[2*(r+1)+1],++a<l&&i===s||(a<c?e.bl_tree[2*i]+=a:0!==i?(i!==o&&e.bl_tree[2*i]++,e.bl_tree[32]++):a<=10?e.bl_tree[34]++:e.bl_tree[36]++,a=0,o=i,0===s?(l=138,c=3):i===s?(l=6,c=3):(l=7,c=4))},j=(e,t,n)=>{let r,i,o=-1,s=t[1],a=0,l=7,c=4;for(0===s&&(l=138,c=3),r=0;r<=n;r++)if(i=s,s=t[2*(r+1)+1],!(++a<l&&i===s)){if(a<c)do{S(e,i,e.bl_tree)}while(0!=--a);else 0!==i?(i!==o&&(S(e,i,e.bl_tree),a--),S(e,16,e.bl_tree),A(e,a-3,2)):a<=10?(S(e,17,e.bl_tree),A(e,a-3,3)):(S(e,18,e.bl_tree),A(e,a-11,7));a=0,o=i,0===s?(l=138,c=3):i===s?(l=6,c=3):(l=7,c=4)}};let M=!1;const L=(e,t,n,r)=>{A(e,0+(r?1:0),3),((e,t,n,r)=>{R(e),r&&(k(e,n),k(e,~n)),e.pending_buf.set(e.window.subarray(t,t+n),e.pending),e.pending+=n})(e,t,n,!0)};t.exports._tr_init=e=>{M||((()=>{let e,t,n,r,i;const f=new Array(16);for(n=0,r=0;r<28;r++)for(m[r]=n,e=0;e<1<<l[r];e++)g[n++]=r;for(g[n-1]=r,i=0,r=0;r<16;r++)for(y[r]=i,e=0;e<1<<c[r];e++)h[i++]=r;for(i>>=7;r<s;r++)for(y[r]=i<<7,e=0;e<1<<c[r]-7;e++)h[256+i++]=r;for(t=0;t<=a;t++)f[t]=0;for(e=0;e<=143;)d[2*e+1]=8,e++,f[8]++;for(;e<=255;)d[2*e+1]=9,e++,f[9]++;for(;e<=279;)d[2*e+1]=7,e++,f[7]++;for(;e<=287;)d[2*e+1]=8,e++,f[8]++;for(T(d,287,f),e=0;e<s;e++)p[2*e+1]=5,p[2*e]=x(e,5);w=new b(d,l,257,o,a),_=new b(p,c,0,s,a),v=new b(new Array(0),u,0,19,7)})(),M=!0),e.l_desc=new O(e.dyn_ltree,w),e.d_desc=new O(e.dyn_dtree,_),e.bl_desc=new O(e.bl_tree,v),e.bi_buf=0,e.bi_valid=0,I(e)},t.exports._tr_stored_block=L,t.exports._tr_flush_block=(e,t,n,r)=>{let o,s,a=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=(e=>{let t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<i;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0})(e)),P(e,e.l_desc),P(e,e.d_desc),a=(e=>{let t;for(B(e,e.dyn_ltree,e.l_desc.max_code),B(e,e.dyn_dtree,e.d_desc.max_code),P(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*f[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t})(e),o=e.opt_len+3+7>>>3,s=e.static_len+3+7>>>3,s<=o&&(o=s)):o=s=n+5,n+4<=o&&-1!==t?L(e,t,n,r):4===e.strategy||s===o?(A(e,2+(r?1:0),3),D(e,d,p)):(A(e,4+(r?1:0),3),((e,t,n,r)=>{let i;for(A(e,t-257,5),A(e,n-1,5),A(e,r-4,4),i=0;i<r;i++)A(e,e.bl_tree[2*f[i]+1],3);j(e,e.dyn_ltree,t-1),j(e,e.dyn_dtree,n-1)})(e,e.l_desc.max_code+1,e.d_desc.max_code+1,a+1),D(e,e.dyn_ltree,e.dyn_dtree)),I(e),r&&R(e)},t.exports._tr_tally=(e,t,n)=>(e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(g[n]+i+1)]++,e.dyn_dtree[2*E(t)]++),e.last_lit===e.lit_bufsize-1),t.exports._tr_align=e=>{A(e,2,3),S(e,256,d),(e=>{16===e.bi_valid?(k(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)})(e)}},{}],42:[function(e,t,n){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],43:[function(e,t,n){"use strict";t.exports=e("./src/index-minimal")},{"./src/index-minimal":44}],44:[function(e,t,n){"use strict";var r=n;function i(){r.util._configure(),r.Writer._configure(r.BufferWriter),r.Reader._configure(r.BufferReader)}r.build="minimal",r.Writer=e("./writer"),r.BufferWriter=e("./writer_buffer"),r.Reader=e("./reader"),r.BufferReader=e("./reader_buffer"),r.util=e("./util/minimal"),r.rpc=e("./rpc"),r.roots=e("./roots"),r.configure=i,i()},{"./reader":45,"./reader_buffer":46,"./roots":47,"./rpc":48,"./util/minimal":51,"./writer":52,"./writer_buffer":53}],45:[function(e,t,n){"use strict";t.exports=l;var r,i=e("./util/minimal"),o=i.LongBits,s=i.utf8;function a(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function l(e){this.buf=e,this.pos=0,this.len=e.length}var c,u="undefined"!=typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new l(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new l(e);throw Error("illegal buffer")},f=function(){return i.Buffer?function(e){return(l.create=function(e){return i.Buffer.isBuffer(e)?new r(e):u(e)})(e)}:u};function d(){var e=new o(0,0),t=0;if(!(this.len-this.pos>4)){for(;t<3;++t){if(this.pos>=this.len)throw a(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;if(t=0,this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw a(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function p(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function h(){if(this.pos+8>this.len)throw a(this,8);return new o(p(this.buf,this.pos+=4),p(this.buf,this.pos+=4))}l.create=f(),l.prototype._slice=i.Array.prototype.subarray||i.Array.prototype.slice,l.prototype.uint32=(c=4294967295,function(){if(c=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return c;if(c=(c|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return c;if((this.pos+=5)>this.len)throw this.pos=this.len,a(this,10);return c}),l.prototype.int32=function(){return 0|this.uint32()},l.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)|0},l.prototype.bool=function(){return 0!==this.uint32()},l.prototype.fixed32=function(){if(this.pos+4>this.len)throw a(this,4);return p(this.buf,this.pos+=4)},l.prototype.sfixed32=function(){if(this.pos+4>this.len)throw a(this,4);return 0|p(this.buf,this.pos+=4)},l.prototype.float=function(){if(this.pos+4>this.len)throw a(this,4);var e=i.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},l.prototype.double=function(){if(this.pos+8>this.len)throw a(this,4);var e=i.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},l.prototype.bytes=function(){var e=this.uint32(),t=this.pos,n=this.pos+e;if(n>this.len)throw a(this,e);return this.pos+=e,Array.isArray(this.buf)?this.buf.slice(t,n):t===n?new this.buf.constructor(0):this._slice.call(this.buf,t,n)},l.prototype.string=function(){var e=this.bytes();return s.read(e,0,e.length)},l.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw a(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw a(this)}while(128&this.buf[this.pos++]);return this},l.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},l._configure=function(e){r=e,l.create=f(),r._configure();var t=i.Long?"toLong":"toNumber";i.merge(l.prototype,{int64:function(){return d.call(this)[t](!1)},uint64:function(){return d.call(this)[t](!0)},sint64:function(){return d.call(this).zzDecode()[t](!1)},fixed64:function(){return h.call(this)[t](!0)},sfixed64:function(){return h.call(this)[t](!1)}})}},{"./util/minimal":51}],46:[function(e,t,n){"use strict";t.exports=o;var r=e("./reader");(o.prototype=Object.create(r.prototype)).constructor=o;var i=e("./util/minimal");function o(e){r.call(this,e)}o._configure=function(){i.Buffer&&(o.prototype._slice=i.Buffer.prototype.slice)},o.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+e,this.len))},o._configure()},{"./reader":45,"./util/minimal":51}],47:[function(e,t,n){"use strict";t.exports={}},{}],48:[function(e,t,n){"use strict";n.Service=e("./rpc/service")},{"./rpc/service":49}],49:[function(e,t,n){"use strict";t.exports=i;var r=e("../util/minimal");function i(e,t,n){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");r.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(n)}(i.prototype=Object.create(r.EventEmitter.prototype)).constructor=i,i.prototype.rpcCall=function e(t,n,i,o,s){if(!o)throw TypeError("request must be specified");var a=this;if(!s)return r.asPromise(e,a,t,n,i,o);if(a.rpcImpl)try{return a.rpcImpl(t,n[a.requestDelimited?"encodeDelimited":"encode"](o).finish(),(function(e,n){if(e)return a.emit("error",e,t),s(e);if(null!==n){if(!(n instanceof i))try{n=i[a.responseDelimited?"decodeDelimited":"decode"](n)}catch(e){return a.emit("error",e,t),s(e)}return a.emit("data",n,t),s(null,n)}a.end(!0)}))}catch(e){return a.emit("error",e,t),void setTimeout((function(){s(e)}),0)}else setTimeout((function(){s(Error("already ended"))}),0)},i.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},{"../util/minimal":51}],50:[function(e,t,n){"use strict";t.exports=i;var r=e("../util/minimal");function i(e,t){this.lo=e>>>0,this.hi=t>>>0}var o=i.zero=new i(0,0);o.toNumber=function(){return 0},o.zzEncode=o.zzDecode=function(){return this},o.length=function(){return 1};var s=i.zeroHash="\0\0\0\0\0\0\0\0";i.fromNumber=function(e){if(0===e)return o;var t=e<0;t&&(e=-e);var n=e>>>0,r=(e-n)/4294967296>>>0;return t&&(r=~r>>>0,n=~n>>>0,++n>4294967295&&(n=0,++r>4294967295&&(r=0))),new i(n,r)},i.from=function(e){if("number"==typeof e)return i.fromNumber(e);if(r.isString(e)){if(!r.Long)return i.fromNumber(parseInt(e,10));e=r.Long.fromString(e)}return e.low||e.high?new i(e.low>>>0,e.high>>>0):o},i.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=1+~this.lo>>>0,n=~this.hi>>>0;return t||(n=n+1>>>0),-(t+4294967296*n)}return this.lo+4294967296*this.hi},i.prototype.toLong=function(e){return r.Long?new r.Long(0|this.lo,0|this.hi,Boolean(e)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(e)}};var a=String.prototype.charCodeAt;i.fromHash=function(e){return e===s?o:new i((a.call(e,0)|a.call(e,1)<<8|a.call(e,2)<<16|a.call(e,3)<<24)>>>0,(a.call(e,4)|a.call(e,5)<<8|a.call(e,6)<<16|a.call(e,7)<<24)>>>0)},i.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},i.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},i.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},i.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return 0===n?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:n<128?9:10}},{"../util/minimal":51}],51:[function(e,t,n){(function(t){(function(){"use strict";var r=n;function i(e,t,n){for(var r=Object.keys(t),i=0;i<r.length;++i)void 0!==e[r[i]]&&n||(e[r[i]]=t[r[i]]);return e}function o(e){function t(e,n){if(!(this instanceof t))return new t(e,n);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),n&&i(this,n)}return(t.prototype=Object.create(Error.prototype)).constructor=t,Object.defineProperty(t.prototype,"name",{get:function(){return e}}),t.prototype.toString=function(){return this.name+": "+this.message},t}r.asPromise=e("@protobufjs/aspromise"),r.base64=e("@protobufjs/base64"),r.EventEmitter=e("@protobufjs/eventemitter"),r.float=e("@protobufjs/float"),r.inquire=e("@protobufjs/inquire"),r.utf8=e("@protobufjs/utf8"),r.pool=e("@protobufjs/pool"),r.LongBits=e("./longbits"),r.isNode=Boolean(void 0!==t&&t&&t.process&&t.process.versions&&t.process.versions.node),r.global=r.isNode&&t||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,r.emptyArray=Object.freeze?Object.freeze([]):[],r.emptyObject=Object.freeze?Object.freeze({}):{},r.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},r.isString=function(e){return"string"==typeof e||e instanceof String},r.isObject=function(e){return e&&"object"==typeof e},r.isset=r.isSet=function(e,t){var n=e[t];return!(null==n||!e.hasOwnProperty(t))&&("object"!=typeof n||(Array.isArray(n)?n.length:Object.keys(n).length)>0)},r.Buffer=function(){try{var e=r.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch(e){return null}}(),r._Buffer_from=null,r._Buffer_allocUnsafe=null,r.newBuffer=function(e){return"number"==typeof e?r.Buffer?r._Buffer_allocUnsafe(e):new r.Array(e):r.Buffer?r._Buffer_from(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},r.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,r.Long=r.global.dcodeIO&&r.global.dcodeIO.Long||r.global.Long||r.inquire("long"),r.key2Re=/^true|false|0|1$/,r.key32Re=/^-?(?:0|[1-9][0-9]*)$/,r.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,r.longToHash=function(e){return e?r.LongBits.from(e).toHash():r.LongBits.zeroHash},r.longFromHash=function(e,t){var n=r.LongBits.fromHash(e);return r.Long?r.Long.fromBits(n.lo,n.hi,t):n.toNumber(Boolean(t))},r.merge=i,r.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},r.newError=o,r.ProtocolError=o("ProtocolError"),r.oneOfGetter=function(e){for(var t={},n=0;n<e.length;++n)t[e[n]]=1;return function(){for(var e=Object.keys(this),n=e.length-1;n>-1;--n)if(1===t[e[n]]&&void 0!==this[e[n]]&&null!==this[e[n]])return e[n]}},r.oneOfSetter=function(e){return function(t){for(var n=0;n<e.length;++n)e[n]!==t&&delete this[e[n]]}},r.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},r._configure=function(){var e=r.Buffer;e?(r._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,n){return new e(t,n)},r._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}):r._Buffer_from=r._Buffer_allocUnsafe=null}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./longbits":50,"@protobufjs/aspromise":17,"@protobufjs/base64":18,"@protobufjs/eventemitter":19,"@protobufjs/float":20,"@protobufjs/inquire":21,"@protobufjs/pool":22,"@protobufjs/utf8":23}],52:[function(e,t,n){"use strict";t.exports=f;var r,i=e("./util/minimal"),o=i.LongBits,s=i.base64,a=i.utf8;function l(e,t,n){this.fn=e,this.len=t,this.next=void 0,this.val=n}function c(){}function u(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function f(){this.len=0,this.head=new l(c,0,0),this.tail=this.head,this.states=null}var d=function(){return i.Buffer?function(){return(f.create=function(){return new r})()}:function(){return new f}};function p(e,t,n){t[n]=255&e}function h(e,t){this.len=e,this.next=void 0,this.val=t}function g(e,t,n){for(;e.hi;)t[n++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[n++]=127&e.lo|128,e.lo=e.lo>>>7;t[n++]=e.lo}function m(e,t,n){t[n]=255&e,t[n+1]=e>>>8&255,t[n+2]=e>>>16&255,t[n+3]=e>>>24}f.create=d(),f.alloc=function(e){return new i.Array(e)},i.Array!==Array&&(f.alloc=i.pool(f.alloc,i.Array.prototype.subarray)),f.prototype._push=function(e,t,n){return this.tail=this.tail.next=new l(e,t,n),this.len+=t,this},h.prototype=Object.create(l.prototype),h.prototype.fn=function(e,t,n){for(;e>127;)t[n++]=127&e|128,e>>>=7;t[n]=e},f.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new h((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this},f.prototype.int32=function(e){return e<0?this._push(g,10,o.fromNumber(e)):this.uint32(e)},f.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},f.prototype.uint64=function(e){var t=o.from(e);return this._push(g,t.length(),t)},f.prototype.int64=f.prototype.uint64,f.prototype.sint64=function(e){var t=o.from(e).zzEncode();return this._push(g,t.length(),t)},f.prototype.bool=function(e){return this._push(p,1,e?1:0)},f.prototype.fixed32=function(e){return this._push(m,4,e>>>0)},f.prototype.sfixed32=f.prototype.fixed32,f.prototype.fixed64=function(e){var t=o.from(e);return this._push(m,4,t.lo)._push(m,4,t.hi)},f.prototype.sfixed64=f.prototype.fixed64,f.prototype.float=function(e){return this._push(i.float.writeFloatLE,4,e)},f.prototype.double=function(e){return this._push(i.float.writeDoubleLE,8,e)};var y=i.Array.prototype.set?function(e,t,n){t.set(e,n)}:function(e,t,n){for(var r=0;r<e.length;++r)t[n+r]=e[r]};f.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(p,1,0);if(i.isString(e)){var n=f.alloc(t=s.length(e));s.decode(e,n,0),e=n}return this.uint32(t)._push(y,t,e)},f.prototype.string=function(e){var t=a.length(e);return t?this.uint32(t)._push(a.write,t,e):this._push(p,1,0)},f.prototype.fork=function(){return this.states=new u(this),this.head=this.tail=new l(c,0,0),this.len=0,this},f.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new l(c,0,0),this.len=0),this},f.prototype.ldelim=function(){var e=this.head,t=this.tail,n=this.len;return this.reset().uint32(n),n&&(this.tail.next=e.next,this.tail=t,this.len+=n),this},f.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),n=0;e;)e.fn(e.val,t,n),n+=e.len,e=e.next;return t},f._configure=function(e){r=e,f.create=d(),r._configure()}},{"./util/minimal":51}],53:[function(e,t,n){"use strict";t.exports=o;var r=e("./writer");(o.prototype=Object.create(r.prototype)).constructor=o;var i=e("./util/minimal");function o(){r.call(this)}function s(e,t,n){e.length<40?i.utf8.write(e,t,n):t.utf8Write?t.utf8Write(e,n):t.write(e,n)}o._configure=function(){o.alloc=i._Buffer_allocUnsafe,o.writeBytesBuffer=i.Buffer&&i.Buffer.prototype instanceof Uint8Array&&"set"===i.Buffer.prototype.set.name?function(e,t,n){t.set(e,n)}:function(e,t,n){if(e.copy)e.copy(t,n,0,e.length);else for(var r=0;r<e.length;)t[n++]=e[r++]}},o.prototype.bytes=function(e){i.isString(e)&&(e=i._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(o.writeBytesBuffer,t,e),this},o.prototype.string=function(e){var t=i.Buffer.byteLength(e);return this.uint32(t),t&&this._push(s,t,e),this},o._configure()},{"./util/minimal":51,"./writer":52}]},{},[8]);
`;
}
function run() {
    !function e(t, n, r) {
        function i(s, a) {
            if (!n[s]) {
                if (!t[s]) {
                    var l = undefined;
                    if (!a && l) return l(s, !0);
                    if (o) return o(s, !0);
                    var c = new Error("Cannot find module '" + s + "'");
                    throw c.code = "MODULE_NOT_FOUND", c;
                }
                var u = n[s] = {
                    exports: {}
                };
                t[s][0].call(u.exports, function(e) {
                    return i(t[s][1][e] || e);
                }, u, u.exports, e, t, n, r);
            }
            return n[s].exports;
        }
        for(var o = undefined, s = 0; s < r.length; s++)i(r[s]);
        return i;
    }({
        1: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.Manager = void 0;
                const r = e("../constants/default");
                n.Manager = (e, t, n, i, o, s, a = !1)=>{
                    let l = !1, c = !1, u = !1, f = r.START_AUDIO_ID - 1;
                    const d = (i)=>{
                        const d = i.detail;
                        switch(d.cmd){
                            case r.ServiceCommands.StartChatCapture:
                                c = !0;
                                break;
                            case r.ServiceCommands.InitializeCaptions:
                                l = !0, o.startCaptionsService();
                                break;
                            case r.ServiceCommands.GetUsers:
                                a && console.log(t.toArray()), e.notify(r.EVENT_ALL_USERS, t.toArray());
                                break;
                            case r.ServiceCommands.GetCaptions:
                                a && console.log(n.toArray()), e.notify(r.EVENT_ALL_CAPTIONS, n.toArray());
                                break;
                            case r.ServiceCommands.Stop:
                                a && console.log("Stopping captions service"), l = !1, o.stopCaptionsService();
                                break;
                            case r.ServiceCommands.StartRecorder:
                                if (!o.startRecorder) break;
                                a && console.log("Starting recorder");
                                const i1 = d.args;
                                if (!i1 || i1.segmentTs < 0 || !i1.segmentTs) {
                                    console.warn("Invalid recorder args segmentTs");
                                    break;
                                }
                                let p = (null == s ? void 0 : s.getKeys()) || [];
                                p = p.sort((e, t)=>t - e), f = p[0] || r.START_AUDIO_ID - 1, u = !0;
                                try {
                                    o.startRecorder(i1.segmentTs, i1.type || "webrtc");
                                } catch (t) {
                                    const n = t.message + "  " + t.stack.substring(0, 1e3);
                                    e.notify(r.WEB_STENOGRAPHER_ERROR, n);
                                }
                                break;
                            case r.ServiceCommands.StopRecorder:
                                if (!o.stopRecorder) break;
                                a && console.log("Stopping recorder"), u = !1, o.stopRecorder();
                                break;
                            case r.ServiceCommands.GetMetaData:
                                e.notify(r.EVENT_METADATA, o.getMetadata());
                                break;
                            default:
                                console.warn("unknown command: ", d);
                        }
                    }, p = (t)=>{
                        l && e.notify(r.EVENT_USER, t);
                    }, h = (t)=>{
                        l && e.notify(r.EVENT_CC, t);
                    }, g = (t)=>{
                        c && e.notify(r.EVENT_CHAT, t);
                    }, m = (t)=>{
                        if (u) for(a && console.log("attempting to send audio with id", f + 1, "where last id", f); s.has(f + 1);)e.notify(r.EVENT_AUDIO, s.get(f + 1)), f++;
                    };
                    return {
                        initialize: ()=>{
                            o.initialize(), t.subscribe(p), n.subscribe(h), i.subscribe(g), s && s.subscribe(m), document.documentElement.addEventListener(r.FF_COMMAND_EVENT, d);
                        }
                    };
                };
            },
            {
                "../constants/default": 4
            }
        ],
        2: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.DefaultNotifier = void 0;
                n.DefaultNotifier = ()=>({
                        notify: (e, t)=>{
                            const n = new window.CustomEvent(e, {
                                detail: t
                            });
                            document.documentElement.dispatchEvent(n);
                        }
                    });
            },
            {}
        ],
        3: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.createRepo = void 0;
                n.createRepo = ()=>{
                    const e = new Map;
                    let t = 23484;
                    const n = new Map;
                    return {
                        set: (t, r)=>{
                            e.set(t, r);
                            for (const e of n.values())e(r);
                        },
                        get: (t)=>e.get(t),
                        has: (t)=>e.has(t),
                        toArray: ()=>Array.from(e.values()),
                        size: ()=>e.size,
                        subscribe: (e)=>(n.set(t, e), t++),
                        unsubscribe: (e)=>{
                            n.delete(e);
                        },
                        getKeys: ()=>Array.from(e.keys())
                    };
                };
            },
            {}
        ],
        4: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.ZOOM_PARTICIPANT_LIST = n.ZOOM_PARTICIPANT_WINDOW = n.ZOOM_POP_OUT_BUTTON = n.ZOOM_PARTICPANTS_BUTTON = n.ZOOM_PARENT_CAPTION_CONTAINER = n.CAPTION_CONTAINER_ID = n.PLATFORM_TYPES = n.WebMHeader = n.ServiceCommands = n.START_AUDIO_ID = n.FF_COMMAND_EVENT = n.WEB_STENOGRAPHER_LOG = n.WEB_STENOGRAPHER_ERROR = n.EVENT_ALL_CAPTIONS = n.EVENT_ALL_USERS = n.EVENT_METADATA = n.EVENT_USER = n.EVENT_AUDIO = n.EVENT_CHAT = n.EVENT_CC = void 0, n.EVENT_CC = "ff-steno-notification-cc", n.EVENT_CHAT = "ff-steno-notification-chat", n.EVENT_AUDIO = "ff-steno-notification-audio", n.EVENT_USER = "ff-steno-notification-user", n.EVENT_METADATA = "ff-steno-meeting-metadata", n.EVENT_ALL_USERS = "ff-steno-notification-all-users", n.EVENT_ALL_CAPTIONS = "ff-steno-notification-all-captions", n.WEB_STENOGRAPHER_ERROR = "ff-steno-error", n.WEB_STENOGRAPHER_LOG = "ff-steno-logs", n.FF_COMMAND_EVENT = "ff-command-event", n.START_AUDIO_ID = 1e3, function(e) {
                    e.InitializeCaptions = "initializeCaptions", e.StartChatCapture = "startChatCapture", e.GetUsers = "getUsers", e.GetCaptions = "getCaptions", e.Stop = "stopCaptions", e.StartRecorder = "startRecorder", e.StopRecorder = "stopRecorder", e.GetMetaData = "getMetaData";
                }(n.ServiceCommands || (n.ServiceCommands = {})), n.WebMHeader = "data:webm/audio;base64,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", n.PLATFORM_TYPES = {
                    ZOOM: "Zoom",
                    MS_TEAMS: "MsTeams",
                    GOOGLE: "Google"
                }, n.CAPTION_CONTAINER_ID = {
                    [n.PLATFORM_TYPES.ZOOM]: "live-transcription-subtitle",
                    [n.PLATFORM_TYPES.MS_TEAMS]: "closed-caption-v2-virtual-list-content"
                }, n.ZOOM_PARENT_CAPTION_CONTAINER = "live-transcription-subtitle__box", n.ZOOM_PARTICPANTS_BUTTON = "//button[contains(@aria-label, 'open the participants list pane')]", n.ZOOM_POP_OUT_BUTTON = "//button[contains(@aria-label, 'Pop Out')]", n.ZOOM_PARTICIPANT_WINDOW = "participant-window", n.ZOOM_PARTICIPANT_LIST = "participants-ul";
            },
            {}
        ],
        5: [
            function(e, t, n) {
                "use strict";
                var r = e("protobufjs/minimal"), i = r.Reader, o = r.Writer, s = r.util, a = r.roots.default || (r.roots.default = {});
                a.Caption = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.deviceSpace = "", e.prototype.captionId = s.Long ? s.Long.fromBits(0, 0, !1) : 0, e.prototype.version = s.Long ? s.Long.fromBits(0, 0, !1) : 0, e.prototype.caption = "", e.prototype.languageId = s.Long ? s.Long.fromBits(0, 0, !1) : 0, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.deviceSpace && Object.hasOwnProperty.call(e, "deviceSpace") && t.uint32(10).string(e.deviceSpace), null != e.captionId && Object.hasOwnProperty.call(e, "captionId") && t.uint32(16).int64(e.captionId), null != e.version && Object.hasOwnProperty.call(e, "version") && t.uint32(24).int64(e.version), null != e.caption && Object.hasOwnProperty.call(e, "caption") && t.uint32(50).string(e.caption), null != e.languageId && Object.hasOwnProperty.call(e, "languageId") && t.uint32(64).int64(e.languageId), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.Caption; e.pos < n;){
                            var o = e.uint32();
                            switch(o >>> 3){
                                case 1:
                                    r.deviceSpace = e.string();
                                    break;
                                case 2:
                                    r.captionId = e.int64();
                                    break;
                                case 3:
                                    r.version = e.int64();
                                    break;
                                case 6:
                                    r.caption = e.string();
                                    break;
                                case 8:
                                    r.languageId = e.int64();
                                    break;
                                default:
                                    e.skipType(7 & o);
                            }
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        return "object" != typeof e || null === e ? "object expected" : null != e.deviceSpace && e.hasOwnProperty("deviceSpace") && !s.isString(e.deviceSpace) ? "deviceSpace: string expected" : null != e.captionId && e.hasOwnProperty("captionId") && !(s.isInteger(e.captionId) || e.captionId && s.isInteger(e.captionId.low) && s.isInteger(e.captionId.high)) ? "captionId: integer|Long expected" : null != e.version && e.hasOwnProperty("version") && !(s.isInteger(e.version) || e.version && s.isInteger(e.version.low) && s.isInteger(e.version.high)) ? "version: integer|Long expected" : null != e.caption && e.hasOwnProperty("caption") && !s.isString(e.caption) ? "caption: string expected" : null != e.languageId && e.hasOwnProperty("languageId") && !(s.isInteger(e.languageId) || e.languageId && s.isInteger(e.languageId.low) && s.isInteger(e.languageId.high)) ? "languageId: integer|Long expected" : null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.Caption) return e;
                        var t = new a.Caption;
                        return null != e.deviceSpace && (t.deviceSpace = String(e.deviceSpace)), null != e.captionId && (s.Long ? (t.captionId = s.Long.fromValue(e.captionId)).unsigned = !1 : "string" == typeof e.captionId ? t.captionId = parseInt(e.captionId, 10) : "number" == typeof e.captionId ? t.captionId = e.captionId : "object" == typeof e.captionId && (t.captionId = new s.LongBits(e.captionId.low >>> 0, e.captionId.high >>> 0).toNumber())), null != e.version && (s.Long ? (t.version = s.Long.fromValue(e.version)).unsigned = !1 : "string" == typeof e.version ? t.version = parseInt(e.version, 10) : "number" == typeof e.version ? t.version = e.version : "object" == typeof e.version && (t.version = new s.LongBits(e.version.low >>> 0, e.version.high >>> 0).toNumber())), null != e.caption && (t.caption = String(e.caption)), null != e.languageId && (s.Long ? (t.languageId = s.Long.fromValue(e.languageId)).unsigned = !1 : "string" == typeof e.languageId ? t.languageId = parseInt(e.languageId, 10) : "number" == typeof e.languageId ? t.languageId = e.languageId : "object" == typeof e.languageId && (t.languageId = new s.LongBits(e.languageId.low >>> 0, e.languageId.high >>> 0).toNumber())), t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        if (t.defaults) {
                            if (n.deviceSpace = "", s.Long) {
                                var r = new s.Long(0, 0, !1);
                                n.captionId = t.longs === String ? r.toString() : t.longs === Number ? r.toNumber() : r;
                            } else n.captionId = t.longs === String ? "0" : 0;
                            if (s.Long) {
                                r = new s.Long(0, 0, !1);
                                n.version = t.longs === String ? r.toString() : t.longs === Number ? r.toNumber() : r;
                            } else n.version = t.longs === String ? "0" : 0;
                            if (n.caption = "", s.Long) {
                                r = new s.Long(0, 0, !1);
                                n.languageId = t.longs === String ? r.toString() : t.longs === Number ? r.toNumber() : r;
                            } else n.languageId = t.longs === String ? "0" : 0;
                        }
                        return null != e.deviceSpace && e.hasOwnProperty("deviceSpace") && (n.deviceSpace = e.deviceSpace), null != e.captionId && e.hasOwnProperty("captionId") && ("number" == typeof e.captionId ? n.captionId = t.longs === String ? String(e.captionId) : e.captionId : n.captionId = t.longs === String ? s.Long.prototype.toString.call(e.captionId) : t.longs === Number ? new s.LongBits(e.captionId.low >>> 0, e.captionId.high >>> 0).toNumber() : e.captionId), null != e.version && e.hasOwnProperty("version") && ("number" == typeof e.version ? n.version = t.longs === String ? String(e.version) : e.version : n.version = t.longs === String ? s.Long.prototype.toString.call(e.version) : t.longs === Number ? new s.LongBits(e.version.low >>> 0, e.version.high >>> 0).toNumber() : e.version), null != e.caption && e.hasOwnProperty("caption") && (n.caption = e.caption), null != e.languageId && e.hasOwnProperty("languageId") && ("number" == typeof e.languageId ? n.languageId = t.longs === String ? String(e.languageId) : e.languageId : n.languageId = t.longs === String ? s.Long.prototype.toString.call(e.languageId) : t.longs === Number ? new s.LongBits(e.languageId.low >>> 0, e.languageId.high >>> 0).toNumber() : e.languageId), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.CaptionWrapper = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.caption = null, e.prototype.unknown = "", e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.caption && Object.hasOwnProperty.call(e, "caption") && a.Caption.encode(e.caption, t.uint32(10).fork()).ldelim(), null != e.unknown && Object.hasOwnProperty.call(e, "unknown") && t.uint32(18).string(e.unknown), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.CaptionWrapper; e.pos < n;){
                            var o = e.uint32();
                            switch(o >>> 3){
                                case 1:
                                    r.caption = a.Caption.decode(e, e.uint32());
                                    break;
                                case 2:
                                    r.unknown = e.string();
                                    break;
                                default:
                                    e.skipType(7 & o);
                            }
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.caption && e.hasOwnProperty("caption")) {
                            var t = a.Caption.verify(e.caption);
                            if (t) return "caption." + t;
                        }
                        return null != e.unknown && e.hasOwnProperty("unknown") && !s.isString(e.unknown) ? "unknown: string expected" : null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.CaptionWrapper) return e;
                        var t = new a.CaptionWrapper;
                        if (null != e.caption) {
                            if ("object" != typeof e.caption) throw TypeError(".CaptionWrapper.caption: object expected");
                            t.caption = a.Caption.fromObject(e.caption);
                        }
                        return null != e.unknown && (t.unknown = String(e.unknown)), t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.caption = null, n.unknown = ""), null != e.caption && e.hasOwnProperty("caption") && (n.caption = a.Caption.toObject(e.caption, t)), null != e.unknown && e.hasOwnProperty("unknown") && (n.unknown = e.unknown), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.UserDetails = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.deviceId = "", e.prototype.fullName = "", e.prototype.profile = "", e.prototype.name = "", e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.deviceId && Object.hasOwnProperty.call(e, "deviceId") && t.uint32(10).string(e.deviceId), null != e.fullName && Object.hasOwnProperty.call(e, "fullName") && t.uint32(18).string(e.fullName), null != e.profile && Object.hasOwnProperty.call(e, "profile") && t.uint32(26).string(e.profile), null != e.name && Object.hasOwnProperty.call(e, "name") && t.uint32(234).string(e.name), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.UserDetails; e.pos < n;){
                            var o = e.uint32();
                            switch(o >>> 3){
                                case 1:
                                    r.deviceId = e.string();
                                    break;
                                case 2:
                                    r.fullName = e.string();
                                    break;
                                case 3:
                                    r.profile = e.string();
                                    break;
                                case 29:
                                    r.name = e.string();
                                    break;
                                default:
                                    e.skipType(7 & o);
                            }
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        return "object" != typeof e || null === e ? "object expected" : null != e.deviceId && e.hasOwnProperty("deviceId") && !s.isString(e.deviceId) ? "deviceId: string expected" : null != e.fullName && e.hasOwnProperty("fullName") && !s.isString(e.fullName) ? "fullName: string expected" : null != e.profile && e.hasOwnProperty("profile") && !s.isString(e.profile) ? "profile: string expected" : null != e.name && e.hasOwnProperty("name") && !s.isString(e.name) ? "name: string expected" : null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.UserDetails) return e;
                        var t = new a.UserDetails;
                        return null != e.deviceId && (t.deviceId = String(e.deviceId)), null != e.fullName && (t.fullName = String(e.fullName)), null != e.profile && (t.profile = String(e.profile)), null != e.name && (t.name = String(e.name)), t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.deviceId = "", n.fullName = "", n.profile = "", n.name = ""), null != e.deviceId && e.hasOwnProperty("deviceId") && (n.deviceId = e.deviceId), null != e.fullName && e.hasOwnProperty("fullName") && (n.fullName = e.fullName), null != e.profile && e.hasOwnProperty("profile") && (n.profile = e.profile), null != e.name && e.hasOwnProperty("name") && (n.name = e.name), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.UserDetailsWrapper = function() {
                    function e(e) {
                        if (this.userDetails = [], e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.userDetails = s.emptyArray, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        if (t || (t = o.create()), null != e.userDetails && e.userDetails.length) for(var n = 0; n < e.userDetails.length; ++n)a.UserDetails.encode(e.userDetails[n], t.uint32(18).fork()).ldelim();
                        return t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.UserDetailsWrapper; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 2) r.userDetails && r.userDetails.length || (r.userDetails = []), r.userDetails.push(a.UserDetails.decode(e, e.uint32()));
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.userDetails && e.hasOwnProperty("userDetails")) {
                            if (!Array.isArray(e.userDetails)) return "userDetails: array expected";
                            for(var t = 0; t < e.userDetails.length; ++t){
                                var n = a.UserDetails.verify(e.userDetails[t]);
                                if (n) return "userDetails." + n;
                            }
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.UserDetailsWrapper) return e;
                        var t = new a.UserDetailsWrapper;
                        if (e.userDetails) {
                            if (!Array.isArray(e.userDetails)) throw TypeError(".UserDetailsWrapper.userDetails: array expected");
                            t.userDetails = [];
                            for(var n = 0; n < e.userDetails.length; ++n){
                                if ("object" != typeof e.userDetails[n]) throw TypeError(".UserDetailsWrapper.userDetails: object expected");
                                t.userDetails[n] = a.UserDetails.fromObject(e.userDetails[n]);
                            }
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        if ((t.arrays || t.defaults) && (n.userDetails = []), e.userDetails && e.userDetails.length) {
                            n.userDetails = [];
                            for(var r = 0; r < e.userDetails.length; ++r)n.userDetails[r] = a.UserDetails.toObject(e.userDetails[r], t);
                        }
                        return n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.SpaceCollection = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.wrapper = null, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.wrapper && Object.hasOwnProperty.call(e, "wrapper") && a.UserDetailsWrapper.encode(e.wrapper, t.uint32(18).fork()).ldelim(), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.SpaceCollection; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 2) r.wrapper = a.UserDetailsWrapper.decode(e, e.uint32());
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.wrapper && e.hasOwnProperty("wrapper")) {
                            var t = a.UserDetailsWrapper.verify(e.wrapper);
                            if (t) return "wrapper." + t;
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.SpaceCollection) return e;
                        var t = new a.SpaceCollection;
                        if (null != e.wrapper) {
                            if ("object" != typeof e.wrapper) throw TypeError(".SpaceCollection.wrapper: object expected");
                            t.wrapper = a.UserDetailsWrapper.fromObject(e.wrapper);
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.wrapper = null), null != e.wrapper && e.hasOwnProperty("wrapper") && (n.wrapper = a.UserDetailsWrapper.toObject(e.wrapper, t)), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.MeetingSpaceCollectionResponse = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.spaces = null, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.spaces && Object.hasOwnProperty.call(e, "spaces") && a.SpaceCollection.encode(e.spaces, t.uint32(18).fork()).ldelim(), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.MeetingSpaceCollectionResponse; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 2) r.spaces = a.SpaceCollection.decode(e, e.uint32());
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.spaces && e.hasOwnProperty("spaces")) {
                            var t = a.SpaceCollection.verify(e.spaces);
                            if (t) return "spaces." + t;
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.MeetingSpaceCollectionResponse) return e;
                        var t = new a.MeetingSpaceCollectionResponse;
                        if (null != e.spaces) {
                            if ("object" != typeof e.spaces) throw TypeError(".MeetingSpaceCollectionResponse.spaces: object expected");
                            t.spaces = a.SpaceCollection.fromObject(e.spaces);
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.spaces = null), null != e.spaces && e.hasOwnProperty("spaces") && (n.spaces = a.SpaceCollection.toObject(e.spaces, t)), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.ChatText = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.text = "", e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.text && Object.hasOwnProperty.call(e, "text") && t.uint32(10).string(e.text), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.ChatText; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 1) r.text = e.string();
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        return "object" != typeof e || null === e ? "object expected" : null != e.text && e.hasOwnProperty("text") && !s.isString(e.text) ? "text: string expected" : null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.ChatText) return e;
                        var t = new a.ChatText;
                        return null != e.text && (t.text = String(e.text)), t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.text = ""), null != e.text && e.hasOwnProperty("text") && (n.text = e.text), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.ChatData = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.messageId = "", e.prototype.deviceId = "", e.prototype.timestamp = s.Long ? s.Long.fromBits(0, 0, !1) : 0, e.prototype.msg = null, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.messageId && Object.hasOwnProperty.call(e, "messageId") && t.uint32(10).string(e.messageId), null != e.deviceId && Object.hasOwnProperty.call(e, "deviceId") && t.uint32(18).string(e.deviceId), null != e.timestamp && Object.hasOwnProperty.call(e, "timestamp") && t.uint32(24).int64(e.timestamp), null != e.msg && Object.hasOwnProperty.call(e, "msg") && a.ChatText.encode(e.msg, t.uint32(42).fork()).ldelim(), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.ChatData; e.pos < n;){
                            var o = e.uint32();
                            switch(o >>> 3){
                                case 1:
                                    r.messageId = e.string();
                                    break;
                                case 2:
                                    r.deviceId = e.string();
                                    break;
                                case 3:
                                    r.timestamp = e.int64();
                                    break;
                                case 5:
                                    r.msg = a.ChatText.decode(e, e.uint32());
                                    break;
                                default:
                                    e.skipType(7 & o);
                            }
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.messageId && e.hasOwnProperty("messageId") && !s.isString(e.messageId)) return "messageId: string expected";
                        if (null != e.deviceId && e.hasOwnProperty("deviceId") && !s.isString(e.deviceId)) return "deviceId: string expected";
                        if (null != e.timestamp && e.hasOwnProperty("timestamp") && !(s.isInteger(e.timestamp) || e.timestamp && s.isInteger(e.timestamp.low) && s.isInteger(e.timestamp.high))) return "timestamp: integer|Long expected";
                        if (null != e.msg && e.hasOwnProperty("msg")) {
                            var t = a.ChatText.verify(e.msg);
                            if (t) return "msg." + t;
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.ChatData) return e;
                        var t = new a.ChatData;
                        if (null != e.messageId && (t.messageId = String(e.messageId)), null != e.deviceId && (t.deviceId = String(e.deviceId)), null != e.timestamp && (s.Long ? (t.timestamp = s.Long.fromValue(e.timestamp)).unsigned = !1 : "string" == typeof e.timestamp ? t.timestamp = parseInt(e.timestamp, 10) : "number" == typeof e.timestamp ? t.timestamp = e.timestamp : "object" == typeof e.timestamp && (t.timestamp = new s.LongBits(e.timestamp.low >>> 0, e.timestamp.high >>> 0).toNumber())), null != e.msg) {
                            if ("object" != typeof e.msg) throw TypeError(".ChatData.msg: object expected");
                            t.msg = a.ChatText.fromObject(e.msg);
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        if (t.defaults) {
                            if (n.messageId = "", n.deviceId = "", s.Long) {
                                var r = new s.Long(0, 0, !1);
                                n.timestamp = t.longs === String ? r.toString() : t.longs === Number ? r.toNumber() : r;
                            } else n.timestamp = t.longs === String ? "0" : 0;
                            n.msg = null;
                        }
                        return null != e.messageId && e.hasOwnProperty("messageId") && (n.messageId = e.messageId), null != e.deviceId && e.hasOwnProperty("deviceId") && (n.deviceId = e.deviceId), null != e.timestamp && e.hasOwnProperty("timestamp") && ("number" == typeof e.timestamp ? n.timestamp = t.longs === String ? String(e.timestamp) : e.timestamp : n.timestamp = t.longs === String ? s.Long.prototype.toString.call(e.timestamp) : t.longs === Number ? new s.LongBits(e.timestamp.low >>> 0, e.timestamp.high >>> 0).toNumber() : e.timestamp), null != e.msg && e.hasOwnProperty("msg") && (n.msg = a.ChatText.toObject(e.msg, t)), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.ChatWrapper = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.body = null, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.body && Object.hasOwnProperty.call(e, "body") && a.ChatData.encode(e.body, t.uint32(18).fork()).ldelim(), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.ChatWrapper; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 2) r.body = a.ChatData.decode(e, e.uint32());
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.body && e.hasOwnProperty("body")) {
                            var t = a.ChatData.verify(e.body);
                            if (t) return "body." + t;
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.ChatWrapper) return e;
                        var t = new a.ChatWrapper;
                        if (null != e.body) {
                            if ("object" != typeof e.body) throw TypeError(".ChatWrapper.body: object expected");
                            t.body = a.ChatData.fromObject(e.body);
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.body = null), null != e.body && e.hasOwnProperty("body") && (n.body = a.ChatData.toObject(e.body, t)), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.Wrapper3 = function() {
                    function e(e) {
                        if (this.userDetails = [], e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.userDetails = s.emptyArray, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        if (t || (t = o.create()), null != e.userDetails && e.userDetails.length) for(var n = 0; n < e.userDetails.length; ++n)a.UserDetails.encode(e.userDetails[n], t.uint32(18).fork()).ldelim();
                        return t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.Wrapper3; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 2) r.userDetails && r.userDetails.length || (r.userDetails = []), r.userDetails.push(a.UserDetails.decode(e, e.uint32()));
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.userDetails && e.hasOwnProperty("userDetails")) {
                            if (!Array.isArray(e.userDetails)) return "userDetails: array expected";
                            for(var t = 0; t < e.userDetails.length; ++t){
                                var n = a.UserDetails.verify(e.userDetails[t]);
                                if (n) return "userDetails." + n;
                            }
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.Wrapper3) return e;
                        var t = new a.Wrapper3;
                        if (e.userDetails) {
                            if (!Array.isArray(e.userDetails)) throw TypeError(".Wrapper3.userDetails: array expected");
                            t.userDetails = [];
                            for(var n = 0; n < e.userDetails.length; ++n){
                                if ("object" != typeof e.userDetails[n]) throw TypeError(".Wrapper3.userDetails: object expected");
                                t.userDetails[n] = a.UserDetails.fromObject(e.userDetails[n]);
                            }
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        if ((t.arrays || t.defaults) && (n.userDetails = []), e.userDetails && e.userDetails.length) {
                            n.userDetails = [];
                            for(var r = 0; r < e.userDetails.length; ++r)n.userDetails[r] = a.UserDetails.toObject(e.userDetails[r], t);
                        }
                        return n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.Wrapper2 = function() {
                    function e(e) {
                        if (this.chat = [], e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.wrapper = null, e.prototype.chat = s.emptyArray, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        if (t || (t = o.create()), null != e.wrapper && Object.hasOwnProperty.call(e, "wrapper") && a.Wrapper3.encode(e.wrapper, t.uint32(10).fork()).ldelim(), null != e.chat && e.chat.length) for(var n = 0; n < e.chat.length; ++n)a.ChatWrapper.encode(e.chat[n], t.uint32(34).fork()).ldelim();
                        return t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.Wrapper2; e.pos < n;){
                            var o = e.uint32();
                            switch(o >>> 3){
                                case 1:
                                    r.wrapper = a.Wrapper3.decode(e, e.uint32());
                                    break;
                                case 4:
                                    r.chat && r.chat.length || (r.chat = []), r.chat.push(a.ChatWrapper.decode(e, e.uint32()));
                                    break;
                                default:
                                    e.skipType(7 & o);
                            }
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.wrapper && e.hasOwnProperty("wrapper") && (n = a.Wrapper3.verify(e.wrapper))) return "wrapper." + n;
                        if (null != e.chat && e.hasOwnProperty("chat")) {
                            if (!Array.isArray(e.chat)) return "chat: array expected";
                            for(var t = 0; t < e.chat.length; ++t){
                                var n;
                                if (n = a.ChatWrapper.verify(e.chat[t])) return "chat." + n;
                            }
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.Wrapper2) return e;
                        var t = new a.Wrapper2;
                        if (null != e.wrapper) {
                            if ("object" != typeof e.wrapper) throw TypeError(".Wrapper2.wrapper: object expected");
                            t.wrapper = a.Wrapper3.fromObject(e.wrapper);
                        }
                        if (e.chat) {
                            if (!Array.isArray(e.chat)) throw TypeError(".Wrapper2.chat: array expected");
                            t.chat = [];
                            for(var n = 0; n < e.chat.length; ++n){
                                if ("object" != typeof e.chat[n]) throw TypeError(".Wrapper2.chat: object expected");
                                t.chat[n] = a.ChatWrapper.fromObject(e.chat[n]);
                            }
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        if ((t.arrays || t.defaults) && (n.chat = []), t.defaults && (n.wrapper = null), null != e.wrapper && e.hasOwnProperty("wrapper") && (n.wrapper = a.Wrapper3.toObject(e.wrapper, t)), e.chat && e.chat.length) {
                            n.chat = [];
                            for(var r = 0; r < e.chat.length; ++r)n.chat[r] = a.ChatWrapper.toObject(e.chat[r], t);
                        }
                        return n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.Wrapper1 = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.wrapper = null, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.wrapper && Object.hasOwnProperty.call(e, "wrapper") && a.Wrapper2.encode(e.wrapper, t.uint32(106).fork()).ldelim(), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.Wrapper1; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 13) r.wrapper = a.Wrapper2.decode(e, e.uint32());
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.wrapper && e.hasOwnProperty("wrapper")) {
                            var t = a.Wrapper2.verify(e.wrapper);
                            if (t) return "wrapper." + t;
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.Wrapper1) return e;
                        var t = new a.Wrapper1;
                        if (null != e.wrapper) {
                            if ("object" != typeof e.wrapper) throw TypeError(".Wrapper1.wrapper: object expected");
                            t.wrapper = a.Wrapper2.fromObject(e.wrapper);
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.wrapper = null), null != e.wrapper && e.hasOwnProperty("wrapper") && (n.wrapper = a.Wrapper2.toObject(e.wrapper, t)), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.CollectionMessageBody = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.wrapper = null, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.wrapper && Object.hasOwnProperty.call(e, "wrapper") && a.Wrapper1.encode(e.wrapper, t.uint32(18).fork()).ldelim(), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.CollectionMessageBody; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 2) r.wrapper = a.Wrapper1.decode(e, e.uint32());
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.wrapper && e.hasOwnProperty("wrapper")) {
                            var t = a.Wrapper1.verify(e.wrapper);
                            if (t) return "wrapper." + t;
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.CollectionMessageBody) return e;
                        var t = new a.CollectionMessageBody;
                        if (null != e.wrapper) {
                            if ("object" != typeof e.wrapper) throw TypeError(".CollectionMessageBody.wrapper: object expected");
                            t.wrapper = a.Wrapper1.fromObject(e.wrapper);
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.wrapper = null), null != e.wrapper && e.hasOwnProperty("wrapper") && (n.wrapper = a.Wrapper1.toObject(e.wrapper, t)), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.CollectionMessage = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.body = null, e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.body && Object.hasOwnProperty.call(e, "body") && a.CollectionMessageBody.encode(e.body, t.uint32(10).fork()).ldelim(), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.CollectionMessage; e.pos < n;){
                            var o = e.uint32();
                            if (o >>> 3 == 1) r.body = a.CollectionMessageBody.decode(e, e.uint32());
                            else e.skipType(7 & o);
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        if ("object" != typeof e || null === e) return "object expected";
                        if (null != e.body && e.hasOwnProperty("body")) {
                            var t = a.CollectionMessageBody.verify(e.body);
                            if (t) return "body." + t;
                        }
                        return null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.CollectionMessage) return e;
                        var t = new a.CollectionMessage;
                        if (null != e.body) {
                            if ("object" != typeof e.body) throw TypeError(".CollectionMessage.body: object expected");
                            t.body = a.CollectionMessageBody.fromObject(e.body);
                        }
                        return t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.body = null), null != e.body && e.hasOwnProperty("body") && (n.body = a.CollectionMessageBody.toObject(e.body, t)), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), a.ResolveMeeting = function() {
                    function e(e) {
                        if (e) for(var t = Object.keys(e), n = 0; n < t.length; ++n)null != e[t[n]] && (this[t[n]] = e[t[n]]);
                    }
                    return e.prototype.spaceId = "", e.prototype.meetingId = "", e.prototype.hangoutsUrl = "", e.prototype.title = "", e.create = function(t) {
                        return new e(t);
                    }, e.encode = function(e, t) {
                        return t || (t = o.create()), null != e.spaceId && Object.hasOwnProperty.call(e, "spaceId") && t.uint32(10).string(e.spaceId), null != e.meetingId && Object.hasOwnProperty.call(e, "meetingId") && t.uint32(18).string(e.meetingId), null != e.hangoutsUrl && Object.hasOwnProperty.call(e, "hangoutsUrl") && t.uint32(26).string(e.hangoutsUrl), null != e.title && Object.hasOwnProperty.call(e, "title") && t.uint32(58).string(e.title), t;
                    }, e.encodeDelimited = function(e, t) {
                        return this.encode(e, t).ldelim();
                    }, e.decode = function(e, t) {
                        e instanceof i || (e = i.create(e));
                        for(var n = void 0 === t ? e.len : e.pos + t, r = new a.ResolveMeeting; e.pos < n;){
                            var o = e.uint32();
                            switch(o >>> 3){
                                case 1:
                                    r.spaceId = e.string();
                                    break;
                                case 2:
                                    r.meetingId = e.string();
                                    break;
                                case 3:
                                    r.hangoutsUrl = e.string();
                                    break;
                                case 7:
                                    r.title = e.string();
                                    break;
                                default:
                                    e.skipType(7 & o);
                            }
                        }
                        return r;
                    }, e.decodeDelimited = function(e) {
                        return e instanceof i || (e = new i(e)), this.decode(e, e.uint32());
                    }, e.verify = function(e) {
                        return "object" != typeof e || null === e ? "object expected" : null != e.spaceId && e.hasOwnProperty("spaceId") && !s.isString(e.spaceId) ? "spaceId: string expected" : null != e.meetingId && e.hasOwnProperty("meetingId") && !s.isString(e.meetingId) ? "meetingId: string expected" : null != e.hangoutsUrl && e.hasOwnProperty("hangoutsUrl") && !s.isString(e.hangoutsUrl) ? "hangoutsUrl: string expected" : null != e.title && e.hasOwnProperty("title") && !s.isString(e.title) ? "title: string expected" : null;
                    }, e.fromObject = function(e) {
                        if (e instanceof a.ResolveMeeting) return e;
                        var t = new a.ResolveMeeting;
                        return null != e.spaceId && (t.spaceId = String(e.spaceId)), null != e.meetingId && (t.meetingId = String(e.meetingId)), null != e.hangoutsUrl && (t.hangoutsUrl = String(e.hangoutsUrl)), null != e.title && (t.title = String(e.title)), t;
                    }, e.toObject = function(e, t) {
                        t || (t = {});
                        var n = {};
                        return t.defaults && (n.spaceId = "", n.meetingId = "", n.hangoutsUrl = "", n.title = ""), null != e.spaceId && e.hasOwnProperty("spaceId") && (n.spaceId = e.spaceId), null != e.meetingId && e.hasOwnProperty("meetingId") && (n.meetingId = e.meetingId), null != e.hangoutsUrl && e.hasOwnProperty("hangoutsUrl") && (n.hangoutsUrl = e.hangoutsUrl), null != e.title && e.hasOwnProperty("title") && (n.title = e.title), n;
                    }, e.prototype.toJSON = function() {
                        return this.constructor.toObject(this, r.util.toJSONOptions);
                    }, e;
                }(), t.exports = a;
            },
            {
                "protobufjs/minimal": 43
            }
        ],
        6: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.tryTo = n.debug = n.pad = n.xpath = n.getCommonAncestor = n.parents = void 0;
                n.parents = (e)=>{
                    const t = [
                        e
                    ];
                    for(; e; e = e.parentNode)t.unshift(e);
                    return t;
                };
                n.getCommonAncestor = (e, t)=>{
                    const r = (0, n.parents)(e), i = (0, n.parents)(t);
                    if (r[0] === i[0]) {
                        for(let e = 0; e < r.length; e++)if (r[e] !== i[e]) return r[e - 1];
                    }
                };
                n.xpath = (e, t = document)=>document.evaluate(e, t, null, XPathResult.FIRST_ORDERED_NODE_TYPE).singleNodeValue;
                n.pad = (e)=>e < 10 ? "0" + e : e.toString();
                n.debug = (...e)=>{
                    console.log(...e);
                };
                n.tryTo = (e, t)=>async (...n)=>{
                        try {
                            return await e(...n);
                        } catch (e) {
                            console.error("error " + t + ":", e);
                        }
                    };
            },
            {}
        ],
        7: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.getWebMHeader = n.base64ToBlob = n.unzip = n.isGzip = void 0;
                const r = e("../constants/default"), i = e("pako");
                function o(e) {
                    if (!e || e.length < 3) return !1;
                    const t = [
                        31,
                        139,
                        8
                    ];
                    return e.slice(0, 3).every((e, n)=>e === t[n]);
                }
                function s(e) {
                    return new Promise((t, n)=>{
                        try {
                            const n = e.split(","), r = n[0].split(":")[1], i = window.atob(n[1]), o = i.length, s = new Uint8Array(o);
                            for(let e = 0; e < o; ++e)s[e] = i.charCodeAt(e);
                            t(new Blob([
                                s
                            ], {
                                type: r.split(";")[0]
                            }));
                        } catch (e) {
                            n(e);
                        }
                    });
                }
                n.isGzip = o, n.unzip = function(e) {
                    const t = new Uint8Array(e);
                    if (o(t)) try {
                        return (0, i.inflate)(t);
                    } catch (e) {
                        console.log(e);
                    }
                    return t;
                }, n.base64ToBlob = s, n.getWebMHeader = function() {
                    return s(r.WebMHeader);
                };
            },
            {
                "../constants/default": 4,
                pako: 27
            }
        ],
        8: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                });
                const r = e("./platforms/google"), i = e("./platforms/zoom"), o = e("./platforms/msteams"), s = e("./components/repository"), a = e("./proxies"), l = e("./components/manager"), c = e("./components/notifier"), u = e("./constants/default");
                !function() {
                    if (window.has_injected_stenographer) return;
                    window.has_injected_stenographer = !0;
                    const e = (0, s.createRepo)(), t = (0, s.createRepo)(), n = (0, s.createRepo)(), f = (0, s.createRepo)(), d = window.PLATFORM_TYPE || u.PLATFORM_TYPES.GOOGLE;
                    let p;
                    switch(d){
                        case u.PLATFORM_TYPES.ZOOM:
                            p = (0, i.Zoom)(e, t);
                            break;
                        case u.PLATFORM_TYPES.MS_TEAMS:
                            p = (0, o.MsTeams)(e, t);
                            break;
                        default:
                            p = (0, r.GoogleMeets)(e, t, f, n, (0, a.WebRtcProxy)(), (0, a.FetchProxy)(), (0, a.RtcSenderProxy)());
                    }
                    (0, l.Manager)((0, c.DefaultNotifier)(), e, t, n, p, f, !1).initialize(), console.log("Stenographer initialized for " + d + " at: " + Date.now());
                }();
            },
            {
                "./components/manager": 1,
                "./components/notifier": 2,
                "./components/repository": 3,
                "./constants/default": 4,
                "./platforms/google": 9,
                "./platforms/msteams": 10,
                "./platforms/zoom": 11,
                "./proxies": 13
            }
        ],
        9: [
            function(e, t, n) {
                (function(t) {
                    (function() {
                        "use strict";
                        Object.defineProperty(n, "__esModule", {
                            value: !0
                        }), n.GoogleMeets = void 0;
                        const r = e("../proxies"), i = e("../lib/utils"), o = e("../decoder/decoder"), s = e("../constants/default"), a = e("../components/notifier");
                        n.GoogleMeets = (e, n, l, c, u, f, d, p = !1)=>{
                            let h, g, m = 2643, y = !1, b = null, w = s.START_AUDIO_ID, _ = null;
                            const v = [], O = new Map, E = (0, r.XhrProxy)();
                            let k = null, A = null;
                            const S = (0, a.DefaultNotifier)(), x = [], T = (t)=>{
                                p && console.log("collection message: ", t);
                                const n = (0, i.unzip)(t.data), r = o.CollectionMessage.decode(n);
                                if (r.body && r.body.wrapper && r.body.wrapper.wrapper) {
                                    if (r.body.wrapper.wrapper.chat) {
                                        const t = r.body.wrapper.wrapper.chat;
                                        for (const n of t){
                                            const t = e.get(n.body.deviceId);
                                            c.set(n.body.messageId, Object.assign(Object.assign({}, n.body), {
                                                user: {
                                                    name: (null == t ? void 0 : t.name) || "",
                                                    fullName: (null == t ? void 0 : t.fullName) || "",
                                                    image: (null == t ? void 0 : t.image) || "",
                                                    id: (null == t ? void 0 : t.id) || ""
                                                }
                                            }));
                                        }
                                    }
                                    if (r.body.wrapper.wrapper.wrapper) {
                                        const t = r.body.wrapper.wrapper.wrapper.userDetails;
                                        if (t) for (const n of t)e.set(n.deviceId, {
                                            id: n.deviceId,
                                            name: n.name,
                                            fullName: n.fullName,
                                            image: n.profile
                                        });
                                    }
                                }
                            }, I = (e, t)=>{
                                "collections" === t.channel.label && (window.proxyPeerConnection = e, p && console.log("data channel message: ", t), t.channel.addEventListener("message", T));
                            };
                            let R = 65110;
                            const C = [], N = new Map, D = (t)=>{
                                try {
                                    const s = (0, i.unzip)(t.data), a = o.CaptionWrapper.decode(s);
                                    if ("" != a.unknown) return void console.log("unknown data found: ", (r = s, [
                                        ...new Uint8Array(r)
                                    ].map((e)=>e.toString(16).padStart(2, "0")).join("")));
                                    if (C.length > 50) {
                                        const e = C.shift();
                                        N.delete(e);
                                    }
                                    const l = a.caption.captionId + "/" + a.caption.deviceSpace;
                                    let c = N.get(l);
                                    c || (c = R++, C.push(l), N.set(l, c));
                                    const u = n.has(c);
                                    let f;
                                    const d = e.get(a.caption.deviceSpace), p = O.get(d.id) || -1;
                                    if (c > p && O.set(d.id, c), u) {
                                        const e = n.get(c);
                                        let t = Date.now();
                                        e.messageId < p && (t = e.endTs), f = Object.assign(Object.assign({}, e), {
                                            endTs: t,
                                            caption: a.caption.caption,
                                            sequence: a.caption.version,
                                            updatedAt: Date.now()
                                        });
                                    } else f = {
                                        messageId: c,
                                        receivedCaptionId: a.caption.captionId,
                                        caption: a.caption.caption,
                                        sequence: a.caption.version,
                                        firstReceiveTs: Date.now(),
                                        updatedAt: Date.now(),
                                        endTs: Date.now(),
                                        user: {
                                            id: d.id,
                                            name: d.name,
                                            fullName: d.fullName,
                                            image: d.image
                                        }
                                    };
                                    n.set(a.caption.captionId, f);
                                } catch (e) {
                                    console.log(e);
                                    const t = e.message + " " + e.stack.substring(0, 1e3);
                                    S.notify(s.WEB_STENOGRAPHER_ERROR, "CaptionMessage " + t);
                                }
                                var r;
                            }, P = async (t)=>{
                                p && console.log("sync meeting space collections: extraction start");
                                try {
                                    const n = await t.text(), r = Uint8Array.from(window.atob(n), (e)=>e.charCodeAt(0)), i = o.MeetingSpaceCollectionResponse.decode(r);
                                    if (i.spaces && i.spaces.wrapper && i.spaces.wrapper.userDetails) {
                                        const t = i.spaces.wrapper.userDetails;
                                        for (const n of t)e.set(n.deviceId, {
                                            id: n.deviceId,
                                            name: n.name,
                                            fullName: n.fullName,
                                            image: n.profile
                                        });
                                    }
                                } catch (e) {
                                    const t = e.message + " " + e.stack.substring(0, 1e3);
                                    S.notify(s.WEB_STENOGRAPHER_ERROR, "SynMeetingSpaceCollection " + t), console.log(e);
                                }
                            }, B = async (t)=>{
                                p && console.log("trying to capture sent comment data");
                                try {
                                    const n = await t.text(), r = Uint8Array.from(window.atob(n), (e)=>e.charCodeAt(0)), i = o.ChatData.decode(r);
                                    if (!i) return;
                                    const s = e.get(i.deviceId);
                                    c.set(i.messageId, Object.assign(Object.assign({}, i), {
                                        user: {
                                            name: (null == s ? void 0 : s.name) || "",
                                            fullName: (null == s ? void 0 : s.fullName) || "",
                                            image: (null == s ? void 0 : s.image) || "",
                                            id: (null == s ? void 0 : s.id) || ""
                                        }
                                    }));
                                } catch (e) {
                                    const t = e.message + " " + e.stack.substring(0, 1e3);
                                    S.notify(s.WEB_STENOGRAPHER_ERROR, "SendComment " + t), console.log(e);
                                }
                            }, j = async (e)=>{
                                const n = await e.text(), r = t.from(n, "base64"), i = o.ResolveMeeting.decode(Uint8Array.from(r));
                                A = {
                                    kind: "fallback",
                                    summary: i.title,
                                    hangoutLink: i.hangoutsUrl
                                };
                            }, M = (e)=>{
                                y || ("closing" === e.readyState || "closed" === e.readyState ? L() : setTimeout(()=>{
                                    M(e);
                                }, 1e3));
                            }, L = ()=>{
                                if (!window.proxyPeerConnection) return console.error("no proxy peer connection found"), void S.notify(s.WEB_STENOGRAPHER_ERROR, "stCaptionService NoProxyPeerConnection");
                                y = !1, [
                                    "disconnected",
                                    "failed",
                                    "closed"
                                ].includes(window.proxyPeerConnection.connectionState) || window.proxyPeerConnection.createDataChannel("captions", {
                                    ordered: !0,
                                    maxRetransmits: 100,
                                    id: m++
                                });
                            }, U = (e)=>{
                                _ ? 0 !== e.getAudioTracks().length ? h ? _.createMediaStreamSource(e).connect(h) : S.notify(s.WEB_STENOGRAPHER_ERROR, "addStreamToDestination no audio destination") : p && console.log("stream doesn't have audio tracks stream::", e.id) : S.notify(s.WEB_STENOGRAPHER_ERROR, "addStreamToDestination no audio context");
                            }, z = (e, t)=>{
                                if (0 != t.streams.length && "closed" !== e.connectionState) for (const e of t.streams)e.getAudioTracks().length > 0 && (v.push(e), U(e));
                            }, W = (e)=>{
                                const t = new MediaStream;
                                t.addTrack(e), v.push(t), x.push(t), U(t);
                            }, Z = (e)=>{
                                l.set(w++, e.data);
                            }, F = (e)=>{
                                try {
                                    if (!_) return S.notify(s.WEB_STENOGRAPHER_ERROR, "WebRTC has no audio context"), p && console.log("no audio context"), !1;
                                    h = _.createMediaStreamDestination();
                                    for (const e of v)U(e);
                                    return H(e), !0;
                                } catch (e) {
                                    const t = e.message + " " + e.stack.substring(0, 1e3);
                                    S.notify(s.WEB_STENOGRAPHER_ERROR, "WebRTC::AUR  " + t), console.error(e);
                                }
                            }, H = async (e)=>{
                                if (_) {
                                    _ && "running" !== _.state && (S.notify(s.WEB_STENOGRAPHER_ERROR, "audio context is not running. trying to wake it up"), await _.resume()), g = new MediaRecorder(h.stream, {
                                        mimeType: "audio/webm"
                                    }), window.ff_media_recorder = g, g.addEventListener("dataavailable", Z), w !== s.START_AUDIO_ID && (S.notify(s.WEB_STENOGRAPHER_ERROR, "audio id is not correct, got: " + w + " expected: " + s.START_AUDIO_ID), (0, i.getWebMHeader)().then((e)=>{
                                        const t = "adding header to audio. header size:" + (e && e.size) + " with id " + w;
                                        console.log(t), S.notify(s.WEB_STENOGRAPHER_LOG, t), l.set(w, e), w++;
                                    }));
                                    try {
                                        g.start(e);
                                    } catch (e) {
                                        const t = e.message + " " + e.stack.substring(0, 1e3);
                                        S.notify(s.WEB_STENOGRAPHER_ERROR, "web-stenographer start audio recording failed: " + t);
                                    }
                                    S.notify(s.WEB_STENOGRAPHER_LOG, "audio recorder started");
                                } else S.notify(s.WEB_STENOGRAPHER_ERROR, "no audio context");
                            }, G = function(e) {
                                const t = document.getElementsByTagName("audio");
                                if (0 === t.length) throw new Error("No audio elements found on page");
                                if (!_) throw new Error("No audio context");
                                h = _.createMediaStreamDestination();
                                for (const e of x)U(e);
                                for (const e of t){
                                    const t = e.srcObject;
                                    _.createMediaStreamSource(t).connect(h);
                                }
                                return H(e), !0;
                            };
                            const V = ()=>{
                                _ && window.ff_audio_context ? console.log("skipping audio context creation, context already exists") : (_ = new AudioContext, window.ff_audio_context = _);
                            };
                            return {
                                initialize: ()=>{
                                    try {
                                        V(), d.initialize(), d.register({
                                            onReplaceTrack: W
                                        });
                                        const e = u.initialize();
                                        u.register({
                                            logChannelArgs: !1,
                                            peerMessages: [
                                                {
                                                    event: "datachannel",
                                                    callback: I
                                                },
                                                {
                                                    event: "track",
                                                    callback: z
                                                }
                                            ],
                                            channelListeners: [
                                                {
                                                    label: "captions",
                                                    callback: D,
                                                    monitor: M
                                                }
                                            ]
                                        });
                                        const t = document.createElement("meta");
                                        t.setAttribute("id", "ff-proxy-check"), t.setAttribute("name", "hasCreatedProxies"), t.setAttribute("content", String(e)), (document.head || document.documentElement).prepend(t), window.addEventListener("load", ()=>{
                                            console.log("Google::window load event"), S.notify(s.WEB_STENOGRAPHER_LOG, "Audio::window load event"), V();
                                        }), f.initialize(), f.register([
                                            {
                                                url: "https://meet.google.com/$rpc/google.rtc.meetings.v1.MeetingSpaceService/SyncMeetingSpaceCollections",
                                                callback: P
                                            },
                                            {
                                                url: "https://meet.google.com/$rpc/google.rtc.meetings.v1.MeetingMessageService/CreateMeetingMessage",
                                                callback: B
                                            },
                                            {
                                                url: "https://meet.google.com/$rpc/google.rtc.meetings.v1.MeetingSpaceService/ResolveMeetingSpace",
                                                callback: j
                                            }
                                        ]), E.initialize(), E.register({
                                            methods: [
                                                {
                                                    url: "https://clients6.google.com/calendar/v3/calendars",
                                                    callback: ()=>{
                                                        console.log("received meeting meta data");
                                                    },
                                                    resp: (e)=>{
                                                        try {
                                                            k = JSON.parse(e);
                                                        } catch (e) {
                                                            console.error(e);
                                                        }
                                                    }
                                                }
                                            ]
                                        });
                                    } catch (e) {
                                        const t = e.message + " " + e.stack.substring(0, 1e3);
                                        S.notify(s.WEB_STENOGRAPHER_ERROR, "initializer failed with: " + t);
                                    }
                                },
                                startCaptionsService: L,
                                stopCaptionsService: ()=>{
                                    y = !0, b && (b.close(), b = null);
                                },
                                startRecorder: (e, t = "webrtc")=>{
                                    switch(console.log("stenographer: start recorder::" + t), t){
                                        case "webrtc":
                                            return F(e);
                                        case "html":
                                            return G(e);
                                    }
                                },
                                stopRecorder: ()=>{
                                    g && g.stop();
                                },
                                getMetadata: ()=>k || A ? {
                                        status: !0,
                                        data: k || A
                                    } : {
                                        status: !1,
                                        data: null
                                    }
                            };
                        };
                    }).call(this);
                }).call(this, e("buffer").Buffer);
            },
            {
                "../components/notifier": 2,
                "../constants/default": 4,
                "../decoder/decoder": 5,
                "../lib/utils": 7,
                "../proxies": 13,
                buffer: 25
            }
        ],
        10: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.MsTeams = void 0;
                const r = e("../components/notifier"), i = e("../constants/default"), o = e("../lib/helpers");
                n.MsTeams = (e, t)=>{
                    const n = (0, r.DefaultNotifier)();
                    let s = null, a = null, l = 0;
                    const c = [], u = new WeakMap, f = (e, n, r)=>{
                        var i;
                        const o = t.get(n);
                        if (o) {
                            const s = e.querySelector(".ui-chat__message__author"), a = (null === (i = null == s ? void 0 : s.textContent) || void 0 === i ? void 0 : i.trim()) || null, l = Date.now(), c = Object.assign(Object.assign({}, o), {
                                caption: r,
                                updatedAt: l,
                                endTs: l,
                                user: a ? {
                                    id: a,
                                    name: a,
                                    fullName: a,
                                    image: null
                                } : null
                            });
                            t.set(n, c);
                        }
                    }, d = (e)=>{
                        var n, r, i, o;
                        for (const s of e)if ("childList" === s.type) {
                            const e = Array.from(s.addedNodes);
                            for (const i of e)if (i instanceof HTMLElement) {
                                const e = i.querySelector(".ui-chat__item__message div[id]"), o = null == e ? void 0 : e.id;
                                if (o && c.includes(o)) {
                                    console.log("captionId refound");
                                    continue;
                                }
                                c.push(o);
                                const s = i.querySelector('[data-tid="closed-caption-message-content"]');
                                if (s instanceof HTMLElement) {
                                    const e = s.querySelector('[data-tid="closed-caption-text"]');
                                    if (e) {
                                        const i = null === (n = e.textContent) || void 0 === n ? void 0 : n.trim();
                                        if (i) {
                                            const e = s.querySelector(".ui-chat__message__author"), n = (null === (r = null == e ? void 0 : e.textContent) || void 0 === r ? void 0 : r.trim()) || null, o = Date.now(), a = {
                                                messageId: l++,
                                                receivedCaptionId: l,
                                                caption: i,
                                                sequence: o,
                                                firstReceiveTs: o,
                                                updatedAt: o,
                                                endTs: o,
                                                user: n ? {
                                                    id: n,
                                                    name: n,
                                                    fullName: n,
                                                    image: null
                                                } : null
                                            };
                                            t.set(a.messageId, a), u.set(s, a.messageId);
                                        }
                                    }
                                }
                            }
                        } else if ("characterData" === s.type) {
                            const e = null === (i = s.target.parentElement) || void 0 === i ? void 0 : i.closest('[data-tid="closed-caption-message-content"]');
                            if (e instanceof HTMLElement) {
                                const t = u.get(e);
                                if (void 0 !== t) {
                                    const n = e.querySelector('[data-tid="closed-caption-text"]'), r = null === (o = null == n ? void 0 : n.textContent) || void 0 === o ? void 0 : o.trim();
                                    r && f(e, t, r);
                                }
                            }
                        }
                    }, p = ()=>{
                        var e, t;
                        const r = document.querySelector('[data-tid="' + i.CAPTION_CONTAINER_ID[i.PLATFORM_TYPES.MS_TEAMS] + '"]') || (null === (t = null === (e = document.getElementsByTagName("iframe")[0]) || void 0 === e ? void 0 : e.contentWindow) || void 0 === t ? void 0 : t.document.querySelector('[data-tid="' + i.CAPTION_CONTAINER_ID[i.PLATFORM_TYPES.MS_TEAMS] + '"]'));
                        if (!r) return a && (a.disconnect(), a = null), void n.notify(i.WEB_STENOGRAPHER_ERROR, "Captions container not found");
                        a || (a = new MutationObserver((0, o.tryTo)(d, "handleCaptionChange")), a.observe(r, {
                            childList: !0,
                            subtree: !0,
                            characterData: !0,
                            characterDataOldValue: !0
                        }), n.notify(i.WEB_STENOGRAPHER_LOG, "Caption observer attached successfully"));
                    };
                    return {
                        initialize: ()=>{
                            var e;
                            try {
                                window.addEventListener("load", ()=>{
                                    n.notify(i.WEB_STENOGRAPHER_LOG, "MS Teams::window load event");
                                }), n.notify(i.WEB_STENOGRAPHER_LOG, "MS Teams module initialized");
                            } catch (t) {
                                const r = t.message + " " + (null === (e = t.stack) || void 0 === e ? void 0 : e.substring(0, 1e3));
                                n.notify(i.WEB_STENOGRAPHER_ERROR, "MS Teams initializer failed with: " + r);
                            }
                        },
                        startCaptionsService: ()=>{
                            s && (clearInterval(s), s = null), s = setInterval((0, o.tryTo)(p, "attach to captions"), 1e3), n.notify(i.WEB_STENOGRAPHER_LOG, "Caption service started");
                        },
                        stopCaptionsService: ()=>{
                            s && (clearInterval(s), s = null), a && (a.disconnect(), a = null), n.notify(i.WEB_STENOGRAPHER_LOG, "Caption service stopped");
                        }
                    };
                };
            },
            {
                "../components/notifier": 2,
                "../constants/default": 4,
                "../lib/helpers": 6
            }
        ],
        11: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.Zoom = void 0;
                const r = e("../components/notifier"), i = e("../constants/default"), o = e("../lib/helpers");
                n.Zoom = (e, t)=>{
                    const n = (0, r.DefaultNotifier)();
                    let s = null, a = null;
                    const l = new Map, c = new Map;
                    let u = 0, f = 0;
                    const d = (e)=>{
                        var n;
                        for (const r of e)if ("characterData" === r.type && "#text" === r.target.nodeName) {
                            const e = r.target, i = e.parentNode.parentElement.getAttribute("data-caption-container-id"), o = null === (n = r.oldValue) || void 0 === n ? void 0 : n.trim();
                            let s = null == e ? void 0 : e.textContent.trim();
                            const { shouldAdd: a , oldValueEndsAt: d  } = p(o, s);
                            if (!a) return;
                            if (d > 0 && u <= 1 && (s = s.slice(d + 1)), s) {
                                const e = Date.now();
                                let n = null;
                                if (i && c.has(i)) {
                                    const e = c.get(i);
                                    n = {
                                        id: i,
                                        name: e,
                                        fullName: e,
                                        image: null
                                    };
                                }
                                const r = l.get(i), o = null == r ? void 0 : r.currentCaptionId;
                                if (console.log("Last Caption:", r), o && u > 1 && d > 0) {
                                    console.log("Multi speaker mode");
                                    const n = t.get(o);
                                    if (console.log(n), n) return n.caption = s, n.endTs = e, void t.set(o, n);
                                }
                                console.log("Single speaker mode");
                                const a = {
                                    messageId: f++,
                                    receivedCaptionId: f,
                                    caption: s,
                                    sequence: e,
                                    firstReceiveTs: e,
                                    updatedAt: e,
                                    endTs: e,
                                    user: n
                                };
                                t.set(a.messageId, a), l.set(i, Object.assign(Object.assign({}, r), {
                                    currentCaptionId: a.messageId
                                }));
                            }
                        }
                    }, p = (e, t, n = 20, r = 5)=>{
                        const i = e.length <= n + r, o = i ? e : e.slice(-n - r, -r), s = t.toLowerCase().indexOf(o.toLowerCase());
                        if (-1 === s) return console.log("Substring not found"), t.length > e.length ? {
                            shouldAdd: !1,
                            oldValueEndsAt: 0
                        } : {
                            shouldAdd: !0,
                            oldValueEndsAt: 0
                        };
                        return {
                            shouldAdd: !0,
                            oldValueEndsAt: i ? s + o.length - 1 : s + o.length + r - 1
                        };
                    }, h = (e, t)=>{
                        if (l.get(t)) return;
                        const n = new MutationObserver((0, o.tryTo)(d, "handleCaptionChange"));
                        n.observe(e, {
                            attributes: !0,
                            subtree: !0,
                            characterData: !0,
                            characterDataOldValue: !0
                        }), l.set(t, {
                            observer: n,
                            currentCaption: ""
                        }), console.log("Caption containers attached");
                    }, g = ()=>{
                        var e, t;
                        console.log("Reached to setup parent observer");
                        const n = document.getElementsByClassName(i.ZOOM_PARENT_CAPTION_CONTAINER)[0] || (null === (t = null === (e = document.getElementsByTagName("iframe")[0]) || void 0 === e ? void 0 : e.contentWindow) || void 0 === t ? void 0 : t.document.getElementsByClassName(i.ZOOM_PARENT_CAPTION_CONTAINER)[0]);
                        if (!n) return void console.log("Parent element not found");
                        console.log("Parent Observer Found"), s = new MutationObserver(y), s.observe(n, {
                            childList: !0,
                            subtree: !1
                        }), console.log("Parent observer attached");
                        const r = n.children;
                        u = r.length, console.log("concurrentSpeakers:", u);
                        for (const e of r){
                            const t = m(e);
                            t && (e.setAttribute("data-caption-container-id", t), h(e, t));
                        }
                        n.style.display = "none";
                    }, m = (e)=>{
                        var t;
                        if (console.log(e), !e.children[0]) return null;
                        const n = e.getAttribute("data-caption-container-id");
                        if (n) return n;
                        const r = null !== (t = e.children[0].getAttribute("src")) && void 0 !== t ? t : e.children[0].style.backgroundColor + e.children[0].innerText;
                        if (!r) return null;
                        return btoa(r).slice(0, 13);
                    }, y = (e)=>{
                        for (const t of e)"childList" === t.type && (u = u + t.addedNodes.length - t.removedNodes.length, t.removedNodes.forEach((e)=>{
                            if (e.nodeType === Node.ELEMENT_NODE) {
                                const t = e.getAttribute("data-caption-container-id");
                                if (t && l.has(t)) {
                                    const e = l.get(t);
                                    e && (e.observer.disconnect(), l.delete(t), console.log("Caption container removed"));
                                }
                            }
                        }), t.addedNodes.forEach((e)=>{
                            if (e.nodeType === Node.ELEMENT_NODE) {
                                const t = e, n = m(t);
                                n && (t.setAttribute("data-caption-container-id", n), h(t, n));
                            }
                        }));
                    }, b = ()=>{
                        const e = document.evaluate(i.ZOOM_PARTICPANTS_BUTTON, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                        console.log("Element found:", e), e instanceof HTMLElement ? (e.click(), console.log("Element clicked successfully!")) : console.error("Error: Element not found with XPath:", i.ZOOM_PARTICPANTS_BUTTON);
                    }, w = ()=>{
                        const e = document.evaluate(i.ZOOM_POP_OUT_BUTTON, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                        e instanceof HTMLElement ? (e.click(), console.log("Element clicked successfully!")) : console.error("Error: Element not found with XPath:", i.ZOOM_POP_OUT_BUTTON);
                    }, _ = ()=>{
                        var e, t;
                        const n = document.getElementById(i.ZOOM_PARTICIPANT_LIST) || (null === (t = null === (e = document.getElementsByTagName("iframe")[0]) || void 0 === e ? void 0 : e.contentWindow) || void 0 === t ? void 0 : t.document.getElementById(i.ZOOM_PARTICIPANT_LIST));
                        if (!n) return void console.log("Participant Panel not found");
                        const r = n.children[0];
                        a = new MutationObserver(v), a.observe(r, {
                            childList: !0,
                            subtree: !1
                        });
                        for (const e of r.children)O(e.children[0].children[0].children[0].children[0]);
                        (()=>{
                            var e, t;
                            (document.getElementById(i.ZOOM_PARTICIPANT_WINDOW) || (null === (t = null === (e = document.getElementsByTagName("iframe")[0]) || void 0 === e ? void 0 : e.contentWindow) || void 0 === t ? void 0 : t.document.getElementById(i.ZOOM_PARTICIPANT_WINDOW))).style.display = "none";
                        })();
                    }, v = (e)=>{
                        for (const t of e)"childList" === t.type && t.addedNodes.forEach((e)=>{
                            e.nodeType === Node.ELEMENT_NODE && O(e.children[0].children[0].children[0].children[0]);
                        });
                    }, O = (e)=>{
                        const t = m(e);
                        if (t && (e.setAttribute("data-caption-container-id", t), !c.has(t))) {
                            const n = e.children[1].textContent.split("(")[0].trim();
                            console.log("Speaker:", n), c.set(t, n);
                        }
                    };
                    return {
                        initialize: ()=>{
                            var e;
                            try {
                                window.addEventListener("load", ()=>{
                                    n.notify(i.WEB_STENOGRAPHER_LOG, "Zoom::window load event");
                                }), n.notify(i.WEB_STENOGRAPHER_LOG, "Zoom module initialized");
                            } catch (t) {
                                const r = t.message + " " + (null === (e = t.stack) || void 0 === e ? void 0 : e.substring(0, 1e3));
                                n.notify(i.WEB_STENOGRAPHER_ERROR, "Zoom initializer failed with: " + r);
                            }
                        },
                        startCaptionsService: ()=>{
                            console.log("Starting caption service"), setTimeout(()=>{
                                g(), b(), setTimeout(w, 500), setTimeout(_, 1500), n.notify(i.WEB_STENOGRAPHER_LOG, "Caption service started");
                            }, 500);
                        },
                        stopCaptionsService: ()=>{
                            s && (s.disconnect(), s = null), l.forEach((e)=>{
                                e.observer.disconnect();
                            }), l.clear(), a && (a.disconnect(), a = null), n.notify(i.WEB_STENOGRAPHER_LOG, "Caption service stopped");
                        }
                    };
                };
            },
            {
                "../components/notifier": 2,
                "../constants/default": 4,
                "../lib/helpers": 6
            }
        ],
        12: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.FetchProxy = void 0, n.FetchProxy = function(e) {
                    let t = [];
                    return {
                        initialize: ()=>{
                            if (!window.fetch) return !1;
                            e && e.debug && console.log("fetch initialized");
                            const n = window.fetch;
                            return window.fetch = function(...e) {
                                return new Promise((r, i)=>{
                                    n.apply(this, e).then((e)=>{
                                        for (const n of t)if (e.url === n.url) try {
                                            const t = e.clone();
                                            n.callback(t);
                                        } catch (e) {
                                            console.error("failed calling proxy for fetch with error", e);
                                        }
                                        r(e);
                                    }).catch((e)=>{
                                        i(e);
                                    });
                                });
                            }, !0;
                        },
                        register: (e)=>{
                            t = [
                                ...t,
                                ...e
                            ];
                        }
                    };
                };
            },
            {}
        ],
        13: [
            function(e, t, n) {
                "use strict";
                var r = this && this.__createBinding || (Object.create ? function(e, t, n, r) {
                    void 0 === r && (r = n);
                    var i = Object.getOwnPropertyDescriptor(t, n);
                    i && !("get" in i ? !t.__esModule : i.writable || i.configurable) || (i = {
                        enumerable: !0,
                        get: function() {
                            return t[n];
                        }
                    }), Object.defineProperty(e, r, i);
                } : function(e, t, n, r) {
                    void 0 === r && (r = n), e[r] = t[n];
                }), i = this && this.__exportStar || function(e, t) {
                    for(var n in e)"default" === n || Object.prototype.hasOwnProperty.call(t, n) || r(t, e, n);
                };
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), i(e("./fetch-proxy"), n), i(e("./xhr-proxy"), n), i(e("./rtc-proxy"), n), i(e("./rtc-sender-proxy"), n);
            },
            {
                "./fetch-proxy": 12,
                "./rtc-proxy": 14,
                "./rtc-sender-proxy": 15,
                "./xhr-proxy": 16
            }
        ],
        14: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.WebRtcProxy = void 0, n.WebRtcProxy = function(e) {
                    let t = {
                        peerMessages: [],
                        logChannelArgs: !1,
                        channelListeners: []
                    };
                    return {
                        initialize: ()=>{
                            if (!window.RTCPeerConnection) return !1;
                            window.ff_channels || (window.ff_channels = {});
                            const n = window.RTCPeerConnection, r = n.prototype.createDataChannel;
                            return r && (n.prototype.createDataChannel = function() {
                                t.logChannelArgs && console.log("creating channel args", arguments);
                                try {
                                    const e = r.apply(this, arguments);
                                    if (e && t.channelListeners.length > 0) {
                                        const n = t.channelListeners.find((t)=>t.label === e.label);
                                        n && (e.addEventListener("message", n.callback), n.monitor && n.monitor(e)), window.ff_channels[e.label] = e;
                                    }
                                    return e;
                                } catch (e) {
                                    console.log(e);
                                }
                            }), window.RTCPeerConnection = function(r, i) {
                                const o = new n(r, i);
                                e && e.debug && console.log("created peer connection", o);
                                for (const e of t.peerMessages)o.addEventListener(e.event, (t)=>{
                                    e.callback(o, t);
                                });
                                return o;
                            }, window.RTCPeerConnection.prototype = n.prototype, !0;
                        },
                        register: (e)=>{
                            t = {
                                peerMessages: [
                                    ...t.peerMessages,
                                    ...e.peerMessages
                                ],
                                logChannelArgs: e.logChannelArgs,
                                channelListeners: [
                                    ...t.channelListeners,
                                    ...e.channelListeners
                                ]
                            };
                        }
                    };
                };
            },
            {}
        ],
        15: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.RtcSenderProxy = void 0, n.RtcSenderProxy = function(e) {
                    let t = null;
                    return {
                        initialize: ()=>{
                            if (!window.RTCRtpSender) return !1;
                            const n = window.RTCRtpSender.prototype.replaceTrack;
                            return window.RTCRtpSender.prototype.replaceTrack = function(r) {
                                return e && e.debug && console.log("replacing track", r), t && t.onReplaceTrack && t.onReplaceTrack(r), n.apply(this, arguments);
                            }, !0;
                        },
                        register: (e)=>{
                            t = e;
                        }
                    };
                };
            },
            {}
        ],
        16: [
            function(e, t, n) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.XhrProxy = void 0, n.XhrProxy = function(e) {
                    let t = {
                        methods: []
                    };
                    return {
                        initialize: ()=>{
                            if (!window.XMLHttpRequest) return !1;
                            const e = window.XMLHttpRequest.prototype.open, n = window.XMLHttpRequest.prototype.send;
                            return window.XMLHttpRequest.prototype.open = function(n, r, ...i) {
                                for (const e of t.methods)if (r.startsWith(e.url)) {
                                    this.__currentURL = r, this.__proxy_callback = e.callback, this.__resp_cb = e.resp;
                                    break;
                                }
                                e.apply(this, [
                                    n,
                                    r,
                                    ...i
                                ]);
                            }, window.XMLHttpRequest.prototype.send = function(e, ...t) {
                                if (this.__currentURL) try {
                                    this.__proxy_callback(e);
                                } catch (e) {
                                    console.error(e);
                                }
                                n.apply(this, [
                                    e,
                                    ...t
                                ]), this.addEventListener("readystatechange", ()=>{
                                    this.__currentURL && this.__resp_cb && this.readyState == XMLHttpRequest.DONE && this.__resp_cb(this.responseText);
                                });
                            }, !0;
                        },
                        register: (e)=>{
                            t = {
                                methods: [
                                    ...t.methods,
                                    ...e.methods
                                ]
                            };
                        }
                    };
                };
            },
            {}
        ],
        17: [
            function(e, t, n) {
                "use strict";
                t.exports = function(e, t) {
                    var n = new Array(arguments.length - 1), r = 0, i = 2, o = !0;
                    for(; i < arguments.length;)n[r++] = arguments[i++];
                    return new Promise(function(i, s) {
                        n[r] = function(e) {
                            if (o) {
                                if (o = !1, e) s(e);
                                else {
                                    for(var t = new Array(arguments.length - 1), n = 0; n < t.length;)t[n++] = arguments[n];
                                    i.apply(null, t);
                                }
                            }
                        };
                        try {
                            e.apply(t || null, n);
                        } catch (e) {
                            o && (o = !1, s(e));
                        }
                    });
                };
            },
            {}
        ],
        18: [
            function(e, t, n) {
                "use strict";
                var r = n;
                r.length = function(e) {
                    var t = e.length;
                    if (!t) return 0;
                    for(var n = 0; --t % 4 > 1 && "=" === e.charAt(t);)++n;
                    return Math.ceil(3 * e.length) / 4 - n;
                };
                for(var i = new Array(64), o = new Array(123), s = 0; s < 64;)o[i[s] = s < 26 ? s + 65 : s < 52 ? s + 71 : s < 62 ? s - 4 : s - 59 | 43] = s++;
                r.encode = function(e, t, n) {
                    for(var r, o = null, s = [], a = 0, l = 0; t < n;){
                        var c = e[t++];
                        switch(l){
                            case 0:
                                s[a++] = i[c >> 2], r = (3 & c) << 4, l = 1;
                                break;
                            case 1:
                                s[a++] = i[r | c >> 4], r = (15 & c) << 2, l = 2;
                                break;
                            case 2:
                                s[a++] = i[r | c >> 6], s[a++] = i[63 & c], l = 0;
                        }
                        a > 8191 && ((o || (o = [])).push(String.fromCharCode.apply(String, s)), a = 0);
                    }
                    return l && (s[a++] = i[r], s[a++] = 61, 1 === l && (s[a++] = 61)), o ? (a && o.push(String.fromCharCode.apply(String, s.slice(0, a))), o.join("")) : String.fromCharCode.apply(String, s.slice(0, a));
                };
                var a = "invalid encoding";
                r.decode = function(e, t, n) {
                    for(var r, i = n, s = 0, l = 0; l < e.length;){
                        var c = e.charCodeAt(l++);
                        if (61 === c && s > 1) break;
                        if (void 0 === (c = o[c])) throw Error(a);
                        switch(s){
                            case 0:
                                r = c, s = 1;
                                break;
                            case 1:
                                t[n++] = r << 2 | (48 & c) >> 4, r = c, s = 2;
                                break;
                            case 2:
                                t[n++] = (15 & r) << 4 | (60 & c) >> 2, r = c, s = 3;
                                break;
                            case 3:
                                t[n++] = (3 & r) << 6 | c, s = 0;
                        }
                    }
                    if (1 === s) throw Error(a);
                    return n - i;
                }, r.test = function(e) {
                    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e);
                };
            },
            {}
        ],
        19: [
            function(e, t, n) {
                "use strict";
                function r() {
                    this._listeners = {};
                }
                t.exports = r, r.prototype.on = function(e, t, n) {
                    return (this._listeners[e] || (this._listeners[e] = [])).push({
                        fn: t,
                        ctx: n || this
                    }), this;
                }, r.prototype.off = function(e, t) {
                    if (void 0 === e) this._listeners = {};
                    else if (void 0 === t) this._listeners[e] = [];
                    else for(var n = this._listeners[e], r = 0; r < n.length;)n[r].fn === t ? n.splice(r, 1) : ++r;
                    return this;
                }, r.prototype.emit = function(e) {
                    var t = this._listeners[e];
                    if (t) {
                        for(var n = [], r = 1; r < arguments.length;)n.push(arguments[r++]);
                        for(r = 0; r < t.length;)t[r].fn.apply(t[r++].ctx, n);
                    }
                    return this;
                };
            },
            {}
        ],
        20: [
            function(e, t, n) {
                "use strict";
                function r(e) {
                    return "undefined" != typeof Float32Array ? function() {
                        var t = new Float32Array([
                            -0
                        ]), n = new Uint8Array(t.buffer), r = 128 === n[3];
                        function i(e, r, i) {
                            t[0] = e, r[i] = n[0], r[i + 1] = n[1], r[i + 2] = n[2], r[i + 3] = n[3];
                        }
                        function o(e, r, i) {
                            t[0] = e, r[i] = n[3], r[i + 1] = n[2], r[i + 2] = n[1], r[i + 3] = n[0];
                        }
                        function s(e, r) {
                            return n[0] = e[r], n[1] = e[r + 1], n[2] = e[r + 2], n[3] = e[r + 3], t[0];
                        }
                        function a(e, r) {
                            return n[3] = e[r], n[2] = e[r + 1], n[1] = e[r + 2], n[0] = e[r + 3], t[0];
                        }
                        e.writeFloatLE = r ? i : o, e.writeFloatBE = r ? o : i, e.readFloatLE = r ? s : a, e.readFloatBE = r ? a : s;
                    }() : function() {
                        function t(e, t, n, r) {
                            var i = t < 0 ? 1 : 0;
                            if (i && (t = -t), 0 === t) e(1 / t > 0 ? 0 : 2147483648, n, r);
                            else if (isNaN(t)) e(2143289344, n, r);
                            else if (t > 34028234663852886e22) e((i << 31 | 2139095040) >>> 0, n, r);
                            else if (t < 11754943508222875e-54) e((i << 31 | Math.round(t / 1401298464324817e-60)) >>> 0, n, r);
                            else {
                                var o = Math.floor(Math.log(t) / Math.LN2);
                                e((i << 31 | o + 127 << 23 | 8388607 & Math.round(t * Math.pow(2, -o) * 8388608)) >>> 0, n, r);
                            }
                        }
                        function n(e, t, n) {
                            var r = e(t, n), i = 2 * (r >> 31) + 1, o = r >>> 23 & 255, s = 8388607 & r;
                            return 255 === o ? s ? NaN : i * (1 / 0) : 0 === o ? 1401298464324817e-60 * i * s : i * Math.pow(2, o - 150) * (s + 8388608);
                        }
                        e.writeFloatLE = t.bind(null, i), e.writeFloatBE = t.bind(null, o), e.readFloatLE = n.bind(null, s), e.readFloatBE = n.bind(null, a);
                    }(), "undefined" != typeof Float64Array ? function() {
                        var t = new Float64Array([
                            -0
                        ]), n = new Uint8Array(t.buffer), r = 128 === n[7];
                        function i(e, r, i) {
                            t[0] = e, r[i] = n[0], r[i + 1] = n[1], r[i + 2] = n[2], r[i + 3] = n[3], r[i + 4] = n[4], r[i + 5] = n[5], r[i + 6] = n[6], r[i + 7] = n[7];
                        }
                        function o(e, r, i) {
                            t[0] = e, r[i] = n[7], r[i + 1] = n[6], r[i + 2] = n[5], r[i + 3] = n[4], r[i + 4] = n[3], r[i + 5] = n[2], r[i + 6] = n[1], r[i + 7] = n[0];
                        }
                        function s(e, r) {
                            return n[0] = e[r], n[1] = e[r + 1], n[2] = e[r + 2], n[3] = e[r + 3], n[4] = e[r + 4], n[5] = e[r + 5], n[6] = e[r + 6], n[7] = e[r + 7], t[0];
                        }
                        function a(e, r) {
                            return n[7] = e[r], n[6] = e[r + 1], n[5] = e[r + 2], n[4] = e[r + 3], n[3] = e[r + 4], n[2] = e[r + 5], n[1] = e[r + 6], n[0] = e[r + 7], t[0];
                        }
                        e.writeDoubleLE = r ? i : o, e.writeDoubleBE = r ? o : i, e.readDoubleLE = r ? s : a, e.readDoubleBE = r ? a : s;
                    }() : function() {
                        function t(e, t, n, r, i, o) {
                            var s = r < 0 ? 1 : 0;
                            if (s && (r = -r), 0 === r) e(0, i, o + t), e(1 / r > 0 ? 0 : 2147483648, i, o + n);
                            else if (isNaN(r)) e(0, i, o + t), e(2146959360, i, o + n);
                            else if (r > 17976931348623157e292) e(0, i, o + t), e((s << 31 | 2146435072) >>> 0, i, o + n);
                            else {
                                var a;
                                if (r < 22250738585072014e-324) e((a = r / 5e-324) >>> 0, i, o + t), e((s << 31 | a / 4294967296) >>> 0, i, o + n);
                                else {
                                    var l = Math.floor(Math.log(r) / Math.LN2);
                                    1024 === l && (l = 1023), e(4503599627370496 * (a = r * Math.pow(2, -l)) >>> 0, i, o + t), e((s << 31 | l + 1023 << 20 | 1048576 * a & 1048575) >>> 0, i, o + n);
                                }
                            }
                        }
                        function n(e, t, n, r, i) {
                            var o = e(r, i + t), s = e(r, i + n), a = 2 * (s >> 31) + 1, l = s >>> 20 & 2047, c = 4294967296 * (1048575 & s) + o;
                            return 2047 === l ? c ? NaN : a * (1 / 0) : 0 === l ? 5e-324 * a * c : a * Math.pow(2, l - 1075) * (c + 4503599627370496);
                        }
                        e.writeDoubleLE = t.bind(null, i, 0, 4), e.writeDoubleBE = t.bind(null, o, 4, 0), e.readDoubleLE = n.bind(null, s, 0, 4), e.readDoubleBE = n.bind(null, a, 4, 0);
                    }(), e;
                }
                function i(e, t, n) {
                    t[n] = 255 & e, t[n + 1] = e >>> 8 & 255, t[n + 2] = e >>> 16 & 255, t[n + 3] = e >>> 24;
                }
                function o(e, t, n) {
                    t[n] = e >>> 24, t[n + 1] = e >>> 16 & 255, t[n + 2] = e >>> 8 & 255, t[n + 3] = 255 & e;
                }
                function s(e, t) {
                    return (e[t] | e[t + 1] << 8 | e[t + 2] << 16 | e[t + 3] << 24) >>> 0;
                }
                function a(e, t) {
                    return (e[t] << 24 | e[t + 1] << 16 | e[t + 2] << 8 | e[t + 3]) >>> 0;
                }
                t.exports = r(r);
            },
            {}
        ],
        21: [
            function(require, module, exports) {
                "use strict";
                function inquire(moduleName) {
                    try {
                        var mod = require(moduleName);
                        if (mod && (mod.length || Object.keys(mod).length)) return mod;
                    } catch (e) {}
                    return null;
                }
                module.exports = inquire;
            },
            {}
        ],
        22: [
            function(e, t, n) {
                "use strict";
                t.exports = function(e, t, n) {
                    var r = n || 8192, i = r >>> 1, o = null, s = r;
                    return function(n) {
                        if (n < 1 || n > i) return e(n);
                        s + n > r && (o = e(r), s = 0);
                        var a = t.call(o, s, s += n);
                        return 7 & s && (s = 1 + (7 | s)), a;
                    };
                };
            },
            {}
        ],
        23: [
            function(e, t, n) {
                "use strict";
                var r = n;
                r.length = function(e) {
                    for(var t = 0, n = 0, r = 0; r < e.length; ++r)(n = e.charCodeAt(r)) < 128 ? t += 1 : n < 2048 ? t += 2 : 55296 == (64512 & n) && 56320 == (64512 & e.charCodeAt(r + 1)) ? (++r, t += 4) : t += 3;
                    return t;
                }, r.read = function(e, t, n) {
                    if (n - t < 1) return "";
                    for(var r, i = null, o = [], s = 0; t < n;)(r = e[t++]) < 128 ? o[s++] = r : r > 191 && r < 224 ? o[s++] = (31 & r) << 6 | 63 & e[t++] : r > 239 && r < 365 ? (r = ((7 & r) << 18 | (63 & e[t++]) << 12 | (63 & e[t++]) << 6 | 63 & e[t++]) - 65536, o[s++] = 55296 + (r >> 10), o[s++] = 56320 + (1023 & r)) : o[s++] = (15 & r) << 12 | (63 & e[t++]) << 6 | 63 & e[t++], s > 8191 && ((i || (i = [])).push(String.fromCharCode.apply(String, o)), s = 0);
                    return i ? (s && i.push(String.fromCharCode.apply(String, o.slice(0, s))), i.join("")) : String.fromCharCode.apply(String, o.slice(0, s));
                }, r.write = function(e, t, n) {
                    for(var r, i, o = n, s = 0; s < e.length; ++s)(r = e.charCodeAt(s)) < 128 ? t[n++] = r : r < 2048 ? (t[n++] = r >> 6 | 192, t[n++] = 63 & r | 128) : 55296 == (64512 & r) && 56320 == (64512 & (i = e.charCodeAt(s + 1))) ? (r = 65536 + ((1023 & r) << 10) + (1023 & i), ++s, t[n++] = r >> 18 | 240, t[n++] = r >> 12 & 63 | 128, t[n++] = r >> 6 & 63 | 128, t[n++] = 63 & r | 128) : (t[n++] = r >> 12 | 224, t[n++] = r >> 6 & 63 | 128, t[n++] = 63 & r | 128);
                    return n - o;
                };
            },
            {}
        ],
        24: [
            function(e, t, n) {
                "use strict";
                n.byteLength = function(e) {
                    var t = c(e), n = t[0], r = t[1];
                    return 3 * (n + r) / 4 - r;
                }, n.toByteArray = function(e) {
                    var t, n, r = c(e), s = r[0], a = r[1], l = new o(function(e, t, n) {
                        return 3 * (t + n) / 4 - n;
                    }(0, s, a)), u = 0, f = a > 0 ? s - 4 : s;
                    for(n = 0; n < f; n += 4)t = i[e.charCodeAt(n)] << 18 | i[e.charCodeAt(n + 1)] << 12 | i[e.charCodeAt(n + 2)] << 6 | i[e.charCodeAt(n + 3)], l[u++] = t >> 16 & 255, l[u++] = t >> 8 & 255, l[u++] = 255 & t;
                    2 === a && (t = i[e.charCodeAt(n)] << 2 | i[e.charCodeAt(n + 1)] >> 4, l[u++] = 255 & t);
                    1 === a && (t = i[e.charCodeAt(n)] << 10 | i[e.charCodeAt(n + 1)] << 4 | i[e.charCodeAt(n + 2)] >> 2, l[u++] = t >> 8 & 255, l[u++] = 255 & t);
                    return l;
                }, n.fromByteArray = function(e) {
                    for(var t, n = e.length, i = n % 3, o = [], s = 16383, a = 0, l = n - i; a < l; a += s)o.push(u(e, a, a + s > l ? l : a + s));
                    1 === i ? (t = e[n - 1], o.push(r[t >> 2] + r[t << 4 & 63] + "==")) : 2 === i && (t = (e[n - 2] << 8) + e[n - 1], o.push(r[t >> 10] + r[t >> 4 & 63] + r[t << 2 & 63] + "="));
                    return o.join("");
                };
                for(var r = [], i = [], o = "undefined" != typeof Uint8Array ? Uint8Array : Array, s = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", a = 0, l = s.length; a < l; ++a)r[a] = s[a], i[s.charCodeAt(a)] = a;
                function c(e) {
                    var t = e.length;
                    if (t % 4 > 0) throw new Error("Invalid string. Length must be a multiple of 4");
                    var n = e.indexOf("=");
                    return -1 === n && (n = t), [
                        n,
                        n === t ? 0 : 4 - n % 4
                    ];
                }
                function u(e, t, n) {
                    for(var i, o, s = [], a = t; a < n; a += 3)i = (e[a] << 16 & 16711680) + (e[a + 1] << 8 & 65280) + (255 & e[a + 2]), s.push(r[(o = i) >> 18 & 63] + r[o >> 12 & 63] + r[o >> 6 & 63] + r[63 & o]);
                    return s.join("");
                }
                i["-".charCodeAt(0)] = 62, i["_".charCodeAt(0)] = 63;
            },
            {}
        ],
        25: [
            function(e, t, n) {
                (function(t) {
                    (function() {
                        /*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */ "use strict";
                        var t = e("base64-js"), r = e("ieee754");
                        n.Buffer = s, n.SlowBuffer = function(e) {
                            +e != e && (e = 0);
                            return s.alloc(+e);
                        }, n.INSPECT_MAX_BYTES = 50;
                        var i = **********;
                        function o(e) {
                            if (e > i) throw new RangeError('The value "' + e + '" is invalid for option "size"');
                            var t = new Uint8Array(e);
                            return t.__proto__ = s.prototype, t;
                        }
                        function s(e, t, n) {
                            if ("number" == typeof e) {
                                if ("string" == typeof t) throw new TypeError('The "string" argument must be of type string. Received type number');
                                return c(e);
                            }
                            return a(e, t, n);
                        }
                        function a(e, t, n) {
                            if ("string" == typeof e) return function(e, t) {
                                "string" == typeof t && "" !== t || (t = "utf8");
                                if (!s.isEncoding(t)) throw new TypeError("Unknown encoding: " + t);
                                var n = 0 | d(e, t), r = o(n), i = r.write(e, t);
                                i !== n && (r = r.slice(0, i));
                                return r;
                            }(e, t);
                            if (ArrayBuffer.isView(e)) return u(e);
                            if (null == e) throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof e);
                            if (z(e, ArrayBuffer) || e && z(e.buffer, ArrayBuffer)) return function(e, t, n) {
                                if (t < 0 || e.byteLength < t) throw new RangeError('"offset" is outside of buffer bounds');
                                if (e.byteLength < t + (n || 0)) throw new RangeError('"length" is outside of buffer bounds');
                                var r;
                                r = void 0 === t && void 0 === n ? new Uint8Array(e) : void 0 === n ? new Uint8Array(e, t) : new Uint8Array(e, t, n);
                                return r.__proto__ = s.prototype, r;
                            }(e, t, n);
                            if ("number" == typeof e) throw new TypeError('The "value" argument must not be of type number. Received type number');
                            var r = e.valueOf && e.valueOf();
                            if (null != r && r !== e) return s.from(r, t, n);
                            var i = function(e) {
                                if (s.isBuffer(e)) {
                                    var t = 0 | f(e.length), n = o(t);
                                    return 0 === n.length || e.copy(n, 0, 0, t), n;
                                }
                                if (void 0 !== e.length) return "number" != typeof e.length || W(e.length) ? o(0) : u(e);
                                if ("Buffer" === e.type && Array.isArray(e.data)) return u(e.data);
                            }(e);
                            if (i) return i;
                            if ("undefined" != typeof Symbol && null != Symbol.toPrimitive && "function" == typeof e[Symbol.toPrimitive]) return s.from(e[Symbol.toPrimitive]("string"), t, n);
                            throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type " + typeof e);
                        }
                        function l(e) {
                            if ("number" != typeof e) throw new TypeError('"size" argument must be of type number');
                            if (e < 0) throw new RangeError('The value "' + e + '" is invalid for option "size"');
                        }
                        function c(e) {
                            return l(e), o(e < 0 ? 0 : 0 | f(e));
                        }
                        function u(e) {
                            for(var t = e.length < 0 ? 0 : 0 | f(e.length), n = o(t), r = 0; r < t; r += 1)n[r] = 255 & e[r];
                            return n;
                        }
                        function f(e) {
                            if (e >= i) throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x" + i.toString(16) + " bytes");
                            return 0 | e;
                        }
                        function d(e, t) {
                            if (s.isBuffer(e)) return e.length;
                            if (ArrayBuffer.isView(e) || z(e, ArrayBuffer)) return e.byteLength;
                            if ("string" != typeof e) throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type ' + typeof e);
                            var n = e.length, r = arguments.length > 2 && !0 === arguments[2];
                            if (!r && 0 === n) return 0;
                            for(var i = !1;;)switch(t){
                                case "ascii":
                                case "latin1":
                                case "binary":
                                    return n;
                                case "utf8":
                                case "utf-8":
                                    return M(e).length;
                                case "ucs2":
                                case "ucs-2":
                                case "utf16le":
                                case "utf-16le":
                                    return 2 * n;
                                case "hex":
                                    return n >>> 1;
                                case "base64":
                                    return L(e).length;
                                default:
                                    if (i) return r ? -1 : M(e).length;
                                    t = ("" + t).toLowerCase(), i = !0;
                            }
                        }
                        function p(e, t, n) {
                            var r = !1;
                            if ((void 0 === t || t < 0) && (t = 0), t > this.length) return "";
                            if ((void 0 === n || n > this.length) && (n = this.length), n <= 0) return "";
                            if ((n >>>= 0) <= (t >>>= 0)) return "";
                            for(e || (e = "utf8");;)switch(e){
                                case "hex":
                                    return T(this, t, n);
                                case "utf8":
                                case "utf-8":
                                    return k(this, t, n);
                                case "ascii":
                                    return S(this, t, n);
                                case "latin1":
                                case "binary":
                                    return x(this, t, n);
                                case "base64":
                                    return E(this, t, n);
                                case "ucs2":
                                case "ucs-2":
                                case "utf16le":
                                case "utf-16le":
                                    return I(this, t, n);
                                default:
                                    if (r) throw new TypeError("Unknown encoding: " + e);
                                    e = (e + "").toLowerCase(), r = !0;
                            }
                        }
                        function h(e, t, n) {
                            var r = e[t];
                            e[t] = e[n], e[n] = r;
                        }
                        function g(e, t, n, r, i) {
                            if (0 === e.length) return -1;
                            if ("string" == typeof n ? (r = n, n = 0) : n > ********** ? n = ********** : n < -2147483648 && (n = -2147483648), W(n = +n) && (n = i ? 0 : e.length - 1), n < 0 && (n = e.length + n), n >= e.length) {
                                if (i) return -1;
                                n = e.length - 1;
                            } else if (n < 0) {
                                if (!i) return -1;
                                n = 0;
                            }
                            if ("string" == typeof t && (t = s.from(t, r)), s.isBuffer(t)) return 0 === t.length ? -1 : m(e, t, n, r, i);
                            if ("number" == typeof t) return t &= 255, "function" == typeof Uint8Array.prototype.indexOf ? i ? Uint8Array.prototype.indexOf.call(e, t, n) : Uint8Array.prototype.lastIndexOf.call(e, t, n) : m(e, [
                                t
                            ], n, r, i);
                            throw new TypeError("val must be string, number or Buffer");
                        }
                        function m(e, t, n, r, i) {
                            var o, s = 1, a = e.length, l = t.length;
                            if (void 0 !== r && ("ucs2" === (r = String(r).toLowerCase()) || "ucs-2" === r || "utf16le" === r || "utf-16le" === r)) {
                                if (e.length < 2 || t.length < 2) return -1;
                                s = 2, a /= 2, l /= 2, n /= 2;
                            }
                            function c(e, t) {
                                return 1 === s ? e[t] : e.readUInt16BE(t * s);
                            }
                            if (i) {
                                var u = -1;
                                for(o = n; o < a; o++)if (c(e, o) === c(t, -1 === u ? 0 : o - u)) {
                                    if (-1 === u && (u = o), o - u + 1 === l) return u * s;
                                } else -1 !== u && (o -= o - u), u = -1;
                            } else for(n + l > a && (n = a - l), o = n; o >= 0; o--){
                                for(var f = !0, d = 0; d < l; d++)if (c(e, o + d) !== c(t, d)) {
                                    f = !1;
                                    break;
                                }
                                if (f) return o;
                            }
                            return -1;
                        }
                        function y(e, t, n, r) {
                            n = Number(n) || 0;
                            var i = e.length - n;
                            r ? (r = Number(r)) > i && (r = i) : r = i;
                            var o = t.length;
                            r > o / 2 && (r = o / 2);
                            for(var s = 0; s < r; ++s){
                                var a = parseInt(t.substr(2 * s, 2), 16);
                                if (W(a)) return s;
                                e[n + s] = a;
                            }
                            return s;
                        }
                        function b(e, t, n, r) {
                            return U(M(t, e.length - n), e, n, r);
                        }
                        function w(e, t, n, r) {
                            return U(function(e) {
                                for(var t = [], n = 0; n < e.length; ++n)t.push(255 & e.charCodeAt(n));
                                return t;
                            }(t), e, n, r);
                        }
                        function _(e, t, n, r) {
                            return w(e, t, n, r);
                        }
                        function v(e, t, n, r) {
                            return U(L(t), e, n, r);
                        }
                        function O(e, t, n, r) {
                            return U(function(e, t) {
                                for(var n, r, i, o = [], s = 0; s < e.length && !((t -= 2) < 0); ++s)r = (n = e.charCodeAt(s)) >> 8, i = n % 256, o.push(i), o.push(r);
                                return o;
                            }(t, e.length - n), e, n, r);
                        }
                        function E(e, n, r) {
                            return 0 === n && r === e.length ? t.fromByteArray(e) : t.fromByteArray(e.slice(n, r));
                        }
                        function k(e, t, n) {
                            n = Math.min(e.length, n);
                            for(var r = [], i = t; i < n;){
                                var o, s, a, l, c = e[i], u = null, f = c > 239 ? 4 : c > 223 ? 3 : c > 191 ? 2 : 1;
                                if (i + f <= n) switch(f){
                                    case 1:
                                        c < 128 && (u = c);
                                        break;
                                    case 2:
                                        128 == (192 & (o = e[i + 1])) && (l = (31 & c) << 6 | 63 & o) > 127 && (u = l);
                                        break;
                                    case 3:
                                        o = e[i + 1], s = e[i + 2], 128 == (192 & o) && 128 == (192 & s) && (l = (15 & c) << 12 | (63 & o) << 6 | 63 & s) > 2047 && (l < 55296 || l > 57343) && (u = l);
                                        break;
                                    case 4:
                                        o = e[i + 1], s = e[i + 2], a = e[i + 3], 128 == (192 & o) && 128 == (192 & s) && 128 == (192 & a) && (l = (15 & c) << 18 | (63 & o) << 12 | (63 & s) << 6 | 63 & a) > 65535 && l < 1114112 && (u = l);
                                }
                                null === u ? (u = 65533, f = 1) : u > 65535 && (u -= 65536, r.push(u >>> 10 & 1023 | 55296), u = 56320 | 1023 & u), r.push(u), i += f;
                            }
                            return function(e) {
                                var t = e.length;
                                if (t <= A) return String.fromCharCode.apply(String, e);
                                var n = "", r = 0;
                                for(; r < t;)n += String.fromCharCode.apply(String, e.slice(r, r += A));
                                return n;
                            }(r);
                        }
                        n.kMaxLength = i, s.TYPED_ARRAY_SUPPORT = function() {
                            try {
                                var e = new Uint8Array(1);
                                return e.__proto__ = {
                                    __proto__: Uint8Array.prototype,
                                    foo: function() {
                                        return 42;
                                    }
                                }, 42 === e.foo();
                            } catch (e) {
                                return !1;
                            }
                        }(), s.TYPED_ARRAY_SUPPORT || "undefined" == typeof console || "function" != typeof console.error || console.error("This browser lacks typed array (Uint8Array) support which is required by 'buffer' v5.x. Use 'buffer' v4.x if you require old browser support."), Object.defineProperty(s.prototype, "parent", {
                            enumerable: !0,
                            get: function() {
                                if (s.isBuffer(this)) return this.buffer;
                            }
                        }), Object.defineProperty(s.prototype, "offset", {
                            enumerable: !0,
                            get: function() {
                                if (s.isBuffer(this)) return this.byteOffset;
                            }
                        }), "undefined" != typeof Symbol && null != Symbol.species && s[Symbol.species] === s && Object.defineProperty(s, Symbol.species, {
                            value: null,
                            configurable: !0,
                            enumerable: !1,
                            writable: !1
                        }), s.poolSize = 8192, s.from = function(e, t, n) {
                            return a(e, t, n);
                        }, s.prototype.__proto__ = Uint8Array.prototype, s.__proto__ = Uint8Array, s.alloc = function(e, t, n) {
                            return function(e, t, n) {
                                return l(e), e <= 0 ? o(e) : void 0 !== t ? "string" == typeof n ? o(e).fill(t, n) : o(e).fill(t) : o(e);
                            }(e, t, n);
                        }, s.allocUnsafe = function(e) {
                            return c(e);
                        }, s.allocUnsafeSlow = function(e) {
                            return c(e);
                        }, s.isBuffer = function(e) {
                            return null != e && !0 === e._isBuffer && e !== s.prototype;
                        }, s.compare = function(e, t) {
                            if (z(e, Uint8Array) && (e = s.from(e, e.offset, e.byteLength)), z(t, Uint8Array) && (t = s.from(t, t.offset, t.byteLength)), !s.isBuffer(e) || !s.isBuffer(t)) throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');
                            if (e === t) return 0;
                            for(var n = e.length, r = t.length, i = 0, o = Math.min(n, r); i < o; ++i)if (e[i] !== t[i]) {
                                n = e[i], r = t[i];
                                break;
                            }
                            return n < r ? -1 : r < n ? 1 : 0;
                        }, s.isEncoding = function(e) {
                            switch(String(e).toLowerCase()){
                                case "hex":
                                case "utf8":
                                case "utf-8":
                                case "ascii":
                                case "latin1":
                                case "binary":
                                case "base64":
                                case "ucs2":
                                case "ucs-2":
                                case "utf16le":
                                case "utf-16le":
                                    return !0;
                                default:
                                    return !1;
                            }
                        }, s.concat = function(e, t) {
                            if (!Array.isArray(e)) throw new TypeError('"list" argument must be an Array of Buffers');
                            if (0 === e.length) return s.alloc(0);
                            var n;
                            if (void 0 === t) for(t = 0, n = 0; n < e.length; ++n)t += e[n].length;
                            var r = s.allocUnsafe(t), i = 0;
                            for(n = 0; n < e.length; ++n){
                                var o = e[n];
                                if (z(o, Uint8Array) && (o = s.from(o)), !s.isBuffer(o)) throw new TypeError('"list" argument must be an Array of Buffers');
                                o.copy(r, i), i += o.length;
                            }
                            return r;
                        }, s.byteLength = d, s.prototype._isBuffer = !0, s.prototype.swap16 = function() {
                            var e = this.length;
                            if (e % 2 != 0) throw new RangeError("Buffer size must be a multiple of 16-bits");
                            for(var t = 0; t < e; t += 2)h(this, t, t + 1);
                            return this;
                        }, s.prototype.swap32 = function() {
                            var e = this.length;
                            if (e % 4 != 0) throw new RangeError("Buffer size must be a multiple of 32-bits");
                            for(var t = 0; t < e; t += 4)h(this, t, t + 3), h(this, t + 1, t + 2);
                            return this;
                        }, s.prototype.swap64 = function() {
                            var e = this.length;
                            if (e % 8 != 0) throw new RangeError("Buffer size must be a multiple of 64-bits");
                            for(var t = 0; t < e; t += 8)h(this, t, t + 7), h(this, t + 1, t + 6), h(this, t + 2, t + 5), h(this, t + 3, t + 4);
                            return this;
                        }, s.prototype.toString = function() {
                            var e = this.length;
                            return 0 === e ? "" : 0 === arguments.length ? k(this, 0, e) : p.apply(this, arguments);
                        }, s.prototype.toLocaleString = s.prototype.toString, s.prototype.equals = function(e) {
                            if (!s.isBuffer(e)) throw new TypeError("Argument must be a Buffer");
                            return this === e || 0 === s.compare(this, e);
                        }, s.prototype.inspect = function() {
                            var e = "", t = n.INSPECT_MAX_BYTES;
                            return e = this.toString("hex", 0, t).replace(/(.{2})/g, "$1 ").trim(), this.length > t && (e += " ... "), "<Buffer " + e + ">";
                        }, s.prototype.compare = function(e, t, n, r, i) {
                            if (z(e, Uint8Array) && (e = s.from(e, e.offset, e.byteLength)), !s.isBuffer(e)) throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type ' + typeof e);
                            if (void 0 === t && (t = 0), void 0 === n && (n = e ? e.length : 0), void 0 === r && (r = 0), void 0 === i && (i = this.length), t < 0 || n > e.length || r < 0 || i > this.length) throw new RangeError("out of range index");
                            if (r >= i && t >= n) return 0;
                            if (r >= i) return -1;
                            if (t >= n) return 1;
                            if (this === e) return 0;
                            for(var o = (i >>>= 0) - (r >>>= 0), a = (n >>>= 0) - (t >>>= 0), l = Math.min(o, a), c = this.slice(r, i), u = e.slice(t, n), f = 0; f < l; ++f)if (c[f] !== u[f]) {
                                o = c[f], a = u[f];
                                break;
                            }
                            return o < a ? -1 : a < o ? 1 : 0;
                        }, s.prototype.includes = function(e, t, n) {
                            return -1 !== this.indexOf(e, t, n);
                        }, s.prototype.indexOf = function(e, t, n) {
                            return g(this, e, t, n, !0);
                        }, s.prototype.lastIndexOf = function(e, t, n) {
                            return g(this, e, t, n, !1);
                        }, s.prototype.write = function(e, t, n, r) {
                            if (void 0 === t) r = "utf8", n = this.length, t = 0;
                            else if (void 0 === n && "string" == typeof t) r = t, n = this.length, t = 0;
                            else {
                                if (!isFinite(t)) throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");
                                t >>>= 0, isFinite(n) ? (n >>>= 0, void 0 === r && (r = "utf8")) : (r = n, n = void 0);
                            }
                            var i = this.length - t;
                            if ((void 0 === n || n > i) && (n = i), e.length > 0 && (n < 0 || t < 0) || t > this.length) throw new RangeError("Attempt to write outside buffer bounds");
                            r || (r = "utf8");
                            for(var o = !1;;)switch(r){
                                case "hex":
                                    return y(this, e, t, n);
                                case "utf8":
                                case "utf-8":
                                    return b(this, e, t, n);
                                case "ascii":
                                    return w(this, e, t, n);
                                case "latin1":
                                case "binary":
                                    return _(this, e, t, n);
                                case "base64":
                                    return v(this, e, t, n);
                                case "ucs2":
                                case "ucs-2":
                                case "utf16le":
                                case "utf-16le":
                                    return O(this, e, t, n);
                                default:
                                    if (o) throw new TypeError("Unknown encoding: " + r);
                                    r = ("" + r).toLowerCase(), o = !0;
                            }
                        }, s.prototype.toJSON = function() {
                            return {
                                type: "Buffer",
                                data: Array.prototype.slice.call(this._arr || this, 0)
                            };
                        };
                        var A = 4096;
                        function S(e, t, n) {
                            var r = "";
                            n = Math.min(e.length, n);
                            for(var i = t; i < n; ++i)r += String.fromCharCode(127 & e[i]);
                            return r;
                        }
                        function x(e, t, n) {
                            var r = "";
                            n = Math.min(e.length, n);
                            for(var i = t; i < n; ++i)r += String.fromCharCode(e[i]);
                            return r;
                        }
                        function T(e, t, n) {
                            var r = e.length;
                            (!t || t < 0) && (t = 0), (!n || n < 0 || n > r) && (n = r);
                            for(var i = "", o = t; o < n; ++o)i += j(e[o]);
                            return i;
                        }
                        function I(e, t, n) {
                            for(var r = e.slice(t, n), i = "", o = 0; o < r.length; o += 2)i += String.fromCharCode(r[o] + 256 * r[o + 1]);
                            return i;
                        }
                        function R(e, t, n) {
                            if (e % 1 != 0 || e < 0) throw new RangeError("offset is not uint");
                            if (e + t > n) throw new RangeError("Trying to access beyond buffer length");
                        }
                        function C(e, t, n, r, i, o) {
                            if (!s.isBuffer(e)) throw new TypeError('"buffer" argument must be a Buffer instance');
                            if (t > i || t < o) throw new RangeError('"value" argument is out of bounds');
                            if (n + r > e.length) throw new RangeError("Index out of range");
                        }
                        function N(e, t, n, r, i, o) {
                            if (n + r > e.length) throw new RangeError("Index out of range");
                            if (n < 0) throw new RangeError("Index out of range");
                        }
                        function D(e, t, n, i, o) {
                            return t = +t, n >>>= 0, o || N(e, 0, n, 4), r.write(e, t, n, i, 23, 4), n + 4;
                        }
                        function P(e, t, n, i, o) {
                            return t = +t, n >>>= 0, o || N(e, 0, n, 8), r.write(e, t, n, i, 52, 8), n + 8;
                        }
                        s.prototype.slice = function(e, t) {
                            var n = this.length;
                            (e = ~~e) < 0 ? (e += n) < 0 && (e = 0) : e > n && (e = n), (t = void 0 === t ? n : ~~t) < 0 ? (t += n) < 0 && (t = 0) : t > n && (t = n), t < e && (t = e);
                            var r = this.subarray(e, t);
                            return r.__proto__ = s.prototype, r;
                        }, s.prototype.readUIntLE = function(e, t, n) {
                            e >>>= 0, t >>>= 0, n || R(e, t, this.length);
                            for(var r = this[e], i = 1, o = 0; ++o < t && (i *= 256);)r += this[e + o] * i;
                            return r;
                        }, s.prototype.readUIntBE = function(e, t, n) {
                            e >>>= 0, t >>>= 0, n || R(e, t, this.length);
                            for(var r = this[e + --t], i = 1; t > 0 && (i *= 256);)r += this[e + --t] * i;
                            return r;
                        }, s.prototype.readUInt8 = function(e, t) {
                            return e >>>= 0, t || R(e, 1, this.length), this[e];
                        }, s.prototype.readUInt16LE = function(e, t) {
                            return e >>>= 0, t || R(e, 2, this.length), this[e] | this[e + 1] << 8;
                        }, s.prototype.readUInt16BE = function(e, t) {
                            return e >>>= 0, t || R(e, 2, this.length), this[e] << 8 | this[e + 1];
                        }, s.prototype.readUInt32LE = function(e, t) {
                            return e >>>= 0, t || R(e, 4, this.length), (this[e] | this[e + 1] << 8 | this[e + 2] << 16) + 16777216 * this[e + 3];
                        }, s.prototype.readUInt32BE = function(e, t) {
                            return e >>>= 0, t || R(e, 4, this.length), 16777216 * this[e] + (this[e + 1] << 16 | this[e + 2] << 8 | this[e + 3]);
                        }, s.prototype.readIntLE = function(e, t, n) {
                            e >>>= 0, t >>>= 0, n || R(e, t, this.length);
                            for(var r = this[e], i = 1, o = 0; ++o < t && (i *= 256);)r += this[e + o] * i;
                            return r >= (i *= 128) && (r -= Math.pow(2, 8 * t)), r;
                        }, s.prototype.readIntBE = function(e, t, n) {
                            e >>>= 0, t >>>= 0, n || R(e, t, this.length);
                            for(var r = t, i = 1, o = this[e + --r]; r > 0 && (i *= 256);)o += this[e + --r] * i;
                            return o >= (i *= 128) && (o -= Math.pow(2, 8 * t)), o;
                        }, s.prototype.readInt8 = function(e, t) {
                            return e >>>= 0, t || R(e, 1, this.length), 128 & this[e] ? -1 * (255 - this[e] + 1) : this[e];
                        }, s.prototype.readInt16LE = function(e, t) {
                            e >>>= 0, t || R(e, 2, this.length);
                            var n = this[e] | this[e + 1] << 8;
                            return 32768 & n ? 4294901760 | n : n;
                        }, s.prototype.readInt16BE = function(e, t) {
                            e >>>= 0, t || R(e, 2, this.length);
                            var n = this[e + 1] | this[e] << 8;
                            return 32768 & n ? 4294901760 | n : n;
                        }, s.prototype.readInt32LE = function(e, t) {
                            return e >>>= 0, t || R(e, 4, this.length), this[e] | this[e + 1] << 8 | this[e + 2] << 16 | this[e + 3] << 24;
                        }, s.prototype.readInt32BE = function(e, t) {
                            return e >>>= 0, t || R(e, 4, this.length), this[e] << 24 | this[e + 1] << 16 | this[e + 2] << 8 | this[e + 3];
                        }, s.prototype.readFloatLE = function(e, t) {
                            return e >>>= 0, t || R(e, 4, this.length), r.read(this, e, !0, 23, 4);
                        }, s.prototype.readFloatBE = function(e, t) {
                            return e >>>= 0, t || R(e, 4, this.length), r.read(this, e, !1, 23, 4);
                        }, s.prototype.readDoubleLE = function(e, t) {
                            return e >>>= 0, t || R(e, 8, this.length), r.read(this, e, !0, 52, 8);
                        }, s.prototype.readDoubleBE = function(e, t) {
                            return e >>>= 0, t || R(e, 8, this.length), r.read(this, e, !1, 52, 8);
                        }, s.prototype.writeUIntLE = function(e, t, n, r) {
                            (e = +e, t >>>= 0, n >>>= 0, r) || C(this, e, t, n, Math.pow(2, 8 * n) - 1, 0);
                            var i = 1, o = 0;
                            for(this[t] = 255 & e; ++o < n && (i *= 256);)this[t + o] = e / i & 255;
                            return t + n;
                        }, s.prototype.writeUIntBE = function(e, t, n, r) {
                            (e = +e, t >>>= 0, n >>>= 0, r) || C(this, e, t, n, Math.pow(2, 8 * n) - 1, 0);
                            var i = n - 1, o = 1;
                            for(this[t + i] = 255 & e; --i >= 0 && (o *= 256);)this[t + i] = e / o & 255;
                            return t + n;
                        }, s.prototype.writeUInt8 = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 1, 255, 0), this[t] = 255 & e, t + 1;
                        }, s.prototype.writeUInt16LE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 2, 65535, 0), this[t] = 255 & e, this[t + 1] = e >>> 8, t + 2;
                        }, s.prototype.writeUInt16BE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 2, 65535, 0), this[t] = e >>> 8, this[t + 1] = 255 & e, t + 2;
                        }, s.prototype.writeUInt32LE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 4, 4294967295, 0), this[t + 3] = e >>> 24, this[t + 2] = e >>> 16, this[t + 1] = e >>> 8, this[t] = 255 & e, t + 4;
                        }, s.prototype.writeUInt32BE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 4, 4294967295, 0), this[t] = e >>> 24, this[t + 1] = e >>> 16, this[t + 2] = e >>> 8, this[t + 3] = 255 & e, t + 4;
                        }, s.prototype.writeIntLE = function(e, t, n, r) {
                            if (e = +e, t >>>= 0, !r) {
                                var i = Math.pow(2, 8 * n - 1);
                                C(this, e, t, n, i - 1, -i);
                            }
                            var o = 0, s = 1, a = 0;
                            for(this[t] = 255 & e; ++o < n && (s *= 256);)e < 0 && 0 === a && 0 !== this[t + o - 1] && (a = 1), this[t + o] = (e / s >> 0) - a & 255;
                            return t + n;
                        }, s.prototype.writeIntBE = function(e, t, n, r) {
                            if (e = +e, t >>>= 0, !r) {
                                var i = Math.pow(2, 8 * n - 1);
                                C(this, e, t, n, i - 1, -i);
                            }
                            var o = n - 1, s = 1, a = 0;
                            for(this[t + o] = 255 & e; --o >= 0 && (s *= 256);)e < 0 && 0 === a && 0 !== this[t + o + 1] && (a = 1), this[t + o] = (e / s >> 0) - a & 255;
                            return t + n;
                        }, s.prototype.writeInt8 = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 1, 127, -128), e < 0 && (e = 255 + e + 1), this[t] = 255 & e, t + 1;
                        }, s.prototype.writeInt16LE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 2, 32767, -32768), this[t] = 255 & e, this[t + 1] = e >>> 8, t + 2;
                        }, s.prototype.writeInt16BE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 2, 32767, -32768), this[t] = e >>> 8, this[t + 1] = 255 & e, t + 2;
                        }, s.prototype.writeInt32LE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 4, **********, -2147483648), this[t] = 255 & e, this[t + 1] = e >>> 8, this[t + 2] = e >>> 16, this[t + 3] = e >>> 24, t + 4;
                        }, s.prototype.writeInt32BE = function(e, t, n) {
                            return e = +e, t >>>= 0, n || C(this, e, t, 4, **********, -2147483648), e < 0 && (e = 4294967295 + e + 1), this[t] = e >>> 24, this[t + 1] = e >>> 16, this[t + 2] = e >>> 8, this[t + 3] = 255 & e, t + 4;
                        }, s.prototype.writeFloatLE = function(e, t, n) {
                            return D(this, e, t, !0, n);
                        }, s.prototype.writeFloatBE = function(e, t, n) {
                            return D(this, e, t, !1, n);
                        }, s.prototype.writeDoubleLE = function(e, t, n) {
                            return P(this, e, t, !0, n);
                        }, s.prototype.writeDoubleBE = function(e, t, n) {
                            return P(this, e, t, !1, n);
                        }, s.prototype.copy = function(e, t, n, r) {
                            if (!s.isBuffer(e)) throw new TypeError("argument should be a Buffer");
                            if (n || (n = 0), r || 0 === r || (r = this.length), t >= e.length && (t = e.length), t || (t = 0), r > 0 && r < n && (r = n), r === n) return 0;
                            if (0 === e.length || 0 === this.length) return 0;
                            if (t < 0) throw new RangeError("targetStart out of bounds");
                            if (n < 0 || n >= this.length) throw new RangeError("Index out of range");
                            if (r < 0) throw new RangeError("sourceEnd out of bounds");
                            r > this.length && (r = this.length), e.length - t < r - n && (r = e.length - t + n);
                            var i = r - n;
                            if (this === e && "function" == typeof Uint8Array.prototype.copyWithin) this.copyWithin(t, n, r);
                            else if (this === e && n < t && t < r) for(var o = i - 1; o >= 0; --o)e[o + t] = this[o + n];
                            else Uint8Array.prototype.set.call(e, this.subarray(n, r), t);
                            return i;
                        }, s.prototype.fill = function(e, t, n, r) {
                            if ("string" == typeof e) {
                                if ("string" == typeof t ? (r = t, t = 0, n = this.length) : "string" == typeof n && (r = n, n = this.length), void 0 !== r && "string" != typeof r) throw new TypeError("encoding must be a string");
                                if ("string" == typeof r && !s.isEncoding(r)) throw new TypeError("Unknown encoding: " + r);
                                if (1 === e.length) {
                                    var i = e.charCodeAt(0);
                                    ("utf8" === r && i < 128 || "latin1" === r) && (e = i);
                                }
                            } else "number" == typeof e && (e &= 255);
                            if (t < 0 || this.length < t || this.length < n) throw new RangeError("Out of range index");
                            if (n <= t) return this;
                            var o;
                            if (t >>>= 0, n = void 0 === n ? this.length : n >>> 0, e || (e = 0), "number" == typeof e) for(o = t; o < n; ++o)this[o] = e;
                            else {
                                var a = s.isBuffer(e) ? e : s.from(e, r), l = a.length;
                                if (0 === l) throw new TypeError('The value "' + e + '" is invalid for argument "value"');
                                for(o = 0; o < n - t; ++o)this[o + t] = a[o % l];
                            }
                            return this;
                        };
                        var B = /[^+/0-9A-Za-z-_]/g;
                        function j(e) {
                            return e < 16 ? "0" + e.toString(16) : e.toString(16);
                        }
                        function M(e, t) {
                            var n;
                            t = t || 1 / 0;
                            for(var r = e.length, i = null, o = [], s = 0; s < r; ++s){
                                if ((n = e.charCodeAt(s)) > 55295 && n < 57344) {
                                    if (!i) {
                                        if (n > 56319) {
                                            (t -= 3) > -1 && o.push(239, 191, 189);
                                            continue;
                                        }
                                        if (s + 1 === r) {
                                            (t -= 3) > -1 && o.push(239, 191, 189);
                                            continue;
                                        }
                                        i = n;
                                        continue;
                                    }
                                    if (n < 56320) {
                                        (t -= 3) > -1 && o.push(239, 191, 189), i = n;
                                        continue;
                                    }
                                    n = 65536 + (i - 55296 << 10 | n - 56320);
                                } else i && (t -= 3) > -1 && o.push(239, 191, 189);
                                if (i = null, n < 128) {
                                    if ((t -= 1) < 0) break;
                                    o.push(n);
                                } else if (n < 2048) {
                                    if ((t -= 2) < 0) break;
                                    o.push(n >> 6 | 192, 63 & n | 128);
                                } else if (n < 65536) {
                                    if ((t -= 3) < 0) break;
                                    o.push(n >> 12 | 224, n >> 6 & 63 | 128, 63 & n | 128);
                                } else {
                                    if (!(n < 1114112)) throw new Error("Invalid code point");
                                    if ((t -= 4) < 0) break;
                                    o.push(n >> 18 | 240, n >> 12 & 63 | 128, n >> 6 & 63 | 128, 63 & n | 128);
                                }
                            }
                            return o;
                        }
                        function L(e) {
                            return t.toByteArray(function(e) {
                                if ((e = (e = e.split("=")[0]).trim().replace(B, "")).length < 2) return "";
                                for(; e.length % 4 != 0;)e += "=";
                                return e;
                            }(e));
                        }
                        function U(e, t, n, r) {
                            for(var i = 0; i < r && !(i + n >= t.length || i >= e.length); ++i)t[i + n] = e[i];
                            return i;
                        }
                        function z(e, t) {
                            return e instanceof t || null != e && null != e.constructor && null != e.constructor.name && e.constructor.name === t.name;
                        }
                        function W(e) {
                            return e != e;
                        }
                    }).call(this);
                }).call(this, e("buffer").Buffer);
            },
            {
                "base64-js": 24,
                buffer: 25,
                ieee754: 26
            }
        ],
        26: [
            function(e, t, n) {
                /*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */ n.read = function(e, t, n, r, i) {
                    var o, s, a = 8 * i - r - 1, l = (1 << a) - 1, c = l >> 1, u = -7, f = n ? i - 1 : 0, d = n ? -1 : 1, p = e[t + f];
                    for(f += d, o = p & (1 << -u) - 1, p >>= -u, u += a; u > 0; o = 256 * o + e[t + f], f += d, u -= 8);
                    for(s = o & (1 << -u) - 1, o >>= -u, u += r; u > 0; s = 256 * s + e[t + f], f += d, u -= 8);
                    if (0 === o) o = 1 - c;
                    else {
                        if (o === l) return s ? NaN : 1 / 0 * (p ? -1 : 1);
                        s += Math.pow(2, r), o -= c;
                    }
                    return (p ? -1 : 1) * s * Math.pow(2, o - r);
                }, n.write = function(e, t, n, r, i, o) {
                    var s, a, l, c = 8 * o - i - 1, u = (1 << c) - 1, f = u >> 1, d = 23 === i ? Math.pow(2, -24) - Math.pow(2, -77) : 0, p = r ? 0 : o - 1, h = r ? 1 : -1, g = t < 0 || 0 === t && 1 / t < 0 ? 1 : 0;
                    for(t = Math.abs(t), isNaN(t) || t === 1 / 0 ? (a = isNaN(t) ? 1 : 0, s = u) : (s = Math.floor(Math.log(t) / Math.LN2), t * (l = Math.pow(2, -s)) < 1 && (s--, l *= 2), (t += s + f >= 1 ? d / l : d * Math.pow(2, 1 - f)) * l >= 2 && (s++, l /= 2), s + f >= u ? (a = 0, s = u) : s + f >= 1 ? (a = (t * l - 1) * Math.pow(2, i), s += f) : (a = t * Math.pow(2, f - 1) * Math.pow(2, i), s = 0)); i >= 8; e[n + p] = 255 & a, p += h, a /= 256, i -= 8);
                    for(s = s << i | a, c += i; c > 0; e[n + p] = 255 & s, p += h, s /= 256, c -= 8);
                    e[n + p - h] |= 128 * g;
                };
            },
            {}
        ],
        27: [
            function(e, t, n) {
                "use strict";
                const { Deflate: r , deflate: i , deflateRaw: o , gzip: s  } = e("./lib/deflate"), { Inflate: a , inflate: l , inflateRaw: c , ungzip: u  } = e("./lib/inflate"), f = e("./lib/zlib/constants");
                t.exports.Deflate = r, t.exports.deflate = i, t.exports.deflateRaw = o, t.exports.gzip = s, t.exports.Inflate = a, t.exports.inflate = l, t.exports.inflateRaw = c, t.exports.ungzip = u, t.exports.constants = f;
            },
            {
                "./lib/deflate": 28,
                "./lib/inflate": 29,
                "./lib/zlib/constants": 33
            }
        ],
        28: [
            function(e, t, n) {
                "use strict";
                const r = e("./zlib/deflate"), i = e("./utils/common"), o = e("./utils/strings"), s = e("./zlib/messages"), a = e("./zlib/zstream"), l = Object.prototype.toString, { Z_NO_FLUSH: c , Z_SYNC_FLUSH: u , Z_FULL_FLUSH: f , Z_FINISH: d , Z_OK: p , Z_STREAM_END: h , Z_DEFAULT_COMPRESSION: g , Z_DEFAULT_STRATEGY: m , Z_DEFLATED: y  } = e("./zlib/constants");
                function b(e) {
                    this.options = i.assign({
                        level: g,
                        method: y,
                        chunkSize: 16384,
                        windowBits: 15,
                        memLevel: 8,
                        strategy: m
                    }, e || {});
                    let t = this.options;
                    t.raw && t.windowBits > 0 ? t.windowBits = -t.windowBits : t.gzip && t.windowBits > 0 && t.windowBits < 16 && (t.windowBits += 16), this.err = 0, this.msg = "", this.ended = !1, this.chunks = [], this.strm = new a, this.strm.avail_out = 0;
                    let n = r.deflateInit2(this.strm, t.level, t.method, t.windowBits, t.memLevel, t.strategy);
                    if (n !== p) throw new Error(s[n]);
                    if (t.header && r.deflateSetHeader(this.strm, t.header), t.dictionary) {
                        let e;
                        if (e = "string" == typeof t.dictionary ? o.string2buf(t.dictionary) : "[object ArrayBuffer]" === l.call(t.dictionary) ? new Uint8Array(t.dictionary) : t.dictionary, n = r.deflateSetDictionary(this.strm, e), n !== p) throw new Error(s[n]);
                        this._dict_set = !0;
                    }
                }
                function w(e, t) {
                    const n = new b(t);
                    if (n.push(e, !0), n.err) throw n.msg || s[n.err];
                    return n.result;
                }
                b.prototype.push = function(e, t) {
                    const n = this.strm, i = this.options.chunkSize;
                    let s, a;
                    if (this.ended) return !1;
                    for(a = t === ~~t ? t : !0 === t ? d : c, "string" == typeof e ? n.input = o.string2buf(e) : "[object ArrayBuffer]" === l.call(e) ? n.input = new Uint8Array(e) : n.input = e, n.next_in = 0, n.avail_in = n.input.length;;)if (0 === n.avail_out && (n.output = new Uint8Array(i), n.next_out = 0, n.avail_out = i), (a === u || a === f) && n.avail_out <= 6) this.onData(n.output.subarray(0, n.next_out)), n.avail_out = 0;
                    else {
                        if (s = r.deflate(n, a), s === h) return n.next_out > 0 && this.onData(n.output.subarray(0, n.next_out)), s = r.deflateEnd(this.strm), this.onEnd(s), this.ended = !0, s === p;
                        if (0 !== n.avail_out) {
                            if (a > 0 && n.next_out > 0) this.onData(n.output.subarray(0, n.next_out)), n.avail_out = 0;
                            else if (0 === n.avail_in) break;
                        } else this.onData(n.output);
                    }
                    return !0;
                }, b.prototype.onData = function(e) {
                    this.chunks.push(e);
                }, b.prototype.onEnd = function(e) {
                    e === p && (this.result = i.flattenChunks(this.chunks)), this.chunks = [], this.err = e, this.msg = this.strm.msg;
                }, t.exports.Deflate = b, t.exports.deflate = w, t.exports.deflateRaw = function(e, t) {
                    return (t = t || {}).raw = !0, w(e, t);
                }, t.exports.gzip = function(e, t) {
                    return (t = t || {}).gzip = !0, w(e, t);
                }, t.exports.constants = e("./zlib/constants");
            },
            {
                "./utils/common": 30,
                "./utils/strings": 31,
                "./zlib/constants": 33,
                "./zlib/deflate": 35,
                "./zlib/messages": 40,
                "./zlib/zstream": 42
            }
        ],
        29: [
            function(e, t, n) {
                "use strict";
                const r = e("./zlib/inflate"), i = e("./utils/common"), o = e("./utils/strings"), s = e("./zlib/messages"), a = e("./zlib/zstream"), l = e("./zlib/gzheader"), c = Object.prototype.toString, { Z_NO_FLUSH: u , Z_FINISH: f , Z_OK: d , Z_STREAM_END: p , Z_NEED_DICT: h , Z_STREAM_ERROR: g , Z_DATA_ERROR: m , Z_MEM_ERROR: y  } = e("./zlib/constants");
                function b(e) {
                    this.options = i.assign({
                        chunkSize: 65536,
                        windowBits: 15,
                        to: ""
                    }, e || {});
                    const t = this.options;
                    t.raw && t.windowBits >= 0 && t.windowBits < 16 && (t.windowBits = -t.windowBits, 0 === t.windowBits && (t.windowBits = -15)), !(t.windowBits >= 0 && t.windowBits < 16) || e && e.windowBits || (t.windowBits += 32), t.windowBits > 15 && t.windowBits < 48 && 0 == (15 & t.windowBits) && (t.windowBits |= 15), this.err = 0, this.msg = "", this.ended = !1, this.chunks = [], this.strm = new a, this.strm.avail_out = 0;
                    let n = r.inflateInit2(this.strm, t.windowBits);
                    if (n !== d) throw new Error(s[n]);
                    if (this.header = new l, r.inflateGetHeader(this.strm, this.header), t.dictionary && ("string" == typeof t.dictionary ? t.dictionary = o.string2buf(t.dictionary) : "[object ArrayBuffer]" === c.call(t.dictionary) && (t.dictionary = new Uint8Array(t.dictionary)), t.raw && (n = r.inflateSetDictionary(this.strm, t.dictionary), n !== d))) throw new Error(s[n]);
                }
                function w(e, t) {
                    const n = new b(t);
                    if (n.push(e), n.err) throw n.msg || s[n.err];
                    return n.result;
                }
                b.prototype.push = function(e, t) {
                    const n = this.strm, i = this.options.chunkSize, s = this.options.dictionary;
                    let a, l, b;
                    if (this.ended) return !1;
                    for(l = t === ~~t ? t : !0 === t ? f : u, "[object ArrayBuffer]" === c.call(e) ? n.input = new Uint8Array(e) : n.input = e, n.next_in = 0, n.avail_in = n.input.length;;){
                        for(0 === n.avail_out && (n.output = new Uint8Array(i), n.next_out = 0, n.avail_out = i), a = r.inflate(n, l), a === h && s && (a = r.inflateSetDictionary(n, s), a === d ? a = r.inflate(n, l) : a === m && (a = h)); n.avail_in > 0 && a === p && n.state.wrap > 0 && 0 !== e[n.next_in];)r.inflateReset(n), a = r.inflate(n, l);
                        switch(a){
                            case g:
                            case m:
                            case h:
                            case y:
                                return this.onEnd(a), this.ended = !0, !1;
                        }
                        if (b = n.avail_out, n.next_out && (0 === n.avail_out || a === p)) {
                            if ("string" === this.options.to) {
                                let e = o.utf8border(n.output, n.next_out), t = n.next_out - e, r = o.buf2string(n.output, e);
                                n.next_out = t, n.avail_out = i - t, t && n.output.set(n.output.subarray(e, e + t), 0), this.onData(r);
                            } else this.onData(n.output.length === n.next_out ? n.output : n.output.subarray(0, n.next_out));
                        }
                        if (a !== d || 0 !== b) {
                            if (a === p) return a = r.inflateEnd(this.strm), this.onEnd(a), this.ended = !0, !0;
                            if (0 === n.avail_in) break;
                        }
                    }
                    return !0;
                }, b.prototype.onData = function(e) {
                    this.chunks.push(e);
                }, b.prototype.onEnd = function(e) {
                    e === d && ("string" === this.options.to ? this.result = this.chunks.join("") : this.result = i.flattenChunks(this.chunks)), this.chunks = [], this.err = e, this.msg = this.strm.msg;
                }, t.exports.Inflate = b, t.exports.inflate = w, t.exports.inflateRaw = function(e, t) {
                    return (t = t || {}).raw = !0, w(e, t);
                }, t.exports.ungzip = w, t.exports.constants = e("./zlib/constants");
            },
            {
                "./utils/common": 30,
                "./utils/strings": 31,
                "./zlib/constants": 33,
                "./zlib/gzheader": 36,
                "./zlib/inflate": 38,
                "./zlib/messages": 40,
                "./zlib/zstream": 42
            }
        ],
        30: [
            function(e, t, n) {
                "use strict";
                const r = (e, t)=>Object.prototype.hasOwnProperty.call(e, t);
                t.exports.assign = function(e) {
                    const t = Array.prototype.slice.call(arguments, 1);
                    for(; t.length;){
                        const n = t.shift();
                        if (n) {
                            if ("object" != typeof n) throw new TypeError(n + "must be non-object");
                            for(const t in n)r(n, t) && (e[t] = n[t]);
                        }
                    }
                    return e;
                }, t.exports.flattenChunks = (e)=>{
                    let t = 0;
                    for(let n = 0, r = e.length; n < r; n++)t += e[n].length;
                    const n = new Uint8Array(t);
                    for(let t = 0, r = 0, i = e.length; t < i; t++){
                        let i = e[t];
                        n.set(i, r), r += i.length;
                    }
                    return n;
                };
            },
            {}
        ],
        31: [
            function(e, t, n) {
                "use strict";
                let r = !0;
                try {
                    String.fromCharCode.apply(null, new Uint8Array(1));
                } catch (e) {
                    r = !1;
                }
                const i = new Uint8Array(256);
                for(let e = 0; e < 256; e++)i[e] = e >= 252 ? 6 : e >= 248 ? 5 : e >= 240 ? 4 : e >= 224 ? 3 : e >= 192 ? 2 : 1;
                i[254] = i[254] = 1, t.exports.string2buf = (e)=>{
                    if ("function" == typeof TextEncoder && TextEncoder.prototype.encode) return (new TextEncoder).encode(e);
                    let t, n, r, i, o, s = e.length, a = 0;
                    for(i = 0; i < s; i++)n = e.charCodeAt(i), 55296 == (64512 & n) && i + 1 < s && (r = e.charCodeAt(i + 1), 56320 == (64512 & r) && (n = 65536 + (n - 55296 << 10) + (r - 56320), i++)), a += n < 128 ? 1 : n < 2048 ? 2 : n < 65536 ? 3 : 4;
                    for(t = new Uint8Array(a), o = 0, i = 0; o < a; i++)n = e.charCodeAt(i), 55296 == (64512 & n) && i + 1 < s && (r = e.charCodeAt(i + 1), 56320 == (64512 & r) && (n = 65536 + (n - 55296 << 10) + (r - 56320), i++)), n < 128 ? t[o++] = n : n < 2048 ? (t[o++] = 192 | n >>> 6, t[o++] = 128 | 63 & n) : n < 65536 ? (t[o++] = 224 | n >>> 12, t[o++] = 128 | n >>> 6 & 63, t[o++] = 128 | 63 & n) : (t[o++] = 240 | n >>> 18, t[o++] = 128 | n >>> 12 & 63, t[o++] = 128 | n >>> 6 & 63, t[o++] = 128 | 63 & n);
                    return t;
                };
                t.exports.buf2string = (e, t)=>{
                    const n = t || e.length;
                    if ("function" == typeof TextDecoder && TextDecoder.prototype.decode) return (new TextDecoder).decode(e.subarray(0, t));
                    let o, s;
                    const a = new Array(2 * n);
                    for(s = 0, o = 0; o < n;){
                        let t = e[o++];
                        if (t < 128) {
                            a[s++] = t;
                            continue;
                        }
                        let r = i[t];
                        if (r > 4) a[s++] = 65533, o += r - 1;
                        else {
                            for(t &= 2 === r ? 31 : 3 === r ? 15 : 7; r > 1 && o < n;)t = t << 6 | 63 & e[o++], r--;
                            r > 1 ? a[s++] = 65533 : t < 65536 ? a[s++] = t : (t -= 65536, a[s++] = 55296 | t >> 10 & 1023, a[s++] = 56320 | 1023 & t);
                        }
                    }
                    return ((e, t)=>{
                        if (t < 65534 && e.subarray && r) return String.fromCharCode.apply(null, e.length === t ? e : e.subarray(0, t));
                        let n = "";
                        for(let r = 0; r < t; r++)n += String.fromCharCode(e[r]);
                        return n;
                    })(a, s);
                }, t.exports.utf8border = (e, t)=>{
                    (t = t || e.length) > e.length && (t = e.length);
                    let n = t - 1;
                    for(; n >= 0 && 128 == (192 & e[n]);)n--;
                    return n < 0 || 0 === n ? t : n + i[e[n]] > t ? n : t;
                };
            },
            {}
        ],
        32: [
            function(e, t, n) {
                "use strict";
                t.exports = (e, t, n, r)=>{
                    let i = 65535 & e | 0, o = e >>> 16 & 65535 | 0, s = 0;
                    for(; 0 !== n;){
                        s = n > 2e3 ? 2e3 : n, n -= s;
                        do i = i + t[r++] | 0, o = o + i | 0;
                        while (--s);
                        i %= 65521, o %= 65521;
                    }
                    return i | o << 16 | 0;
                };
            },
            {}
        ],
        33: [
            function(e, t, n) {
                "use strict";
                t.exports = {
                    Z_NO_FLUSH: 0,
                    Z_PARTIAL_FLUSH: 1,
                    Z_SYNC_FLUSH: 2,
                    Z_FULL_FLUSH: 3,
                    Z_FINISH: 4,
                    Z_BLOCK: 5,
                    Z_TREES: 6,
                    Z_OK: 0,
                    Z_STREAM_END: 1,
                    Z_NEED_DICT: 2,
                    Z_ERRNO: -1,
                    Z_STREAM_ERROR: -2,
                    Z_DATA_ERROR: -3,
                    Z_MEM_ERROR: -4,
                    Z_BUF_ERROR: -5,
                    Z_NO_COMPRESSION: 0,
                    Z_BEST_SPEED: 1,
                    Z_BEST_COMPRESSION: 9,
                    Z_DEFAULT_COMPRESSION: -1,
                    Z_FILTERED: 1,
                    Z_HUFFMAN_ONLY: 2,
                    Z_RLE: 3,
                    Z_FIXED: 4,
                    Z_DEFAULT_STRATEGY: 0,
                    Z_BINARY: 0,
                    Z_TEXT: 1,
                    Z_UNKNOWN: 2,
                    Z_DEFLATED: 8
                };
            },
            {}
        ],
        34: [
            function(e, t, n) {
                "use strict";
                const r = new Uint32Array((()=>{
                    let e, t = [];
                    for(var n = 0; n < 256; n++){
                        e = n;
                        for(var r = 0; r < 8; r++)e = 1 & e ? 3988292384 ^ e >>> 1 : e >>> 1;
                        t[n] = e;
                    }
                    return t;
                })());
                t.exports = (e, t, n, i)=>{
                    const o = r, s = i + n;
                    e ^= -1;
                    for(let n = i; n < s; n++)e = e >>> 8 ^ o[255 & (e ^ t[n])];
                    return -1 ^ e;
                };
            },
            {}
        ],
        35: [
            function(e, t, n) {
                "use strict";
                const { _tr_init: r , _tr_stored_block: i , _tr_flush_block: o , _tr_tally: s , _tr_align: a  } = e("./trees"), l = e("./adler32"), c = e("./crc32"), u = e("./messages"), { Z_NO_FLUSH: f , Z_PARTIAL_FLUSH: d , Z_FULL_FLUSH: p , Z_FINISH: h , Z_BLOCK: g , Z_OK: m , Z_STREAM_END: y , Z_STREAM_ERROR: b , Z_DATA_ERROR: w , Z_BUF_ERROR: _ , Z_DEFAULT_COMPRESSION: v , Z_FILTERED: O , Z_HUFFMAN_ONLY: E , Z_RLE: k , Z_FIXED: A , Z_DEFAULT_STRATEGY: S , Z_UNKNOWN: x , Z_DEFLATED: T  } = e("./constants"), I = 258, R = 262, C = 103, N = 113, D = 666, P = (e, t)=>(e.msg = u[t], t), B = (e)=>(e << 1) - (e > 4 ? 9 : 0), j = (e)=>{
                    let t = e.length;
                    for(; --t >= 0;)e[t] = 0;
                };
                let M = (e, t, n)=>(t << e.hash_shift ^ n) & e.hash_mask;
                const L = (e)=>{
                    const t = e.state;
                    let n = t.pending;
                    n > e.avail_out && (n = e.avail_out), 0 !== n && (e.output.set(t.pending_buf.subarray(t.pending_out, t.pending_out + n), e.next_out), e.next_out += n, t.pending_out += n, e.total_out += n, e.avail_out -= n, t.pending -= n, 0 === t.pending && (t.pending_out = 0));
                }, U = (e, t)=>{
                    o(e, e.block_start >= 0 ? e.block_start : -1, e.strstart - e.block_start, t), e.block_start = e.strstart, L(e.strm);
                }, z = (e, t)=>{
                    e.pending_buf[e.pending++] = t;
                }, W = (e, t)=>{
                    e.pending_buf[e.pending++] = t >>> 8 & 255, e.pending_buf[e.pending++] = 255 & t;
                }, Z = (e, t, n, r)=>{
                    let i = e.avail_in;
                    return i > r && (i = r), 0 === i ? 0 : (e.avail_in -= i, t.set(e.input.subarray(e.next_in, e.next_in + i), n), 1 === e.state.wrap ? e.adler = l(e.adler, t, i, n) : 2 === e.state.wrap && (e.adler = c(e.adler, t, i, n)), e.next_in += i, e.total_in += i, i);
                }, F = (e, t)=>{
                    let n, r, i = e.max_chain_length, o = e.strstart, s = e.prev_length, a = e.nice_match;
                    const l = e.strstart > e.w_size - R ? e.strstart - (e.w_size - R) : 0, c = e.window, u = e.w_mask, f = e.prev, d = e.strstart + I;
                    let p = c[o + s - 1], h = c[o + s];
                    e.prev_length >= e.good_match && (i >>= 2), a > e.lookahead && (a = e.lookahead);
                    do if (n = t, c[n + s] === h && c[n + s - 1] === p && c[n] === c[o] && c[++n] === c[o + 1]) {
                        o += 2, n++;
                        do ;
                        while (c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && c[++o] === c[++n] && o < d);
                        if (r = I - (d - o), o = d - I, r > s) {
                            if (e.match_start = t, s = r, r >= a) break;
                            p = c[o + s - 1], h = c[o + s];
                        }
                    }
                    while ((t = f[t & u]) > l && 0 != --i);
                    return s <= e.lookahead ? s : e.lookahead;
                }, H = (e)=>{
                    const t = e.w_size;
                    let n, r, i, o, s;
                    do {
                        if (o = e.window_size - e.lookahead - e.strstart, e.strstart >= t + (t - R)) {
                            e.window.set(e.window.subarray(t, t + t), 0), e.match_start -= t, e.strstart -= t, e.block_start -= t, r = e.hash_size, n = r;
                            do i = e.head[--n], e.head[n] = i >= t ? i - t : 0;
                            while (--r);
                            r = t, n = r;
                            do i = e.prev[--n], e.prev[n] = i >= t ? i - t : 0;
                            while (--r);
                            o += t;
                        }
                        if (0 === e.strm.avail_in) break;
                        if (r = Z(e.strm, e.window, e.strstart + e.lookahead, o), e.lookahead += r, e.lookahead + e.insert >= 3) for(s = e.strstart - e.insert, e.ins_h = e.window[s], e.ins_h = M(e, e.ins_h, e.window[s + 1]); e.insert && (e.ins_h = M(e, e.ins_h, e.window[s + 3 - 1]), e.prev[s & e.w_mask] = e.head[e.ins_h], e.head[e.ins_h] = s, s++, e.insert--, !(e.lookahead + e.insert < 3)););
                    }while (e.lookahead < R && 0 !== e.strm.avail_in);
                }, G = (e, t)=>{
                    let n, r;
                    for(;;){
                        if (e.lookahead < R) {
                            if (H(e), e.lookahead < R && t === f) return 1;
                            if (0 === e.lookahead) break;
                        }
                        if (n = 0, e.lookahead >= 3 && (e.ins_h = M(e, e.ins_h, e.window[e.strstart + 3 - 1]), n = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h], e.head[e.ins_h] = e.strstart), 0 !== n && e.strstart - n <= e.w_size - R && (e.match_length = F(e, n)), e.match_length >= 3) {
                            if (r = s(e, e.strstart - e.match_start, e.match_length - 3), e.lookahead -= e.match_length, e.match_length <= e.max_lazy_match && e.lookahead >= 3) {
                                e.match_length--;
                                do e.strstart++, e.ins_h = M(e, e.ins_h, e.window[e.strstart + 3 - 1]), n = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h], e.head[e.ins_h] = e.strstart;
                                while (0 != --e.match_length);
                                e.strstart++;
                            } else e.strstart += e.match_length, e.match_length = 0, e.ins_h = e.window[e.strstart], e.ins_h = M(e, e.ins_h, e.window[e.strstart + 1]);
                        } else r = s(e, 0, e.window[e.strstart]), e.lookahead--, e.strstart++;
                        if (r && (U(e, !1), 0 === e.strm.avail_out)) return 1;
                    }
                    return e.insert = e.strstart < 2 ? e.strstart : 2, t === h ? (U(e, !0), 0 === e.strm.avail_out ? 3 : 4) : e.last_lit && (U(e, !1), 0 === e.strm.avail_out) ? 1 : 2;
                }, V = (e, t)=>{
                    let n, r, i;
                    for(;;){
                        if (e.lookahead < R) {
                            if (H(e), e.lookahead < R && t === f) return 1;
                            if (0 === e.lookahead) break;
                        }
                        if (n = 0, e.lookahead >= 3 && (e.ins_h = M(e, e.ins_h, e.window[e.strstart + 3 - 1]), n = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h], e.head[e.ins_h] = e.strstart), e.prev_length = e.match_length, e.prev_match = e.match_start, e.match_length = 2, 0 !== n && e.prev_length < e.max_lazy_match && e.strstart - n <= e.w_size - R && (e.match_length = F(e, n), e.match_length <= 5 && (e.strategy === O || 3 === e.match_length && e.strstart - e.match_start > 4096) && (e.match_length = 2)), e.prev_length >= 3 && e.match_length <= e.prev_length) {
                            i = e.strstart + e.lookahead - 3, r = s(e, e.strstart - 1 - e.prev_match, e.prev_length - 3), e.lookahead -= e.prev_length - 1, e.prev_length -= 2;
                            do ++e.strstart <= i && (e.ins_h = M(e, e.ins_h, e.window[e.strstart + 3 - 1]), n = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h], e.head[e.ins_h] = e.strstart);
                            while (0 != --e.prev_length);
                            if (e.match_available = 0, e.match_length = 2, e.strstart++, r && (U(e, !1), 0 === e.strm.avail_out)) return 1;
                        } else if (e.match_available) {
                            if (r = s(e, 0, e.window[e.strstart - 1]), r && U(e, !1), e.strstart++, e.lookahead--, 0 === e.strm.avail_out) return 1;
                        } else e.match_available = 1, e.strstart++, e.lookahead--;
                    }
                    return e.match_available && (r = s(e, 0, e.window[e.strstart - 1]), e.match_available = 0), e.insert = e.strstart < 2 ? e.strstart : 2, t === h ? (U(e, !0), 0 === e.strm.avail_out ? 3 : 4) : e.last_lit && (U(e, !1), 0 === e.strm.avail_out) ? 1 : 2;
                };
                function q(e, t, n, r, i) {
                    this.good_length = e, this.max_lazy = t, this.nice_length = n, this.max_chain = r, this.func = i;
                }
                const J = [
                    new q(0, 0, 0, 0, (e, t)=>{
                        let n = 65535;
                        for(n > e.pending_buf_size - 5 && (n = e.pending_buf_size - 5);;){
                            if (e.lookahead <= 1) {
                                if (H(e), 0 === e.lookahead && t === f) return 1;
                                if (0 === e.lookahead) break;
                            }
                            e.strstart += e.lookahead, e.lookahead = 0;
                            const r = e.block_start + n;
                            if ((0 === e.strstart || e.strstart >= r) && (e.lookahead = e.strstart - r, e.strstart = r, U(e, !1), 0 === e.strm.avail_out)) return 1;
                            if (e.strstart - e.block_start >= e.w_size - R && (U(e, !1), 0 === e.strm.avail_out)) return 1;
                        }
                        return e.insert = 0, t === h ? (U(e, !0), 0 === e.strm.avail_out ? 3 : 4) : (e.strstart > e.block_start && (U(e, !1), e.strm.avail_out), 1);
                    }),
                    new q(4, 4, 8, 4, G),
                    new q(4, 5, 16, 8, G),
                    new q(4, 6, 32, 32, G),
                    new q(4, 4, 16, 16, V),
                    new q(8, 16, 32, 32, V),
                    new q(8, 16, 128, 128, V),
                    new q(8, 32, 128, 256, V),
                    new q(32, 128, 258, 1024, V),
                    new q(32, 258, 258, 4096, V)
                ];
                function Y() {
                    this.strm = null, this.status = 0, this.pending_buf = null, this.pending_buf_size = 0, this.pending_out = 0, this.pending = 0, this.wrap = 0, this.gzhead = null, this.gzindex = 0, this.method = T, this.last_flush = -1, this.w_size = 0, this.w_bits = 0, this.w_mask = 0, this.window = null, this.window_size = 0, this.prev = null, this.head = null, this.ins_h = 0, this.hash_size = 0, this.hash_bits = 0, this.hash_mask = 0, this.hash_shift = 0, this.block_start = 0, this.match_length = 0, this.prev_match = 0, this.match_available = 0, this.strstart = 0, this.match_start = 0, this.lookahead = 0, this.prev_length = 0, this.max_chain_length = 0, this.max_lazy_match = 0, this.level = 0, this.strategy = 0, this.good_match = 0, this.nice_match = 0, this.dyn_ltree = new Uint16Array(1146), this.dyn_dtree = new Uint16Array(122), this.bl_tree = new Uint16Array(78), j(this.dyn_ltree), j(this.dyn_dtree), j(this.bl_tree), this.l_desc = null, this.d_desc = null, this.bl_desc = null, this.bl_count = new Uint16Array(16), this.heap = new Uint16Array(573), j(this.heap), this.heap_len = 0, this.heap_max = 0, this.depth = new Uint16Array(573), j(this.depth), this.l_buf = 0, this.lit_bufsize = 0, this.last_lit = 0, this.d_buf = 0, this.opt_len = 0, this.static_len = 0, this.matches = 0, this.insert = 0, this.bi_buf = 0, this.bi_valid = 0;
                }
                const Q = (e)=>{
                    if (!e || !e.state) return P(e, b);
                    e.total_in = e.total_out = 0, e.data_type = x;
                    const t = e.state;
                    return t.pending = 0, t.pending_out = 0, t.wrap < 0 && (t.wrap = -t.wrap), t.status = t.wrap ? 42 : N, e.adler = 2 === t.wrap ? 0 : 1, t.last_flush = f, r(t), m;
                }, X = (e)=>{
                    const t = Q(e);
                    var n;
                    return t === m && ((n = e.state).window_size = 2 * n.w_size, j(n.head), n.max_lazy_match = J[n.level].max_lazy, n.good_match = J[n.level].good_length, n.nice_match = J[n.level].nice_length, n.max_chain_length = J[n.level].max_chain, n.strstart = 0, n.block_start = 0, n.lookahead = 0, n.insert = 0, n.match_length = n.prev_length = 2, n.match_available = 0, n.ins_h = 0), t;
                }, K = (e, t, n, r, i, o)=>{
                    if (!e) return b;
                    let s = 1;
                    if (t === v && (t = 6), r < 0 ? (s = 0, r = -r) : r > 15 && (s = 2, r -= 16), i < 1 || i > 9 || n !== T || r < 8 || r > 15 || t < 0 || t > 9 || o < 0 || o > A) return P(e, b);
                    8 === r && (r = 9);
                    const a = new Y;
                    return e.state = a, a.strm = e, a.wrap = s, a.gzhead = null, a.w_bits = r, a.w_size = 1 << a.w_bits, a.w_mask = a.w_size - 1, a.hash_bits = i + 7, a.hash_size = 1 << a.hash_bits, a.hash_mask = a.hash_size - 1, a.hash_shift = ~~((a.hash_bits + 3 - 1) / 3), a.window = new Uint8Array(2 * a.w_size), a.head = new Uint16Array(a.hash_size), a.prev = new Uint16Array(a.w_size), a.lit_bufsize = 1 << i + 6, a.pending_buf_size = 4 * a.lit_bufsize, a.pending_buf = new Uint8Array(a.pending_buf_size), a.d_buf = 1 * a.lit_bufsize, a.l_buf = 3 * a.lit_bufsize, a.level = t, a.strategy = o, a.method = n, X(e);
                };
                t.exports.deflateInit = (e, t)=>K(e, t, T, 15, 8, S), t.exports.deflateInit2 = K, t.exports.deflateReset = X, t.exports.deflateResetKeep = Q, t.exports.deflateSetHeader = (e, t)=>e && e.state ? 2 !== e.state.wrap ? b : (e.state.gzhead = t, m) : b, t.exports.deflate = (e, t)=>{
                    let n, r;
                    if (!e || !e.state || t > g || t < 0) return e ? P(e, b) : b;
                    const o = e.state;
                    if (!e.output || !e.input && 0 !== e.avail_in || o.status === D && t !== h) return P(e, 0 === e.avail_out ? _ : b);
                    o.strm = e;
                    const l = o.last_flush;
                    if (o.last_flush = t, 42 === o.status) {
                        if (2 === o.wrap) e.adler = 0, z(o, 31), z(o, 139), z(o, 8), o.gzhead ? (z(o, (o.gzhead.text ? 1 : 0) + (o.gzhead.hcrc ? 2 : 0) + (o.gzhead.extra ? 4 : 0) + (o.gzhead.name ? 8 : 0) + (o.gzhead.comment ? 16 : 0)), z(o, 255 & o.gzhead.time), z(o, o.gzhead.time >> 8 & 255), z(o, o.gzhead.time >> 16 & 255), z(o, o.gzhead.time >> 24 & 255), z(o, 9 === o.level ? 2 : o.strategy >= E || o.level < 2 ? 4 : 0), z(o, 255 & o.gzhead.os), o.gzhead.extra && o.gzhead.extra.length && (z(o, 255 & o.gzhead.extra.length), z(o, o.gzhead.extra.length >> 8 & 255)), o.gzhead.hcrc && (e.adler = c(e.adler, o.pending_buf, o.pending, 0)), o.gzindex = 0, o.status = 69) : (z(o, 0), z(o, 0), z(o, 0), z(o, 0), z(o, 0), z(o, 9 === o.level ? 2 : o.strategy >= E || o.level < 2 ? 4 : 0), z(o, 3), o.status = N);
                        else {
                            let t = T + (o.w_bits - 8 << 4) << 8, n = -1;
                            n = o.strategy >= E || o.level < 2 ? 0 : o.level < 6 ? 1 : 6 === o.level ? 2 : 3, t |= n << 6, 0 !== o.strstart && (t |= 32), t += 31 - t % 31, o.status = N, W(o, t), 0 !== o.strstart && (W(o, e.adler >>> 16), W(o, 65535 & e.adler)), e.adler = 1;
                        }
                    }
                    if (69 === o.status) {
                        if (o.gzhead.extra) {
                            for(n = o.pending; o.gzindex < (65535 & o.gzhead.extra.length) && (o.pending !== o.pending_buf_size || (o.gzhead.hcrc && o.pending > n && (e.adler = c(e.adler, o.pending_buf, o.pending - n, n)), L(e), n = o.pending, o.pending !== o.pending_buf_size));)z(o, 255 & o.gzhead.extra[o.gzindex]), o.gzindex++;
                            o.gzhead.hcrc && o.pending > n && (e.adler = c(e.adler, o.pending_buf, o.pending - n, n)), o.gzindex === o.gzhead.extra.length && (o.gzindex = 0, o.status = 73);
                        } else o.status = 73;
                    }
                    if (73 === o.status) {
                        if (o.gzhead.name) {
                            n = o.pending;
                            do {
                                if (o.pending === o.pending_buf_size && (o.gzhead.hcrc && o.pending > n && (e.adler = c(e.adler, o.pending_buf, o.pending - n, n)), L(e), n = o.pending, o.pending === o.pending_buf_size)) {
                                    r = 1;
                                    break;
                                }
                                r = o.gzindex < o.gzhead.name.length ? 255 & o.gzhead.name.charCodeAt(o.gzindex++) : 0, z(o, r);
                            }while (0 !== r);
                            o.gzhead.hcrc && o.pending > n && (e.adler = c(e.adler, o.pending_buf, o.pending - n, n)), 0 === r && (o.gzindex = 0, o.status = 91);
                        } else o.status = 91;
                    }
                    if (91 === o.status) {
                        if (o.gzhead.comment) {
                            n = o.pending;
                            do {
                                if (o.pending === o.pending_buf_size && (o.gzhead.hcrc && o.pending > n && (e.adler = c(e.adler, o.pending_buf, o.pending - n, n)), L(e), n = o.pending, o.pending === o.pending_buf_size)) {
                                    r = 1;
                                    break;
                                }
                                r = o.gzindex < o.gzhead.comment.length ? 255 & o.gzhead.comment.charCodeAt(o.gzindex++) : 0, z(o, r);
                            }while (0 !== r);
                            o.gzhead.hcrc && o.pending > n && (e.adler = c(e.adler, o.pending_buf, o.pending - n, n)), 0 === r && (o.status = C);
                        } else o.status = C;
                    }
                    if (o.status === C && (o.gzhead.hcrc ? (o.pending + 2 > o.pending_buf_size && L(e), o.pending + 2 <= o.pending_buf_size && (z(o, 255 & e.adler), z(o, e.adler >> 8 & 255), e.adler = 0, o.status = N)) : o.status = N), 0 !== o.pending) {
                        if (L(e), 0 === e.avail_out) return o.last_flush = -1, m;
                    } else if (0 === e.avail_in && B(t) <= B(l) && t !== h) return P(e, _);
                    if (o.status === D && 0 !== e.avail_in) return P(e, _);
                    if (0 !== e.avail_in || 0 !== o.lookahead || t !== f && o.status !== D) {
                        let n = o.strategy === E ? ((e, t)=>{
                            let n;
                            for(;;){
                                if (0 === e.lookahead && (H(e), 0 === e.lookahead)) {
                                    if (t === f) return 1;
                                    break;
                                }
                                if (e.match_length = 0, n = s(e, 0, e.window[e.strstart]), e.lookahead--, e.strstart++, n && (U(e, !1), 0 === e.strm.avail_out)) return 1;
                            }
                            return e.insert = 0, t === h ? (U(e, !0), 0 === e.strm.avail_out ? 3 : 4) : e.last_lit && (U(e, !1), 0 === e.strm.avail_out) ? 1 : 2;
                        })(o, t) : o.strategy === k ? ((e, t)=>{
                            let n, r, i, o;
                            const a = e.window;
                            for(;;){
                                if (e.lookahead <= I) {
                                    if (H(e), e.lookahead <= I && t === f) return 1;
                                    if (0 === e.lookahead) break;
                                }
                                if (e.match_length = 0, e.lookahead >= 3 && e.strstart > 0 && (i = e.strstart - 1, r = a[i], r === a[++i] && r === a[++i] && r === a[++i])) {
                                    o = e.strstart + I;
                                    do ;
                                    while (r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && r === a[++i] && i < o);
                                    e.match_length = I - (o - i), e.match_length > e.lookahead && (e.match_length = e.lookahead);
                                }
                                if (e.match_length >= 3 ? (n = s(e, 1, e.match_length - 3), e.lookahead -= e.match_length, e.strstart += e.match_length, e.match_length = 0) : (n = s(e, 0, e.window[e.strstart]), e.lookahead--, e.strstart++), n && (U(e, !1), 0 === e.strm.avail_out)) return 1;
                            }
                            return e.insert = 0, t === h ? (U(e, !0), 0 === e.strm.avail_out ? 3 : 4) : e.last_lit && (U(e, !1), 0 === e.strm.avail_out) ? 1 : 2;
                        })(o, t) : J[o.level].func(o, t);
                        if (3 !== n && 4 !== n || (o.status = D), 1 === n || 3 === n) return 0 === e.avail_out && (o.last_flush = -1), m;
                        if (2 === n && (t === d ? a(o) : t !== g && (i(o, 0, 0, !1), t === p && (j(o.head), 0 === o.lookahead && (o.strstart = 0, o.block_start = 0, o.insert = 0))), L(e), 0 === e.avail_out)) return o.last_flush = -1, m;
                    }
                    return t !== h ? m : o.wrap <= 0 ? y : (2 === o.wrap ? (z(o, 255 & e.adler), z(o, e.adler >> 8 & 255), z(o, e.adler >> 16 & 255), z(o, e.adler >> 24 & 255), z(o, 255 & e.total_in), z(o, e.total_in >> 8 & 255), z(o, e.total_in >> 16 & 255), z(o, e.total_in >> 24 & 255)) : (W(o, e.adler >>> 16), W(o, 65535 & e.adler)), L(e), o.wrap > 0 && (o.wrap = -o.wrap), 0 !== o.pending ? m : y);
                }, t.exports.deflateEnd = (e)=>{
                    if (!e || !e.state) return b;
                    const t = e.state.status;
                    return 42 !== t && 69 !== t && 73 !== t && 91 !== t && t !== C && t !== N && t !== D ? P(e, b) : (e.state = null, t === N ? P(e, w) : m);
                }, t.exports.deflateSetDictionary = (e, t)=>{
                    let n = t.length;
                    if (!e || !e.state) return b;
                    const r = e.state, i = r.wrap;
                    if (2 === i || 1 === i && 42 !== r.status || r.lookahead) return b;
                    if (1 === i && (e.adler = l(e.adler, t, n, 0)), r.wrap = 0, n >= r.w_size) {
                        0 === i && (j(r.head), r.strstart = 0, r.block_start = 0, r.insert = 0);
                        let e = new Uint8Array(r.w_size);
                        e.set(t.subarray(n - r.w_size, n), 0), t = e, n = r.w_size;
                    }
                    const o = e.avail_in, s = e.next_in, a = e.input;
                    for(e.avail_in = n, e.next_in = 0, e.input = t, H(r); r.lookahead >= 3;){
                        let e = r.strstart, t = r.lookahead - 2;
                        do r.ins_h = M(r, r.ins_h, r.window[e + 3 - 1]), r.prev[e & r.w_mask] = r.head[r.ins_h], r.head[r.ins_h] = e, e++;
                        while (--t);
                        r.strstart = e, r.lookahead = 2, H(r);
                    }
                    return r.strstart += r.lookahead, r.block_start = r.strstart, r.insert = r.lookahead, r.lookahead = 0, r.match_length = r.prev_length = 2, r.match_available = 0, e.next_in = s, e.input = a, e.avail_in = o, r.wrap = i, m;
                }, t.exports.deflateInfo = "pako deflate (from Nodeca project)";
            },
            {
                "./adler32": 32,
                "./constants": 33,
                "./crc32": 34,
                "./messages": 40,
                "./trees": 41
            }
        ],
        36: [
            function(e, t, n) {
                "use strict";
                t.exports = function() {
                    this.text = 0, this.time = 0, this.xflags = 0, this.os = 0, this.extra = null, this.extra_len = 0, this.name = "", this.comment = "", this.hcrc = 0, this.done = !1;
                };
            },
            {}
        ],
        37: [
            function(e, t, n) {
                "use strict";
                t.exports = function(e, t) {
                    let n, r, i, o, s, a, l, c, u, f, d, p, h, g, m, y, b, w, _, v, O, E, k, A;
                    const S = e.state;
                    n = e.next_in, k = e.input, r = n + (e.avail_in - 5), i = e.next_out, A = e.output, o = i - (t - e.avail_out), s = i + (e.avail_out - 257), a = S.dmax, l = S.wsize, c = S.whave, u = S.wnext, f = S.window, d = S.hold, p = S.bits, h = S.lencode, g = S.distcode, m = (1 << S.lenbits) - 1, y = (1 << S.distbits) - 1;
                    e: do {
                        p < 15 && (d += k[n++] << p, p += 8, d += k[n++] << p, p += 8), b = h[d & m];
                        t: for(;;){
                            if (w = b >>> 24, d >>>= w, p -= w, w = b >>> 16 & 255, 0 === w) A[i++] = 65535 & b;
                            else {
                                if (!(16 & w)) {
                                    if (0 == (64 & w)) {
                                        b = h[(65535 & b) + (d & (1 << w) - 1)];
                                        continue t;
                                    }
                                    if (32 & w) {
                                        S.mode = 12;
                                        break e;
                                    }
                                    e.msg = "invalid literal/length code", S.mode = 30;
                                    break e;
                                }
                                _ = 65535 & b, w &= 15, w && (p < w && (d += k[n++] << p, p += 8), _ += d & (1 << w) - 1, d >>>= w, p -= w), p < 15 && (d += k[n++] << p, p += 8, d += k[n++] << p, p += 8), b = g[d & y];
                                n: for(;;){
                                    if (w = b >>> 24, d >>>= w, p -= w, w = b >>> 16 & 255, !(16 & w)) {
                                        if (0 == (64 & w)) {
                                            b = g[(65535 & b) + (d & (1 << w) - 1)];
                                            continue n;
                                        }
                                        e.msg = "invalid distance code", S.mode = 30;
                                        break e;
                                    }
                                    if (v = 65535 & b, w &= 15, p < w && (d += k[n++] << p, p += 8, p < w && (d += k[n++] << p, p += 8)), v += d & (1 << w) - 1, v > a) {
                                        e.msg = "invalid distance too far back", S.mode = 30;
                                        break e;
                                    }
                                    if (d >>>= w, p -= w, w = i - o, v > w) {
                                        if (w = v - w, w > c && S.sane) {
                                            e.msg = "invalid distance too far back", S.mode = 30;
                                            break e;
                                        }
                                        if (O = 0, E = f, 0 === u) {
                                            if (O += l - w, w < _) {
                                                _ -= w;
                                                do A[i++] = f[O++];
                                                while (--w);
                                                O = i - v, E = A;
                                            }
                                        } else if (u < w) {
                                            if (O += l + u - w, w -= u, w < _) {
                                                _ -= w;
                                                do A[i++] = f[O++];
                                                while (--w);
                                                if (O = 0, u < _) {
                                                    w = u, _ -= w;
                                                    do A[i++] = f[O++];
                                                    while (--w);
                                                    O = i - v, E = A;
                                                }
                                            }
                                        } else if (O += u - w, w < _) {
                                            _ -= w;
                                            do A[i++] = f[O++];
                                            while (--w);
                                            O = i - v, E = A;
                                        }
                                        for(; _ > 2;)A[i++] = E[O++], A[i++] = E[O++], A[i++] = E[O++], _ -= 3;
                                        _ && (A[i++] = E[O++], _ > 1 && (A[i++] = E[O++]));
                                    } else {
                                        O = i - v;
                                        do A[i++] = A[O++], A[i++] = A[O++], A[i++] = A[O++], _ -= 3;
                                        while (_ > 2);
                                        _ && (A[i++] = A[O++], _ > 1 && (A[i++] = A[O++]));
                                    }
                                    break;
                                }
                            }
                            break;
                        }
                    }while (n < r && i < s);
                    _ = p >> 3, n -= _, p -= _ << 3, d &= (1 << p) - 1, e.next_in = n, e.next_out = i, e.avail_in = n < r ? r - n + 5 : 5 - (n - r), e.avail_out = i < s ? s - i + 257 : 257 - (i - s), S.hold = d, S.bits = p;
                };
            },
            {}
        ],
        38: [
            function(e, t, n) {
                "use strict";
                const r = e("./adler32"), i = e("./crc32"), o = e("./inffast"), s = e("./inftrees"), { Z_FINISH: a , Z_BLOCK: l , Z_TREES: c , Z_OK: u , Z_STREAM_END: f , Z_NEED_DICT: d , Z_STREAM_ERROR: p , Z_DATA_ERROR: h , Z_MEM_ERROR: g , Z_BUF_ERROR: m , Z_DEFLATED: y  } = e("./constants"), b = 12, w = 30, _ = (e)=>(e >>> 24 & 255) + (e >>> 8 & 65280) + ((65280 & e) << 8) + ((255 & e) << 24);
                function v() {
                    this.mode = 0, this.last = !1, this.wrap = 0, this.havedict = !1, this.flags = 0, this.dmax = 0, this.check = 0, this.total = 0, this.head = null, this.wbits = 0, this.wsize = 0, this.whave = 0, this.wnext = 0, this.window = null, this.hold = 0, this.bits = 0, this.length = 0, this.offset = 0, this.extra = 0, this.lencode = null, this.distcode = null, this.lenbits = 0, this.distbits = 0, this.ncode = 0, this.nlen = 0, this.ndist = 0, this.have = 0, this.next = null, this.lens = new Uint16Array(320), this.work = new Uint16Array(288), this.lendyn = null, this.distdyn = null, this.sane = 0, this.back = 0, this.was = 0;
                }
                const O = (e)=>{
                    if (!e || !e.state) return p;
                    const t = e.state;
                    return e.total_in = e.total_out = t.total = 0, e.msg = "", t.wrap && (e.adler = 1 & t.wrap), t.mode = 1, t.last = 0, t.havedict = 0, t.dmax = 32768, t.head = null, t.hold = 0, t.bits = 0, t.lencode = t.lendyn = new Int32Array(852), t.distcode = t.distdyn = new Int32Array(592), t.sane = 1, t.back = -1, u;
                }, E = (e)=>{
                    if (!e || !e.state) return p;
                    const t = e.state;
                    return t.wsize = 0, t.whave = 0, t.wnext = 0, O(e);
                }, k = (e, t)=>{
                    let n;
                    if (!e || !e.state) return p;
                    const r = e.state;
                    return t < 0 ? (n = 0, t = -t) : (n = 1 + (t >> 4), t < 48 && (t &= 15)), t && (t < 8 || t > 15) ? p : (null !== r.window && r.wbits !== t && (r.window = null), r.wrap = n, r.wbits = t, E(e));
                }, A = (e, t)=>{
                    if (!e) return p;
                    const n = new v;
                    e.state = n, n.window = null;
                    const r = k(e, t);
                    return r !== u && (e.state = null), r;
                };
                let S, x, T = !0;
                const I = (e)=>{
                    if (T) {
                        S = new Int32Array(512), x = new Int32Array(32);
                        let t = 0;
                        for(; t < 144;)e.lens[t++] = 8;
                        for(; t < 256;)e.lens[t++] = 9;
                        for(; t < 280;)e.lens[t++] = 7;
                        for(; t < 288;)e.lens[t++] = 8;
                        for(s(1, e.lens, 0, 288, S, 0, e.work, {
                            bits: 9
                        }), t = 0; t < 32;)e.lens[t++] = 5;
                        s(2, e.lens, 0, 32, x, 0, e.work, {
                            bits: 5
                        }), T = !1;
                    }
                    e.lencode = S, e.lenbits = 9, e.distcode = x, e.distbits = 5;
                }, R = (e, t, n, r)=>{
                    let i;
                    const o = e.state;
                    return null === o.window && (o.wsize = 1 << o.wbits, o.wnext = 0, o.whave = 0, o.window = new Uint8Array(o.wsize)), r >= o.wsize ? (o.window.set(t.subarray(n - o.wsize, n), 0), o.wnext = 0, o.whave = o.wsize) : (i = o.wsize - o.wnext, i > r && (i = r), o.window.set(t.subarray(n - r, n - r + i), o.wnext), (r -= i) ? (o.window.set(t.subarray(n - r, n), 0), o.wnext = r, o.whave = o.wsize) : (o.wnext += i, o.wnext === o.wsize && (o.wnext = 0), o.whave < o.wsize && (o.whave += i))), 0;
                };
                t.exports.inflateReset = E, t.exports.inflateReset2 = k, t.exports.inflateResetKeep = O, t.exports.inflateInit = (e)=>A(e, 15), t.exports.inflateInit2 = A, t.exports.inflate = (e, t)=>{
                    let n, v, O, E, k, A, S, x, T, C, N, D, P, B, j, M, L, U, z, W, Z, F, H = 0;
                    const G = new Uint8Array(4);
                    let V, q;
                    const J = new Uint8Array([
                        16,
                        17,
                        18,
                        0,
                        8,
                        7,
                        9,
                        6,
                        10,
                        5,
                        11,
                        4,
                        12,
                        3,
                        13,
                        2,
                        14,
                        1,
                        15
                    ]);
                    if (!e || !e.state || !e.output || !e.input && 0 !== e.avail_in) return p;
                    n = e.state, n.mode === b && (n.mode = 13), k = e.next_out, O = e.output, S = e.avail_out, E = e.next_in, v = e.input, A = e.avail_in, x = n.hold, T = n.bits, C = A, N = S, F = u;
                    e: for(;;)switch(n.mode){
                        case 1:
                            if (0 === n.wrap) {
                                n.mode = 13;
                                break;
                            }
                            for(; T < 16;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            if (2 & n.wrap && 35615 === x) {
                                n.check = 0, G[0] = 255 & x, G[1] = x >>> 8 & 255, n.check = i(n.check, G, 2, 0), x = 0, T = 0, n.mode = 2;
                                break;
                            }
                            if (n.flags = 0, n.head && (n.head.done = !1), !(1 & n.wrap) || (((255 & x) << 8) + (x >> 8)) % 31) {
                                e.msg = "incorrect header check", n.mode = w;
                                break;
                            }
                            if ((15 & x) !== y) {
                                e.msg = "unknown compression method", n.mode = w;
                                break;
                            }
                            if (x >>>= 4, T -= 4, Z = 8 + (15 & x), 0 === n.wbits) n.wbits = Z;
                            else if (Z > n.wbits) {
                                e.msg = "invalid window size", n.mode = w;
                                break;
                            }
                            n.dmax = 1 << n.wbits, e.adler = n.check = 1, n.mode = 512 & x ? 10 : b, x = 0, T = 0;
                            break;
                        case 2:
                            for(; T < 16;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            if (n.flags = x, (255 & n.flags) !== y) {
                                e.msg = "unknown compression method", n.mode = w;
                                break;
                            }
                            if (57344 & n.flags) {
                                e.msg = "unknown header flags set", n.mode = w;
                                break;
                            }
                            n.head && (n.head.text = x >> 8 & 1), 512 & n.flags && (G[0] = 255 & x, G[1] = x >>> 8 & 255, n.check = i(n.check, G, 2, 0)), x = 0, T = 0, n.mode = 3;
                        case 3:
                            for(; T < 32;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            n.head && (n.head.time = x), 512 & n.flags && (G[0] = 255 & x, G[1] = x >>> 8 & 255, G[2] = x >>> 16 & 255, G[3] = x >>> 24 & 255, n.check = i(n.check, G, 4, 0)), x = 0, T = 0, n.mode = 4;
                        case 4:
                            for(; T < 16;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            n.head && (n.head.xflags = 255 & x, n.head.os = x >> 8), 512 & n.flags && (G[0] = 255 & x, G[1] = x >>> 8 & 255, n.check = i(n.check, G, 2, 0)), x = 0, T = 0, n.mode = 5;
                        case 5:
                            if (1024 & n.flags) {
                                for(; T < 16;){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                n.length = x, n.head && (n.head.extra_len = x), 512 & n.flags && (G[0] = 255 & x, G[1] = x >>> 8 & 255, n.check = i(n.check, G, 2, 0)), x = 0, T = 0;
                            } else n.head && (n.head.extra = null);
                            n.mode = 6;
                        case 6:
                            if (1024 & n.flags && (D = n.length, D > A && (D = A), D && (n.head && (Z = n.head.extra_len - n.length, n.head.extra || (n.head.extra = new Uint8Array(n.head.extra_len)), n.head.extra.set(v.subarray(E, E + D), Z)), 512 & n.flags && (n.check = i(n.check, v, D, E)), A -= D, E += D, n.length -= D), n.length)) break e;
                            n.length = 0, n.mode = 7;
                        case 7:
                            if (2048 & n.flags) {
                                if (0 === A) break e;
                                D = 0;
                                do Z = v[E + D++], n.head && Z && n.length < 65536 && (n.head.name += String.fromCharCode(Z));
                                while (Z && D < A);
                                if (512 & n.flags && (n.check = i(n.check, v, D, E)), A -= D, E += D, Z) break e;
                            } else n.head && (n.head.name = null);
                            n.length = 0, n.mode = 8;
                        case 8:
                            if (4096 & n.flags) {
                                if (0 === A) break e;
                                D = 0;
                                do Z = v[E + D++], n.head && Z && n.length < 65536 && (n.head.comment += String.fromCharCode(Z));
                                while (Z && D < A);
                                if (512 & n.flags && (n.check = i(n.check, v, D, E)), A -= D, E += D, Z) break e;
                            } else n.head && (n.head.comment = null);
                            n.mode = 9;
                        case 9:
                            if (512 & n.flags) {
                                for(; T < 16;){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                if (x !== (65535 & n.check)) {
                                    e.msg = "header crc mismatch", n.mode = w;
                                    break;
                                }
                                x = 0, T = 0;
                            }
                            n.head && (n.head.hcrc = n.flags >> 9 & 1, n.head.done = !0), e.adler = n.check = 0, n.mode = b;
                            break;
                        case 10:
                            for(; T < 32;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            e.adler = n.check = _(x), x = 0, T = 0, n.mode = 11;
                        case 11:
                            if (0 === n.havedict) return e.next_out = k, e.avail_out = S, e.next_in = E, e.avail_in = A, n.hold = x, n.bits = T, d;
                            e.adler = n.check = 1, n.mode = b;
                        case b:
                            if (t === l || t === c) break e;
                        case 13:
                            if (n.last) {
                                x >>>= 7 & T, T -= 7 & T, n.mode = 27;
                                break;
                            }
                            for(; T < 3;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            switch(n.last = 1 & x, x >>>= 1, T -= 1, 3 & x){
                                case 0:
                                    n.mode = 14;
                                    break;
                                case 1:
                                    if (I(n), n.mode = 20, t === c) {
                                        x >>>= 2, T -= 2;
                                        break e;
                                    }
                                    break;
                                case 2:
                                    n.mode = 17;
                                    break;
                                case 3:
                                    e.msg = "invalid block type", n.mode = w;
                            }
                            x >>>= 2, T -= 2;
                            break;
                        case 14:
                            for(x >>>= 7 & T, T -= 7 & T; T < 32;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            if ((65535 & x) != (x >>> 16 ^ 65535)) {
                                e.msg = "invalid stored block lengths", n.mode = w;
                                break;
                            }
                            if (n.length = 65535 & x, x = 0, T = 0, n.mode = 15, t === c) break e;
                        case 15:
                            n.mode = 16;
                        case 16:
                            if (D = n.length, D) {
                                if (D > A && (D = A), D > S && (D = S), 0 === D) break e;
                                O.set(v.subarray(E, E + D), k), A -= D, E += D, S -= D, k += D, n.length -= D;
                                break;
                            }
                            n.mode = b;
                            break;
                        case 17:
                            for(; T < 14;){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            if (n.nlen = 257 + (31 & x), x >>>= 5, T -= 5, n.ndist = 1 + (31 & x), x >>>= 5, T -= 5, n.ncode = 4 + (15 & x), x >>>= 4, T -= 4, n.nlen > 286 || n.ndist > 30) {
                                e.msg = "too many length or distance symbols", n.mode = w;
                                break;
                            }
                            n.have = 0, n.mode = 18;
                        case 18:
                            for(; n.have < n.ncode;){
                                for(; T < 3;){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                n.lens[J[n.have++]] = 7 & x, x >>>= 3, T -= 3;
                            }
                            for(; n.have < 19;)n.lens[J[n.have++]] = 0;
                            if (n.lencode = n.lendyn, n.lenbits = 7, V = {
                                bits: n.lenbits
                            }, F = s(0, n.lens, 0, 19, n.lencode, 0, n.work, V), n.lenbits = V.bits, F) {
                                e.msg = "invalid code lengths set", n.mode = w;
                                break;
                            }
                            n.have = 0, n.mode = 19;
                        case 19:
                            for(; n.have < n.nlen + n.ndist;){
                                for(; H = n.lencode[x & (1 << n.lenbits) - 1], j = H >>> 24, M = H >>> 16 & 255, L = 65535 & H, !(j <= T);){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                if (L < 16) x >>>= j, T -= j, n.lens[n.have++] = L;
                                else {
                                    if (16 === L) {
                                        for(q = j + 2; T < q;){
                                            if (0 === A) break e;
                                            A--, x += v[E++] << T, T += 8;
                                        }
                                        if (x >>>= j, T -= j, 0 === n.have) {
                                            e.msg = "invalid bit length repeat", n.mode = w;
                                            break;
                                        }
                                        Z = n.lens[n.have - 1], D = 3 + (3 & x), x >>>= 2, T -= 2;
                                    } else if (17 === L) {
                                        for(q = j + 3; T < q;){
                                            if (0 === A) break e;
                                            A--, x += v[E++] << T, T += 8;
                                        }
                                        x >>>= j, T -= j, Z = 0, D = 3 + (7 & x), x >>>= 3, T -= 3;
                                    } else {
                                        for(q = j + 7; T < q;){
                                            if (0 === A) break e;
                                            A--, x += v[E++] << T, T += 8;
                                        }
                                        x >>>= j, T -= j, Z = 0, D = 11 + (127 & x), x >>>= 7, T -= 7;
                                    }
                                    if (n.have + D > n.nlen + n.ndist) {
                                        e.msg = "invalid bit length repeat", n.mode = w;
                                        break;
                                    }
                                    for(; D--;)n.lens[n.have++] = Z;
                                }
                            }
                            if (n.mode === w) break;
                            if (0 === n.lens[256]) {
                                e.msg = "invalid code -- missing end-of-block", n.mode = w;
                                break;
                            }
                            if (n.lenbits = 9, V = {
                                bits: n.lenbits
                            }, F = s(1, n.lens, 0, n.nlen, n.lencode, 0, n.work, V), n.lenbits = V.bits, F) {
                                e.msg = "invalid literal/lengths set", n.mode = w;
                                break;
                            }
                            if (n.distbits = 6, n.distcode = n.distdyn, V = {
                                bits: n.distbits
                            }, F = s(2, n.lens, n.nlen, n.ndist, n.distcode, 0, n.work, V), n.distbits = V.bits, F) {
                                e.msg = "invalid distances set", n.mode = w;
                                break;
                            }
                            if (n.mode = 20, t === c) break e;
                        case 20:
                            n.mode = 21;
                        case 21:
                            if (A >= 6 && S >= 258) {
                                e.next_out = k, e.avail_out = S, e.next_in = E, e.avail_in = A, n.hold = x, n.bits = T, o(e, N), k = e.next_out, O = e.output, S = e.avail_out, E = e.next_in, v = e.input, A = e.avail_in, x = n.hold, T = n.bits, n.mode === b && (n.back = -1);
                                break;
                            }
                            for(n.back = 0; H = n.lencode[x & (1 << n.lenbits) - 1], j = H >>> 24, M = H >>> 16 & 255, L = 65535 & H, !(j <= T);){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            if (M && 0 == (240 & M)) {
                                for(U = j, z = M, W = L; H = n.lencode[W + ((x & (1 << U + z) - 1) >> U)], j = H >>> 24, M = H >>> 16 & 255, L = 65535 & H, !(U + j <= T);){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                x >>>= U, T -= U, n.back += U;
                            }
                            if (x >>>= j, T -= j, n.back += j, n.length = L, 0 === M) {
                                n.mode = 26;
                                break;
                            }
                            if (32 & M) {
                                n.back = -1, n.mode = b;
                                break;
                            }
                            if (64 & M) {
                                e.msg = "invalid literal/length code", n.mode = w;
                                break;
                            }
                            n.extra = 15 & M, n.mode = 22;
                        case 22:
                            if (n.extra) {
                                for(q = n.extra; T < q;){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                n.length += x & (1 << n.extra) - 1, x >>>= n.extra, T -= n.extra, n.back += n.extra;
                            }
                            n.was = n.length, n.mode = 23;
                        case 23:
                            for(; H = n.distcode[x & (1 << n.distbits) - 1], j = H >>> 24, M = H >>> 16 & 255, L = 65535 & H, !(j <= T);){
                                if (0 === A) break e;
                                A--, x += v[E++] << T, T += 8;
                            }
                            if (0 == (240 & M)) {
                                for(U = j, z = M, W = L; H = n.distcode[W + ((x & (1 << U + z) - 1) >> U)], j = H >>> 24, M = H >>> 16 & 255, L = 65535 & H, !(U + j <= T);){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                x >>>= U, T -= U, n.back += U;
                            }
                            if (x >>>= j, T -= j, n.back += j, 64 & M) {
                                e.msg = "invalid distance code", n.mode = w;
                                break;
                            }
                            n.offset = L, n.extra = 15 & M, n.mode = 24;
                        case 24:
                            if (n.extra) {
                                for(q = n.extra; T < q;){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                n.offset += x & (1 << n.extra) - 1, x >>>= n.extra, T -= n.extra, n.back += n.extra;
                            }
                            if (n.offset > n.dmax) {
                                e.msg = "invalid distance too far back", n.mode = w;
                                break;
                            }
                            n.mode = 25;
                        case 25:
                            if (0 === S) break e;
                            if (D = N - S, n.offset > D) {
                                if (D = n.offset - D, D > n.whave && n.sane) {
                                    e.msg = "invalid distance too far back", n.mode = w;
                                    break;
                                }
                                D > n.wnext ? (D -= n.wnext, P = n.wsize - D) : P = n.wnext - D, D > n.length && (D = n.length), B = n.window;
                            } else B = O, P = k - n.offset, D = n.length;
                            D > S && (D = S), S -= D, n.length -= D;
                            do O[k++] = B[P++];
                            while (--D);
                            0 === n.length && (n.mode = 21);
                            break;
                        case 26:
                            if (0 === S) break e;
                            O[k++] = n.length, S--, n.mode = 21;
                            break;
                        case 27:
                            if (n.wrap) {
                                for(; T < 32;){
                                    if (0 === A) break e;
                                    A--, x |= v[E++] << T, T += 8;
                                }
                                if (N -= S, e.total_out += N, n.total += N, N && (e.adler = n.check = n.flags ? i(n.check, O, N, k - N) : r(n.check, O, N, k - N)), N = S, (n.flags ? x : _(x)) !== n.check) {
                                    e.msg = "incorrect data check", n.mode = w;
                                    break;
                                }
                                x = 0, T = 0;
                            }
                            n.mode = 28;
                        case 28:
                            if (n.wrap && n.flags) {
                                for(; T < 32;){
                                    if (0 === A) break e;
                                    A--, x += v[E++] << T, T += 8;
                                }
                                if (x !== (4294967295 & n.total)) {
                                    e.msg = "incorrect length check", n.mode = w;
                                    break;
                                }
                                x = 0, T = 0;
                            }
                            n.mode = 29;
                        case 29:
                            F = f;
                            break e;
                        case w:
                            F = h;
                            break e;
                        case 31:
                            return g;
                        default:
                            return p;
                    }
                    return e.next_out = k, e.avail_out = S, e.next_in = E, e.avail_in = A, n.hold = x, n.bits = T, (n.wsize || N !== e.avail_out && n.mode < w && (n.mode < 27 || t !== a)) && R(e, e.output, e.next_out, N - e.avail_out) ? (n.mode = 31, g) : (C -= e.avail_in, N -= e.avail_out, e.total_in += C, e.total_out += N, n.total += N, n.wrap && N && (e.adler = n.check = n.flags ? i(n.check, O, N, e.next_out - N) : r(n.check, O, N, e.next_out - N)), e.data_type = n.bits + (n.last ? 64 : 0) + (n.mode === b ? 128 : 0) + (20 === n.mode || 15 === n.mode ? 256 : 0), (0 === C && 0 === N || t === a) && F === u && (F = m), F);
                }, t.exports.inflateEnd = (e)=>{
                    if (!e || !e.state) return p;
                    let t = e.state;
                    return t.window && (t.window = null), e.state = null, u;
                }, t.exports.inflateGetHeader = (e, t)=>{
                    if (!e || !e.state) return p;
                    const n = e.state;
                    return 0 == (2 & n.wrap) ? p : (n.head = t, t.done = !1, u);
                }, t.exports.inflateSetDictionary = (e, t)=>{
                    const n = t.length;
                    let i, o, s;
                    return e && e.state ? (i = e.state, 0 !== i.wrap && 11 !== i.mode ? p : 11 === i.mode && (o = 1, o = r(o, t, n, 0), o !== i.check) ? h : (s = R(e, t, n, n), s ? (i.mode = 31, g) : (i.havedict = 1, u))) : p;
                }, t.exports.inflateInfo = "pako inflate (from Nodeca project)";
            },
            {
                "./adler32": 32,
                "./constants": 33,
                "./crc32": 34,
                "./inffast": 37,
                "./inftrees": 39
            }
        ],
        39: [
            function(e, t, n) {
                "use strict";
                const r = 15, i = new Uint16Array([
                    3,
                    4,
                    5,
                    6,
                    7,
                    8,
                    9,
                    10,
                    11,
                    13,
                    15,
                    17,
                    19,
                    23,
                    27,
                    31,
                    35,
                    43,
                    51,
                    59,
                    67,
                    83,
                    99,
                    115,
                    131,
                    163,
                    195,
                    227,
                    258,
                    0,
                    0
                ]), o = new Uint8Array([
                    16,
                    16,
                    16,
                    16,
                    16,
                    16,
                    16,
                    16,
                    17,
                    17,
                    17,
                    17,
                    18,
                    18,
                    18,
                    18,
                    19,
                    19,
                    19,
                    19,
                    20,
                    20,
                    20,
                    20,
                    21,
                    21,
                    21,
                    21,
                    16,
                    72,
                    78
                ]), s = new Uint16Array([
                    1,
                    2,
                    3,
                    4,
                    5,
                    7,
                    9,
                    13,
                    17,
                    25,
                    33,
                    49,
                    65,
                    97,
                    129,
                    193,
                    257,
                    385,
                    513,
                    769,
                    1025,
                    1537,
                    2049,
                    3073,
                    4097,
                    6145,
                    8193,
                    12289,
                    16385,
                    24577,
                    0,
                    0
                ]), a = new Uint8Array([
                    16,
                    16,
                    16,
                    16,
                    17,
                    17,
                    18,
                    18,
                    19,
                    19,
                    20,
                    20,
                    21,
                    21,
                    22,
                    22,
                    23,
                    23,
                    24,
                    24,
                    25,
                    25,
                    26,
                    26,
                    27,
                    27,
                    28,
                    28,
                    29,
                    29,
                    64,
                    64
                ]);
                t.exports = (e, t, n, l, c, u, f, d)=>{
                    const p = d.bits;
                    let h, g, m, y, b, w, _ = 0, v = 0, O = 0, E = 0, k = 0, A = 0, S = 0, x = 0, T = 0, I = 0, R = null, C = 0;
                    const N = new Uint16Array(16), D = new Uint16Array(16);
                    let P, B, j, M = null, L = 0;
                    for(_ = 0; _ <= r; _++)N[_] = 0;
                    for(v = 0; v < l; v++)N[t[n + v]]++;
                    for(k = p, E = r; E >= 1 && 0 === N[E]; E--);
                    if (k > E && (k = E), 0 === E) return c[u++] = 20971520, c[u++] = 20971520, d.bits = 1, 0;
                    for(O = 1; O < E && 0 === N[O]; O++);
                    for(k < O && (k = O), x = 1, _ = 1; _ <= r; _++)if (x <<= 1, x -= N[_], x < 0) return -1;
                    if (x > 0 && (0 === e || 1 !== E)) return -1;
                    for(D[1] = 0, _ = 1; _ < r; _++)D[_ + 1] = D[_] + N[_];
                    for(v = 0; v < l; v++)0 !== t[n + v] && (f[D[t[n + v]]++] = v);
                    if (0 === e ? (R = M = f, w = 19) : 1 === e ? (R = i, C -= 257, M = o, L -= 257, w = 256) : (R = s, M = a, w = -1), I = 0, v = 0, _ = O, b = u, A = k, S = 0, m = -1, T = 1 << k, y = T - 1, 1 === e && T > 852 || 2 === e && T > 592) return 1;
                    for(;;){
                        P = _ - S, f[v] < w ? (B = 0, j = f[v]) : f[v] > w ? (B = M[L + f[v]], j = R[C + f[v]]) : (B = 96, j = 0), h = 1 << _ - S, g = 1 << A, O = g;
                        do g -= h, c[b + (I >> S) + g] = P << 24 | B << 16 | j | 0;
                        while (0 !== g);
                        for(h = 1 << _ - 1; I & h;)h >>= 1;
                        if (0 !== h ? (I &= h - 1, I += h) : I = 0, v++, 0 == --N[_]) {
                            if (_ === E) break;
                            _ = t[n + f[v]];
                        }
                        if (_ > k && (I & y) !== m) {
                            for(0 === S && (S = k), b += O, A = _ - S, x = 1 << A; A + S < E && (x -= N[A + S], !(x <= 0));)A++, x <<= 1;
                            if (T += 1 << A, 1 === e && T > 852 || 2 === e && T > 592) return 1;
                            m = I & y, c[m] = k << 24 | A << 16 | b - u | 0;
                        }
                    }
                    return 0 !== I && (c[b + I] = _ - S << 24 | 4194304), d.bits = k, 0;
                };
            },
            {}
        ],
        40: [
            function(e, t, n) {
                "use strict";
                t.exports = {
                    2: "need dictionary",
                    1: "stream end",
                    0: "",
                    "-1": "file error",
                    "-2": "stream error",
                    "-3": "data error",
                    "-4": "insufficient memory",
                    "-5": "buffer error",
                    "-6": "incompatible version"
                };
            },
            {}
        ],
        41: [
            function(e, t, n) {
                "use strict";
                function r(e) {
                    let t = e.length;
                    for(; --t >= 0;)e[t] = 0;
                }
                const i = 256, o = 286, s = 30, a = 15, l = new Uint8Array([
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    1,
                    1,
                    1,
                    1,
                    2,
                    2,
                    2,
                    2,
                    3,
                    3,
                    3,
                    3,
                    4,
                    4,
                    4,
                    4,
                    5,
                    5,
                    5,
                    5,
                    0
                ]), c = new Uint8Array([
                    0,
                    0,
                    0,
                    0,
                    1,
                    1,
                    2,
                    2,
                    3,
                    3,
                    4,
                    4,
                    5,
                    5,
                    6,
                    6,
                    7,
                    7,
                    8,
                    8,
                    9,
                    9,
                    10,
                    10,
                    11,
                    11,
                    12,
                    12,
                    13,
                    13
                ]), u = new Uint8Array([
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    2,
                    3,
                    7
                ]), f = new Uint8Array([
                    16,
                    17,
                    18,
                    0,
                    8,
                    7,
                    9,
                    6,
                    10,
                    5,
                    11,
                    4,
                    12,
                    3,
                    13,
                    2,
                    14,
                    1,
                    15
                ]), d = new Array(576);
                r(d);
                const p = new Array(60);
                r(p);
                const h = new Array(512);
                r(h);
                const g = new Array(256);
                r(g);
                const m = new Array(29);
                r(m);
                const y = new Array(s);
                function b(e, t, n, r, i) {
                    this.static_tree = e, this.extra_bits = t, this.extra_base = n, this.elems = r, this.max_length = i, this.has_stree = e && e.length;
                }
                let w, _, v;
                function O(e, t) {
                    this.dyn_tree = e, this.max_code = 0, this.stat_desc = t;
                }
                r(y);
                const E = (e)=>e < 256 ? h[e] : h[256 + (e >>> 7)], k = (e, t)=>{
                    e.pending_buf[e.pending++] = 255 & t, e.pending_buf[e.pending++] = t >>> 8 & 255;
                }, A = (e, t, n)=>{
                    e.bi_valid > 16 - n ? (e.bi_buf |= t << e.bi_valid & 65535, k(e, e.bi_buf), e.bi_buf = t >> 16 - e.bi_valid, e.bi_valid += n - 16) : (e.bi_buf |= t << e.bi_valid & 65535, e.bi_valid += n);
                }, S = (e, t, n)=>{
                    A(e, n[2 * t], n[2 * t + 1]);
                }, x = (e, t)=>{
                    let n = 0;
                    do n |= 1 & e, e >>>= 1, n <<= 1;
                    while (--t > 0);
                    return n >>> 1;
                }, T = (e, t, n)=>{
                    const r = new Array(16);
                    let i, o, s = 0;
                    for(i = 1; i <= a; i++)r[i] = s = s + n[i - 1] << 1;
                    for(o = 0; o <= t; o++){
                        let t = e[2 * o + 1];
                        0 !== t && (e[2 * o] = x(r[t]++, t));
                    }
                }, I = (e)=>{
                    let t;
                    for(t = 0; t < o; t++)e.dyn_ltree[2 * t] = 0;
                    for(t = 0; t < s; t++)e.dyn_dtree[2 * t] = 0;
                    for(t = 0; t < 19; t++)e.bl_tree[2 * t] = 0;
                    e.dyn_ltree[512] = 1, e.opt_len = e.static_len = 0, e.last_lit = e.matches = 0;
                }, R = (e)=>{
                    e.bi_valid > 8 ? k(e, e.bi_buf) : e.bi_valid > 0 && (e.pending_buf[e.pending++] = e.bi_buf), e.bi_buf = 0, e.bi_valid = 0;
                }, C = (e, t, n, r)=>{
                    const i = 2 * t, o = 2 * n;
                    return e[i] < e[o] || e[i] === e[o] && r[t] <= r[n];
                }, N = (e, t, n)=>{
                    const r = e.heap[n];
                    let i = n << 1;
                    for(; i <= e.heap_len && (i < e.heap_len && C(t, e.heap[i + 1], e.heap[i], e.depth) && i++, !C(t, r, e.heap[i], e.depth));)e.heap[n] = e.heap[i], n = i, i <<= 1;
                    e.heap[n] = r;
                }, D = (e, t, n)=>{
                    let r, o, s, a, u = 0;
                    if (0 !== e.last_lit) do r = e.pending_buf[e.d_buf + 2 * u] << 8 | e.pending_buf[e.d_buf + 2 * u + 1], o = e.pending_buf[e.l_buf + u], u++, 0 === r ? S(e, o, t) : (s = g[o], S(e, s + i + 1, t), a = l[s], 0 !== a && (o -= m[s], A(e, o, a)), r--, s = E(r), S(e, s, n), a = c[s], 0 !== a && (r -= y[s], A(e, r, a)));
                    while (u < e.last_lit);
                    S(e, 256, t);
                }, P = (e, t)=>{
                    const n = t.dyn_tree, r = t.stat_desc.static_tree, i = t.stat_desc.has_stree, o = t.stat_desc.elems;
                    let s, l, c, u = -1;
                    for(e.heap_len = 0, e.heap_max = 573, s = 0; s < o; s++)0 !== n[2 * s] ? (e.heap[++e.heap_len] = u = s, e.depth[s] = 0) : n[2 * s + 1] = 0;
                    for(; e.heap_len < 2;)c = e.heap[++e.heap_len] = u < 2 ? ++u : 0, n[2 * c] = 1, e.depth[c] = 0, e.opt_len--, i && (e.static_len -= r[2 * c + 1]);
                    for(t.max_code = u, s = e.heap_len >> 1; s >= 1; s--)N(e, n, s);
                    c = o;
                    do s = e.heap[1], e.heap[1] = e.heap[e.heap_len--], N(e, n, 1), l = e.heap[1], e.heap[--e.heap_max] = s, e.heap[--e.heap_max] = l, n[2 * c] = n[2 * s] + n[2 * l], e.depth[c] = (e.depth[s] >= e.depth[l] ? e.depth[s] : e.depth[l]) + 1, n[2 * s + 1] = n[2 * l + 1] = c, e.heap[1] = c++, N(e, n, 1);
                    while (e.heap_len >= 2);
                    e.heap[--e.heap_max] = e.heap[1], ((e, t)=>{
                        const n = t.dyn_tree, r = t.max_code, i = t.stat_desc.static_tree, o = t.stat_desc.has_stree, s = t.stat_desc.extra_bits, l = t.stat_desc.extra_base, c = t.stat_desc.max_length;
                        let u, f, d, p, h, g, m = 0;
                        for(p = 0; p <= a; p++)e.bl_count[p] = 0;
                        for(n[2 * e.heap[e.heap_max] + 1] = 0, u = e.heap_max + 1; u < 573; u++)f = e.heap[u], p = n[2 * n[2 * f + 1] + 1] + 1, p > c && (p = c, m++), n[2 * f + 1] = p, f > r || (e.bl_count[p]++, h = 0, f >= l && (h = s[f - l]), g = n[2 * f], e.opt_len += g * (p + h), o && (e.static_len += g * (i[2 * f + 1] + h)));
                        if (0 !== m) {
                            do {
                                for(p = c - 1; 0 === e.bl_count[p];)p--;
                                e.bl_count[p]--, e.bl_count[p + 1] += 2, e.bl_count[c]--, m -= 2;
                            }while (m > 0);
                            for(p = c; 0 !== p; p--)for(f = e.bl_count[p]; 0 !== f;)d = e.heap[--u], d > r || (n[2 * d + 1] !== p && (e.opt_len += (p - n[2 * d + 1]) * n[2 * d], n[2 * d + 1] = p), f--);
                        }
                    })(e, t), T(n, u, e.bl_count);
                }, B = (e, t, n)=>{
                    let r, i, o = -1, s = t[1], a = 0, l = 7, c = 4;
                    for(0 === s && (l = 138, c = 3), t[2 * (n + 1) + 1] = 65535, r = 0; r <= n; r++)i = s, s = t[2 * (r + 1) + 1], ++a < l && i === s || (a < c ? e.bl_tree[2 * i] += a : 0 !== i ? (i !== o && e.bl_tree[2 * i]++, e.bl_tree[32]++) : a <= 10 ? e.bl_tree[34]++ : e.bl_tree[36]++, a = 0, o = i, 0 === s ? (l = 138, c = 3) : i === s ? (l = 6, c = 3) : (l = 7, c = 4));
                }, j = (e, t, n)=>{
                    let r, i, o = -1, s = t[1], a = 0, l = 7, c = 4;
                    for(0 === s && (l = 138, c = 3), r = 0; r <= n; r++)if (i = s, s = t[2 * (r + 1) + 1], !(++a < l && i === s)) {
                        if (a < c) do S(e, i, e.bl_tree);
                        while (0 != --a);
                        else 0 !== i ? (i !== o && (S(e, i, e.bl_tree), a--), S(e, 16, e.bl_tree), A(e, a - 3, 2)) : a <= 10 ? (S(e, 17, e.bl_tree), A(e, a - 3, 3)) : (S(e, 18, e.bl_tree), A(e, a - 11, 7));
                        a = 0, o = i, 0 === s ? (l = 138, c = 3) : i === s ? (l = 6, c = 3) : (l = 7, c = 4);
                    }
                };
                let M = !1;
                const L = (e, t, n, r)=>{
                    A(e, 0 + (r ? 1 : 0), 3), ((e, t, n, r)=>{
                        R(e), r && (k(e, n), k(e, ~n)), e.pending_buf.set(e.window.subarray(t, t + n), e.pending), e.pending += n;
                    })(e, t, n, !0);
                };
                t.exports._tr_init = (e)=>{
                    M || ((()=>{
                        let e, t, n, r, i;
                        const f = new Array(16);
                        for(n = 0, r = 0; r < 28; r++)for(m[r] = n, e = 0; e < 1 << l[r]; e++)g[n++] = r;
                        for(g[n - 1] = r, i = 0, r = 0; r < 16; r++)for(y[r] = i, e = 0; e < 1 << c[r]; e++)h[i++] = r;
                        for(i >>= 7; r < s; r++)for(y[r] = i << 7, e = 0; e < 1 << c[r] - 7; e++)h[256 + i++] = r;
                        for(t = 0; t <= a; t++)f[t] = 0;
                        for(e = 0; e <= 143;)d[2 * e + 1] = 8, e++, f[8]++;
                        for(; e <= 255;)d[2 * e + 1] = 9, e++, f[9]++;
                        for(; e <= 279;)d[2 * e + 1] = 7, e++, f[7]++;
                        for(; e <= 287;)d[2 * e + 1] = 8, e++, f[8]++;
                        for(T(d, 287, f), e = 0; e < s; e++)p[2 * e + 1] = 5, p[2 * e] = x(e, 5);
                        w = new b(d, l, 257, o, a), _ = new b(p, c, 0, s, a), v = new b(new Array(0), u, 0, 19, 7);
                    })(), M = !0), e.l_desc = new O(e.dyn_ltree, w), e.d_desc = new O(e.dyn_dtree, _), e.bl_desc = new O(e.bl_tree, v), e.bi_buf = 0, e.bi_valid = 0, I(e);
                }, t.exports._tr_stored_block = L, t.exports._tr_flush_block = (e, t, n, r)=>{
                    let o, s, a = 0;
                    e.level > 0 ? (2 === e.strm.data_type && (e.strm.data_type = ((e)=>{
                        let t, n = 4093624447;
                        for(t = 0; t <= 31; t++, n >>>= 1)if (1 & n && 0 !== e.dyn_ltree[2 * t]) return 0;
                        if (0 !== e.dyn_ltree[18] || 0 !== e.dyn_ltree[20] || 0 !== e.dyn_ltree[26]) return 1;
                        for(t = 32; t < i; t++)if (0 !== e.dyn_ltree[2 * t]) return 1;
                        return 0;
                    })(e)), P(e, e.l_desc), P(e, e.d_desc), a = ((e)=>{
                        let t;
                        for(B(e, e.dyn_ltree, e.l_desc.max_code), B(e, e.dyn_dtree, e.d_desc.max_code), P(e, e.bl_desc), t = 18; t >= 3 && 0 === e.bl_tree[2 * f[t] + 1]; t--);
                        return e.opt_len += 3 * (t + 1) + 5 + 5 + 4, t;
                    })(e), o = e.opt_len + 3 + 7 >>> 3, s = e.static_len + 3 + 7 >>> 3, s <= o && (o = s)) : o = s = n + 5, n + 4 <= o && -1 !== t ? L(e, t, n, r) : 4 === e.strategy || s === o ? (A(e, 2 + (r ? 1 : 0), 3), D(e, d, p)) : (A(e, 4 + (r ? 1 : 0), 3), ((e, t, n, r)=>{
                        let i;
                        for(A(e, t - 257, 5), A(e, n - 1, 5), A(e, r - 4, 4), i = 0; i < r; i++)A(e, e.bl_tree[2 * f[i] + 1], 3);
                        j(e, e.dyn_ltree, t - 1), j(e, e.dyn_dtree, n - 1);
                    })(e, e.l_desc.max_code + 1, e.d_desc.max_code + 1, a + 1), D(e, e.dyn_ltree, e.dyn_dtree)), I(e), r && R(e);
                }, t.exports._tr_tally = (e, t, n)=>(e.pending_buf[e.d_buf + 2 * e.last_lit] = t >>> 8 & 255, e.pending_buf[e.d_buf + 2 * e.last_lit + 1] = 255 & t, e.pending_buf[e.l_buf + e.last_lit] = 255 & n, e.last_lit++, 0 === t ? e.dyn_ltree[2 * n]++ : (e.matches++, t--, e.dyn_ltree[2 * (g[n] + i + 1)]++, e.dyn_dtree[2 * E(t)]++), e.last_lit === e.lit_bufsize - 1), t.exports._tr_align = (e)=>{
                    A(e, 2, 3), S(e, 256, d), ((e)=>{
                        16 === e.bi_valid ? (k(e, e.bi_buf), e.bi_buf = 0, e.bi_valid = 0) : e.bi_valid >= 8 && (e.pending_buf[e.pending++] = 255 & e.bi_buf, e.bi_buf >>= 8, e.bi_valid -= 8);
                    })(e);
                };
            },
            {}
        ],
        42: [
            function(e, t, n) {
                "use strict";
                t.exports = function() {
                    this.input = null, this.next_in = 0, this.avail_in = 0, this.total_in = 0, this.output = null, this.next_out = 0, this.avail_out = 0, this.total_out = 0, this.msg = "", this.state = null, this.data_type = 2, this.adler = 0;
                };
            },
            {}
        ],
        43: [
            function(e, t, n) {
                "use strict";
                t.exports = e("./src/index-minimal");
            },
            {
                "./src/index-minimal": 44
            }
        ],
        44: [
            function(e, t, n) {
                "use strict";
                var r = n;
                function i() {
                    r.util._configure(), r.Writer._configure(r.BufferWriter), r.Reader._configure(r.BufferReader);
                }
                r.build = "minimal", r.Writer = e("./writer"), r.BufferWriter = e("./writer_buffer"), r.Reader = e("./reader"), r.BufferReader = e("./reader_buffer"), r.util = e("./util/minimal"), r.rpc = e("./rpc"), r.roots = e("./roots"), r.configure = i, i();
            },
            {
                "./reader": 45,
                "./reader_buffer": 46,
                "./roots": 47,
                "./rpc": 48,
                "./util/minimal": 51,
                "./writer": 52,
                "./writer_buffer": 53
            }
        ],
        45: [
            function(e, t, n) {
                "use strict";
                t.exports = l;
                var r, i = e("./util/minimal"), o = i.LongBits, s = i.utf8;
                function a(e, t) {
                    return RangeError("index out of range: " + e.pos + " + " + (t || 1) + " > " + e.len);
                }
                function l(e) {
                    this.buf = e, this.pos = 0, this.len = e.length;
                }
                var c, u = "undefined" != typeof Uint8Array ? function(e) {
                    if (e instanceof Uint8Array || Array.isArray(e)) return new l(e);
                    throw Error("illegal buffer");
                } : function(e) {
                    if (Array.isArray(e)) return new l(e);
                    throw Error("illegal buffer");
                }, f = function() {
                    return i.Buffer ? function(e) {
                        return (l.create = function(e) {
                            return i.Buffer.isBuffer(e) ? new r(e) : u(e);
                        })(e);
                    } : u;
                };
                function d() {
                    var e = new o(0, 0), t = 0;
                    if (!(this.len - this.pos > 4)) {
                        for(; t < 3; ++t){
                            if (this.pos >= this.len) throw a(this);
                            if (e.lo = (e.lo | (127 & this.buf[this.pos]) << 7 * t) >>> 0, this.buf[this.pos++] < 128) return e;
                        }
                        return e.lo = (e.lo | (127 & this.buf[this.pos++]) << 7 * t) >>> 0, e;
                    }
                    for(; t < 4; ++t)if (e.lo = (e.lo | (127 & this.buf[this.pos]) << 7 * t) >>> 0, this.buf[this.pos++] < 128) return e;
                    if (e.lo = (e.lo | (127 & this.buf[this.pos]) << 28) >>> 0, e.hi = (e.hi | (127 & this.buf[this.pos]) >> 4) >>> 0, this.buf[this.pos++] < 128) return e;
                    if (t = 0, this.len - this.pos > 4) {
                        for(; t < 5; ++t)if (e.hi = (e.hi | (127 & this.buf[this.pos]) << 7 * t + 3) >>> 0, this.buf[this.pos++] < 128) return e;
                    } else for(; t < 5; ++t){
                        if (this.pos >= this.len) throw a(this);
                        if (e.hi = (e.hi | (127 & this.buf[this.pos]) << 7 * t + 3) >>> 0, this.buf[this.pos++] < 128) return e;
                    }
                    throw Error("invalid varint encoding");
                }
                function p(e, t) {
                    return (e[t - 4] | e[t - 3] << 8 | e[t - 2] << 16 | e[t - 1] << 24) >>> 0;
                }
                function h() {
                    if (this.pos + 8 > this.len) throw a(this, 8);
                    return new o(p(this.buf, this.pos += 4), p(this.buf, this.pos += 4));
                }
                l.create = f(), l.prototype._slice = i.Array.prototype.subarray || i.Array.prototype.slice, l.prototype.uint32 = (c = 4294967295, function() {
                    if (c = (127 & this.buf[this.pos]) >>> 0, this.buf[this.pos++] < 128) return c;
                    if (c = (c | (127 & this.buf[this.pos]) << 7) >>> 0, this.buf[this.pos++] < 128) return c;
                    if (c = (c | (127 & this.buf[this.pos]) << 14) >>> 0, this.buf[this.pos++] < 128) return c;
                    if (c = (c | (127 & this.buf[this.pos]) << 21) >>> 0, this.buf[this.pos++] < 128) return c;
                    if (c = (c | (15 & this.buf[this.pos]) << 28) >>> 0, this.buf[this.pos++] < 128) return c;
                    if ((this.pos += 5) > this.len) throw this.pos = this.len, a(this, 10);
                    return c;
                }), l.prototype.int32 = function() {
                    return 0 | this.uint32();
                }, l.prototype.sint32 = function() {
                    var e = this.uint32();
                    return e >>> 1 ^ -(1 & e) | 0;
                }, l.prototype.bool = function() {
                    return 0 !== this.uint32();
                }, l.prototype.fixed32 = function() {
                    if (this.pos + 4 > this.len) throw a(this, 4);
                    return p(this.buf, this.pos += 4);
                }, l.prototype.sfixed32 = function() {
                    if (this.pos + 4 > this.len) throw a(this, 4);
                    return 0 | p(this.buf, this.pos += 4);
                }, l.prototype.float = function() {
                    if (this.pos + 4 > this.len) throw a(this, 4);
                    var e = i.float.readFloatLE(this.buf, this.pos);
                    return this.pos += 4, e;
                }, l.prototype.double = function() {
                    if (this.pos + 8 > this.len) throw a(this, 4);
                    var e = i.float.readDoubleLE(this.buf, this.pos);
                    return this.pos += 8, e;
                }, l.prototype.bytes = function() {
                    var e = this.uint32(), t = this.pos, n = this.pos + e;
                    if (n > this.len) throw a(this, e);
                    return this.pos += e, Array.isArray(this.buf) ? this.buf.slice(t, n) : t === n ? new this.buf.constructor(0) : this._slice.call(this.buf, t, n);
                }, l.prototype.string = function() {
                    var e = this.bytes();
                    return s.read(e, 0, e.length);
                }, l.prototype.skip = function(e) {
                    if ("number" == typeof e) {
                        if (this.pos + e > this.len) throw a(this, e);
                        this.pos += e;
                    } else do {
                        if (this.pos >= this.len) throw a(this);
                    }while (128 & this.buf[this.pos++]);
                    return this;
                }, l.prototype.skipType = function(e) {
                    switch(e){
                        case 0:
                            this.skip();
                            break;
                        case 1:
                            this.skip(8);
                            break;
                        case 2:
                            this.skip(this.uint32());
                            break;
                        case 3:
                            for(; 4 != (e = 7 & this.uint32());)this.skipType(e);
                            break;
                        case 5:
                            this.skip(4);
                            break;
                        default:
                            throw Error("invalid wire type " + e + " at offset " + this.pos);
                    }
                    return this;
                }, l._configure = function(e) {
                    r = e, l.create = f(), r._configure();
                    var t = i.Long ? "toLong" : "toNumber";
                    i.merge(l.prototype, {
                        int64: function() {
                            return d.call(this)[t](!1);
                        },
                        uint64: function() {
                            return d.call(this)[t](!0);
                        },
                        sint64: function() {
                            return d.call(this).zzDecode()[t](!1);
                        },
                        fixed64: function() {
                            return h.call(this)[t](!0);
                        },
                        sfixed64: function() {
                            return h.call(this)[t](!1);
                        }
                    });
                };
            },
            {
                "./util/minimal": 51
            }
        ],
        46: [
            function(e, t, n) {
                "use strict";
                t.exports = o;
                var r = e("./reader");
                (o.prototype = Object.create(r.prototype)).constructor = o;
                var i = e("./util/minimal");
                function o(e) {
                    r.call(this, e);
                }
                o._configure = function() {
                    i.Buffer && (o.prototype._slice = i.Buffer.prototype.slice);
                }, o.prototype.string = function() {
                    var e = this.uint32();
                    return this.buf.utf8Slice ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + e, this.len)) : this.buf.toString("utf-8", this.pos, this.pos = Math.min(this.pos + e, this.len));
                }, o._configure();
            },
            {
                "./reader": 45,
                "./util/minimal": 51
            }
        ],
        47: [
            function(e, t, n) {
                "use strict";
                t.exports = {};
            },
            {}
        ],
        48: [
            function(e, t, n) {
                "use strict";
                n.Service = e("./rpc/service");
            },
            {
                "./rpc/service": 49
            }
        ],
        49: [
            function(e, t, n) {
                "use strict";
                t.exports = i;
                var r = e("../util/minimal");
                function i(e, t, n) {
                    if ("function" != typeof e) throw TypeError("rpcImpl must be a function");
                    r.EventEmitter.call(this), this.rpcImpl = e, this.requestDelimited = Boolean(t), this.responseDelimited = Boolean(n);
                }
                (i.prototype = Object.create(r.EventEmitter.prototype)).constructor = i, i.prototype.rpcCall = function e(t, n, i, o, s) {
                    if (!o) throw TypeError("request must be specified");
                    var a = this;
                    if (!s) return r.asPromise(e, a, t, n, i, o);
                    if (a.rpcImpl) try {
                        return a.rpcImpl(t, n[a.requestDelimited ? "encodeDelimited" : "encode"](o).finish(), function(e, n) {
                            if (e) return a.emit("error", e, t), s(e);
                            if (null !== n) {
                                if (!(n instanceof i)) try {
                                    n = i[a.responseDelimited ? "decodeDelimited" : "decode"](n);
                                } catch (e) {
                                    return a.emit("error", e, t), s(e);
                                }
                                return a.emit("data", n, t), s(null, n);
                            }
                            a.end(!0);
                        });
                    } catch (e) {
                        return a.emit("error", e, t), void setTimeout(function() {
                            s(e);
                        }, 0);
                    }
                    else setTimeout(function() {
                        s(Error("already ended"));
                    }, 0);
                }, i.prototype.end = function(e) {
                    return this.rpcImpl && (e || this.rpcImpl(null, null, null), this.rpcImpl = null, this.emit("end").off()), this;
                };
            },
            {
                "../util/minimal": 51
            }
        ],
        50: [
            function(e, t, n) {
                "use strict";
                t.exports = i;
                var r = e("../util/minimal");
                function i(e, t) {
                    this.lo = e >>> 0, this.hi = t >>> 0;
                }
                var o = i.zero = new i(0, 0);
                o.toNumber = function() {
                    return 0;
                }, o.zzEncode = o.zzDecode = function() {
                    return this;
                }, o.length = function() {
                    return 1;
                };
                var s = i.zeroHash = "\0\0\0\0\0\0\0\0";
                i.fromNumber = function(e) {
                    if (0 === e) return o;
                    var t = e < 0;
                    t && (e = -e);
                    var n = e >>> 0, r = (e - n) / 4294967296 >>> 0;
                    return t && (r = ~r >>> 0, n = ~n >>> 0, ++n > 4294967295 && (n = 0, ++r > 4294967295 && (r = 0))), new i(n, r);
                }, i.from = function(e) {
                    if ("number" == typeof e) return i.fromNumber(e);
                    if (r.isString(e)) {
                        if (!r.Long) return i.fromNumber(parseInt(e, 10));
                        e = r.Long.fromString(e);
                    }
                    return e.low || e.high ? new i(e.low >>> 0, e.high >>> 0) : o;
                }, i.prototype.toNumber = function(e) {
                    if (!e && this.hi >>> 31) {
                        var t = 1 + ~this.lo >>> 0, n = ~this.hi >>> 0;
                        return t || (n = n + 1 >>> 0), -(t + 4294967296 * n);
                    }
                    return this.lo + 4294967296 * this.hi;
                }, i.prototype.toLong = function(e) {
                    return r.Long ? new r.Long(0 | this.lo, 0 | this.hi, Boolean(e)) : {
                        low: 0 | this.lo,
                        high: 0 | this.hi,
                        unsigned: Boolean(e)
                    };
                };
                var a = String.prototype.charCodeAt;
                i.fromHash = function(e) {
                    return e === s ? o : new i((a.call(e, 0) | a.call(e, 1) << 8 | a.call(e, 2) << 16 | a.call(e, 3) << 24) >>> 0, (a.call(e, 4) | a.call(e, 5) << 8 | a.call(e, 6) << 16 | a.call(e, 7) << 24) >>> 0);
                }, i.prototype.toHash = function() {
                    return String.fromCharCode(255 & this.lo, this.lo >>> 8 & 255, this.lo >>> 16 & 255, this.lo >>> 24, 255 & this.hi, this.hi >>> 8 & 255, this.hi >>> 16 & 255, this.hi >>> 24);
                }, i.prototype.zzEncode = function() {
                    var e = this.hi >> 31;
                    return this.hi = ((this.hi << 1 | this.lo >>> 31) ^ e) >>> 0, this.lo = (this.lo << 1 ^ e) >>> 0, this;
                }, i.prototype.zzDecode = function() {
                    var e = -(1 & this.lo);
                    return this.lo = ((this.lo >>> 1 | this.hi << 31) ^ e) >>> 0, this.hi = (this.hi >>> 1 ^ e) >>> 0, this;
                }, i.prototype.length = function() {
                    var e = this.lo, t = (this.lo >>> 28 | this.hi << 4) >>> 0, n = this.hi >>> 24;
                    return 0 === n ? 0 === t ? e < 16384 ? e < 128 ? 1 : 2 : e < 2097152 ? 3 : 4 : t < 16384 ? t < 128 ? 5 : 6 : t < 2097152 ? 7 : 8 : n < 128 ? 9 : 10;
                };
            },
            {
                "../util/minimal": 51
            }
        ],
        51: [
            function(e, t, n) {
                (function(t) {
                    (function() {
                        "use strict";
                        var r = n;
                        function i(e, t, n) {
                            for(var r = Object.keys(t), i = 0; i < r.length; ++i)void 0 !== e[r[i]] && n || (e[r[i]] = t[r[i]]);
                            return e;
                        }
                        function o(e) {
                            function t(e, n) {
                                if (!(this instanceof t)) return new t(e, n);
                                Object.defineProperty(this, "message", {
                                    get: function() {
                                        return e;
                                    }
                                }), Error.captureStackTrace ? Error.captureStackTrace(this, t) : Object.defineProperty(this, "stack", {
                                    value: (new Error).stack || ""
                                }), n && i(this, n);
                            }
                            return (t.prototype = Object.create(Error.prototype)).constructor = t, Object.defineProperty(t.prototype, "name", {
                                get: function() {
                                    return e;
                                }
                            }), t.prototype.toString = function() {
                                return this.name + ": " + this.message;
                            }, t;
                        }
                        r.asPromise = e("@protobufjs/aspromise"), r.base64 = e("@protobufjs/base64"), r.EventEmitter = e("@protobufjs/eventemitter"), r.float = e("@protobufjs/float"), r.inquire = e("@protobufjs/inquire"), r.utf8 = e("@protobufjs/utf8"), r.pool = e("@protobufjs/pool"), r.LongBits = e("./longbits"), r.isNode = Boolean(void 0 !== t && t && t.process && t.process.versions && t.process.versions.node), r.global = r.isNode && t || "undefined" != typeof window && window || "undefined" != typeof self && self || this, r.emptyArray = Object.freeze ? Object.freeze([]) : [], r.emptyObject = Object.freeze ? Object.freeze({}) : {}, r.isInteger = Number.isInteger || function(e) {
                            return "number" == typeof e && isFinite(e) && Math.floor(e) === e;
                        }, r.isString = function(e) {
                            return "string" == typeof e || e instanceof String;
                        }, r.isObject = function(e) {
                            return e && "object" == typeof e;
                        }, r.isset = r.isSet = function(e, t) {
                            var n = e[t];
                            return !(null == n || !e.hasOwnProperty(t)) && ("object" != typeof n || (Array.isArray(n) ? n.length : Object.keys(n).length) > 0);
                        }, r.Buffer = function() {
                            try {
                                var e = r.inquire("buffer").Buffer;
                                return e.prototype.utf8Write ? e : null;
                            } catch (e) {
                                return null;
                            }
                        }(), r._Buffer_from = null, r._Buffer_allocUnsafe = null, r.newBuffer = function(e) {
                            return "number" == typeof e ? r.Buffer ? r._Buffer_allocUnsafe(e) : new r.Array(e) : r.Buffer ? r._Buffer_from(e) : "undefined" == typeof Uint8Array ? e : new Uint8Array(e);
                        }, r.Array = "undefined" != typeof Uint8Array ? Uint8Array : Array, r.Long = r.global.dcodeIO && r.global.dcodeIO.Long || r.global.Long || r.inquire("long"), r.key2Re = /^true|false|0|1$/, r.key32Re = /^-?(?:0|[1-9][0-9]*)$/, r.key64Re = /^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/, r.longToHash = function(e) {
                            return e ? r.LongBits.from(e).toHash() : r.LongBits.zeroHash;
                        }, r.longFromHash = function(e, t) {
                            var n = r.LongBits.fromHash(e);
                            return r.Long ? r.Long.fromBits(n.lo, n.hi, t) : n.toNumber(Boolean(t));
                        }, r.merge = i, r.lcFirst = function(e) {
                            return e.charAt(0).toLowerCase() + e.substring(1);
                        }, r.newError = o, r.ProtocolError = o("ProtocolError"), r.oneOfGetter = function(e) {
                            for(var t = {}, n = 0; n < e.length; ++n)t[e[n]] = 1;
                            return function() {
                                for(var e = Object.keys(this), n = e.length - 1; n > -1; --n)if (1 === t[e[n]] && void 0 !== this[e[n]] && null !== this[e[n]]) return e[n];
                            };
                        }, r.oneOfSetter = function(e) {
                            return function(t) {
                                for(var n = 0; n < e.length; ++n)e[n] !== t && delete this[e[n]];
                            };
                        }, r.toJSONOptions = {
                            longs: String,
                            enums: String,
                            bytes: String,
                            json: !0
                        }, r._configure = function() {
                            var e = r.Buffer;
                            e ? (r._Buffer_from = e.from !== Uint8Array.from && e.from || function(t, n) {
                                return new e(t, n);
                            }, r._Buffer_allocUnsafe = e.allocUnsafe || function(t) {
                                return new e(t);
                            }) : r._Buffer_from = r._Buffer_allocUnsafe = null;
                        };
                    }).call(this);
                }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
            },
            {
                "./longbits": 50,
                "@protobufjs/aspromise": 17,
                "@protobufjs/base64": 18,
                "@protobufjs/eventemitter": 19,
                "@protobufjs/float": 20,
                "@protobufjs/inquire": 21,
                "@protobufjs/pool": 22,
                "@protobufjs/utf8": 23
            }
        ],
        52: [
            function(e, t, n) {
                "use strict";
                t.exports = f;
                var r, i = e("./util/minimal"), o = i.LongBits, s = i.base64, a = i.utf8;
                function l(e, t, n) {
                    this.fn = e, this.len = t, this.next = void 0, this.val = n;
                }
                function c() {}
                function u(e) {
                    this.head = e.head, this.tail = e.tail, this.len = e.len, this.next = e.states;
                }
                function f() {
                    this.len = 0, this.head = new l(c, 0, 0), this.tail = this.head, this.states = null;
                }
                var d = function() {
                    return i.Buffer ? function() {
                        return (f.create = function() {
                            return new r;
                        })();
                    } : function() {
                        return new f;
                    };
                };
                function p(e, t, n) {
                    t[n] = 255 & e;
                }
                function h(e, t) {
                    this.len = e, this.next = void 0, this.val = t;
                }
                function g(e, t, n) {
                    for(; e.hi;)t[n++] = 127 & e.lo | 128, e.lo = (e.lo >>> 7 | e.hi << 25) >>> 0, e.hi >>>= 7;
                    for(; e.lo > 127;)t[n++] = 127 & e.lo | 128, e.lo = e.lo >>> 7;
                    t[n++] = e.lo;
                }
                function m(e, t, n) {
                    t[n] = 255 & e, t[n + 1] = e >>> 8 & 255, t[n + 2] = e >>> 16 & 255, t[n + 3] = e >>> 24;
                }
                f.create = d(), f.alloc = function(e) {
                    return new i.Array(e);
                }, i.Array !== Array && (f.alloc = i.pool(f.alloc, i.Array.prototype.subarray)), f.prototype._push = function(e, t, n) {
                    return this.tail = this.tail.next = new l(e, t, n), this.len += t, this;
                }, h.prototype = Object.create(l.prototype), h.prototype.fn = function(e, t, n) {
                    for(; e > 127;)t[n++] = 127 & e | 128, e >>>= 7;
                    t[n] = e;
                }, f.prototype.uint32 = function(e) {
                    return this.len += (this.tail = this.tail.next = new h((e >>>= 0) < 128 ? 1 : e < 16384 ? 2 : e < 2097152 ? 3 : e < 268435456 ? 4 : 5, e)).len, this;
                }, f.prototype.int32 = function(e) {
                    return e < 0 ? this._push(g, 10, o.fromNumber(e)) : this.uint32(e);
                }, f.prototype.sint32 = function(e) {
                    return this.uint32((e << 1 ^ e >> 31) >>> 0);
                }, f.prototype.uint64 = function(e) {
                    var t = o.from(e);
                    return this._push(g, t.length(), t);
                }, f.prototype.int64 = f.prototype.uint64, f.prototype.sint64 = function(e) {
                    var t = o.from(e).zzEncode();
                    return this._push(g, t.length(), t);
                }, f.prototype.bool = function(e) {
                    return this._push(p, 1, e ? 1 : 0);
                }, f.prototype.fixed32 = function(e) {
                    return this._push(m, 4, e >>> 0);
                }, f.prototype.sfixed32 = f.prototype.fixed32, f.prototype.fixed64 = function(e) {
                    var t = o.from(e);
                    return this._push(m, 4, t.lo)._push(m, 4, t.hi);
                }, f.prototype.sfixed64 = f.prototype.fixed64, f.prototype.float = function(e) {
                    return this._push(i.float.writeFloatLE, 4, e);
                }, f.prototype.double = function(e) {
                    return this._push(i.float.writeDoubleLE, 8, e);
                };
                var y = i.Array.prototype.set ? function(e, t, n) {
                    t.set(e, n);
                } : function(e, t, n) {
                    for(var r = 0; r < e.length; ++r)t[n + r] = e[r];
                };
                f.prototype.bytes = function(e) {
                    var t = e.length >>> 0;
                    if (!t) return this._push(p, 1, 0);
                    if (i.isString(e)) {
                        var n = f.alloc(t = s.length(e));
                        s.decode(e, n, 0), e = n;
                    }
                    return this.uint32(t)._push(y, t, e);
                }, f.prototype.string = function(e) {
                    var t = a.length(e);
                    return t ? this.uint32(t)._push(a.write, t, e) : this._push(p, 1, 0);
                }, f.prototype.fork = function() {
                    return this.states = new u(this), this.head = this.tail = new l(c, 0, 0), this.len = 0, this;
                }, f.prototype.reset = function() {
                    return this.states ? (this.head = this.states.head, this.tail = this.states.tail, this.len = this.states.len, this.states = this.states.next) : (this.head = this.tail = new l(c, 0, 0), this.len = 0), this;
                }, f.prototype.ldelim = function() {
                    var e = this.head, t = this.tail, n = this.len;
                    return this.reset().uint32(n), n && (this.tail.next = e.next, this.tail = t, this.len += n), this;
                }, f.prototype.finish = function() {
                    for(var e = this.head.next, t = this.constructor.alloc(this.len), n = 0; e;)e.fn(e.val, t, n), n += e.len, e = e.next;
                    return t;
                }, f._configure = function(e) {
                    r = e, f.create = d(), r._configure();
                };
            },
            {
                "./util/minimal": 51
            }
        ],
        53: [
            function(e, t, n) {
                "use strict";
                t.exports = o;
                var r = e("./writer");
                (o.prototype = Object.create(r.prototype)).constructor = o;
                var i = e("./util/minimal");
                function o() {
                    r.call(this);
                }
                function s(e, t, n) {
                    e.length < 40 ? i.utf8.write(e, t, n) : t.utf8Write ? t.utf8Write(e, n) : t.write(e, n);
                }
                o._configure = function() {
                    o.alloc = i._Buffer_allocUnsafe, o.writeBytesBuffer = i.Buffer && i.Buffer.prototype instanceof Uint8Array && "set" === i.Buffer.prototype.set.name ? function(e, t, n) {
                        t.set(e, n);
                    } : function(e, t, n) {
                        if (e.copy) e.copy(t, n, 0, e.length);
                        else for(var r = 0; r < e.length;)t[n++] = e[r++];
                    };
                }, o.prototype.bytes = function(e) {
                    i.isString(e) && (e = i._Buffer_from(e, "base64"));
                    var t = e.length >>> 0;
                    return this.uint32(t), t && this._push(o.writeBytesBuffer, t, e), this;
                }, o.prototype.string = function(e) {
                    var t = i.Buffer.byteLength(e);
                    return this.uint32(t), t && this._push(s, t, e), this;
                }, o._configure();
            },
            {
                "./util/minimal": 51,
                "./writer": 52
            }
        ]
    }, {}, [
        8
    ]);
}

},{"@parcel/transformer-js/src/esmodule-helpers.js":"ky9Vb"}],"ky9Vb":[function(require,module,exports) {
exports.interopDefault = function(a) {
    return a && a.__esModule ? a : {
        default: a
    };
};
exports.defineInteropFlag = function(a) {
    Object.defineProperty(a, "__esModule", {
        value: true
    });
};
exports.exportAll = function(source, dest) {
    Object.keys(source).forEach(function(key) {
        if (key === "default" || key === "__esModule" || dest.hasOwnProperty(key)) return;
        Object.defineProperty(dest, key, {
            enumerable: true,
            get: function() {
                return source[key];
            }
        });
    });
    return dest;
};
exports.export = function(dest, destName, get) {
    Object.defineProperty(dest, destName, {
        enumerable: true,
        get: get
    });
};

},{}]},["lmkzo"], "lmkzo", "parcelRequire0912")

 globalThis.define=__define;  })(globalThis.define);