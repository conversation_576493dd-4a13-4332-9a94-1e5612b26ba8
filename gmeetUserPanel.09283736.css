.popup_input_form_group_ff {
  margin-bottom: 15px;
}

.popup_input_form_group_last_ff {
  flex-direction: column;
  max-height: 170px;
  margin-bottom: 0;
  font-family: PoppinsRegular;
  display: flex;
  overflow-y: auto;
}

.popup_input_form_group_last_ff > div {
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  border: 1px solid #e4e4ea;
  border-radius: 5px;
  min-height: 48px;
  padding: 3px 8px;
  display: flex;
  overflow-x: auto;
}

.popup_input_form_group_last_ff > div::-webkit-scrollbar {
  width: 2px;
  height: 6px;
}

.popup_input_form_group_last_ff > div::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.popup_input_form_group_last_ff > div > div {
  color: #3c4149;
  background-color: #f5f5f5;
  border-radius: 5px;
  min-height: 32px;
  margin: 4px;
  padding: 4px 10px;
  font-size: 14px;
}

.popup_input_form_group_last_ff > div > div div:first-child {
  background-color: #f5f5f5;
  padding-left: 0;
}

.popup_input_form_group_last_ff > div > div div:last-child {
  background: none;
  width: 14px;
  height: 14px;
  margin: 0 0 0 3px;
}

.popup_input_form_group_last_ff > div > div div:last-child:before, .popup_input_form_group_last_ff > div > div div:last-child:after {
  background-color: #90959d;
}

.popup_input_form_group_last_ff > div > input {
  max-width: 100%;
  min-height: 38px;
  font-size: 14px;
}

.popup_input_form_group_last_ff > div > input:focus {
  color: #222;
  outline: none;
  font-size: 14px;
}

.popup_input_form_group_last_ff > div > input::placeholder {
  color: #1e1e2266;
  font-size: 16px;
  font-weight: normal;
}

.popup_input_form_group_last_ff > div > input::-webkit-input-placeholder {
  color: #1e1e2266;
  font-size: 16px;
  font-weight: normal;
}

.popup_input_form_group_last_ff > div > input:-ms-input-placeholder {
  color: #1e1e2266;
  font-size: 16px;
  font-weight: normal;
}

.gmeet_share_emails_ff {
  flex-direction: column;
  flex-grow: 1;
  padding-top: 24px;
  display: flex;
  overflow-y: auto;
}

.gmeet_share_emails_ff .emailLabel {
  color: #878f97;
  flex-shrink: 0;
  margin-bottom: 12px;
  font-family: PoppinsRegular;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
}

.gmeet_share_emails_ff .emailInput {
  color: #3c4149;
  background: #f9fafb;
  border: 1px solid #eaedf0;
  border-radius: 4px;
  width: 100%;
  max-width: 438px;
  padding: 12px;
  font-size: 14px;
  line-height: 20px;
}

.gmeet_share_emails_ff .emailInput::placeholder {
  color: #90959d;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.popup_input_form_group_ff .loader_block {
  text-align: center;
  color: #878f97;
  border: none;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 0;
  margin-top: -16px;
  padding: 0;
  font-size: 12px;
  position: absolute;
}

.react_tag_input_ff {
  box-sizing: border-box;
  color: #333;
  background: #fff;
  border: 1px solid #e1e1e1;
  border-radius: 3px;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  height: auto;
  min-height: 2.375em;
  padding: .1875em .375em;
  font-size: 1rem;
  display: flex;
  position: relative;
  overflow-y: auto;
}

.react_tag_input_ff * {
  box-sizing: border-box;
}

.react_tag_input_ff > * {
  margin: .1875em;
}

.react_tag_input_ff__input {
  font-family: PoppinsRegular;
  color: #333 !important;
  width: auto !important;
  height: 1.875em !important;
  box-shadow: none !important;
  -webkit-appearance: none !important;
  background: #fff !important;
  border: none !important;
  border-radius: 3px !important;
  outline: 0 !important;
  flex-grow: 1 !important;
  margin: 0 .1875em !important;
  padding: 0 0 0 .1875em !important;
  line-height: 1 !important;
  transition: all .3s !important;
}

.react_tag_input_ff__input:focus {
  overflow: initial !important;
}

.react_tag_input_ff__input::placeholder {
  color: #333;
}

.react_tag_input_ff__input:-moz-placeholder {
  color: #333;
}

.react_tag_input_ff__input:-ms-input-placeholder {
  color: #333;
}

.react_tag_input_ff__input::-moz-placeholder {
  color: #333;
}

.react_tag_input_ff__input::-webkit-input-placeholder {
  color: #333;
}

.react_tag_input_ff__input:focus {
  border: none;
}

.react_tag_input_ff__tag {
  background: #e1e1e1;
  border-radius: 3px;
  align-items: center;
  font-size: .85em;
  line-height: 1;
  display: flex;
  position: relative;
}

.react_tag_input_ff__tag__content {
  white-space: normal;
  word-break: break-word;
  border: none;
  outline: 0;
  padding: 0 .46875em;
  line-height: 1.5;
}

.react_tag_input_ff__tag__remove {
  cursor: pointer;
  background: #d4d4d4;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  width: 2em;
  height: 2em;
  font-size: .85em;
  position: relative;
}

.react_tag_input_ff__tag__remove:before, .react_tag_input_ff__tag__remove:after {
  content: " ";
  background-color: #333;
  width: 1.5px;
  height: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
}

.react_tag_input_ff__tag__remove:before {
  transform: translateX(-50%)translateY(-50%)rotate(45deg);
}

.react_tag_input_ff__tag__remove:after {
  transform: translateX(-50%)translateY(-50%)rotate(-45deg);
}

.react_tag_input_ff__tag__remove-readonly {
  width: 0 !important;
}

.react_tag_input_ff__tag__remove-readonly:before, .react_tag_input_ff__tag__remove-readonly:after {
  content: "";
  width: 0 !important;
}

