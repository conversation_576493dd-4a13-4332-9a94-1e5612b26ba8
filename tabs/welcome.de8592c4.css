.heading_logo_container_ff {
  justify-content: center;
  margin: 0;
  display: flex;
}

.heading_logo_container_ff .heading_logo {
  margin: 0;
  display: inline-flex;
}

.heading_logo_container_ff .heading_logo img {
  width: 32px;
  height: 32px;
}

.heading_logo_container_ff .img_text {
  color: #22262a;
  margin-left: 8px;
  font-size: 22px;
  font-weight: 600;
  line-height: 33px;
}

.heading_logo_container_ff_small .heading_logo img {
  width: 24px;
  height: 24px;
}

.heading_logo_container_ff_small .img_text {
  color: #101626;
  z-index: 1;
  margin-left: 8px;
  font-family: PoppinsRegular;
  font-size: 16px;
  line-height: 24px;
}

.heading_logo_container_ff_small .img_text.white_text {
  color: #fff;
}

.popup_bottom_info_ff {
  background-color: #f9fafb;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  max-width: 452px;
  padding: 16px 0;
  display: flex;
  position: relative;
}

.popup_bottom_info_ff:before {
  content: "";
  background-color: #eaedf0;
  height: 1px;
  position: absolute;
  inset: 0 0 auto;
}

.popup_bottom_info_ff .popup_bottom_info_ff_text_ff {
  color: #98a2b3;
  text-transform: none;
  text-align: center;
  background-color: #0000;
  margin: 0;
  padding: 0 30px;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  font-family: Roboto !important;
}

.popup_bottom_info_ff .version_text {
  letter-spacing: -.02em;
  color: #878f97;
  background-color: #0000;
  max-width: 275px;
  margin: 0;
  padding-top: 1px;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

.login_screen_ff {
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 24px;
  display: flex;
}

.popUp_btn_disable_ff {
  flex-direction: column;
}

.popUp_btn_disable_ff .disableBtnText {
  color: #f6008d;
  text-align: center;
  font-size: 14px;
}

.google_btn_ff {
  color: #656ce6;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d6d8ff;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 48px;
  padding: 11px;
  font-family: PoppinsRegular;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
}

.google_btn_ff .google_btn_ff_container {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.google_btn_ff .google_btn_ff_container .googleImgCanvas {
  margin-right: 16px;
  display: flex;
}

.google_btn_ff .google_btn_ff_container .googleImgCanvas img {
  width: 22px;
  height: 22px;
}

.google_btn_ff:hover, .google_btn_ff:focus {
  background-color: #eaedf0;
}

.gmeet_heading_ff {
  letter-spacing: -.01em;
  color: #353b41;
  text-align: center;
  height: 40px;
  margin: 24px 0 32px;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px;
}

.gmeet_heading_ff_black {
  color: #000 !important;
}

.gmeet_heading_ff.gmeet_heading_ff_black .purple {
  color: #7e00ff !important;
}

@keyframes fadeInUp_ff {
  0% {
    background-color: #fff;
  }

  100% {
    background-color: #000;
  }
}

.tooltip_text_ff {
  color: #fff;
  z-index: 9999999;
  pointer-events: none;
  white-space: pre-line;
  background: #3c4043;
  border-radius: 3px;
  max-width: 200px;
  margin: 5px -5px;
  padding: 10px 15px;
  font-family: DMSANS;
  font-size: 13px;
  font-weight: 100;
  line-height: 20px;
  display: inline-block;
}

.tooltip_text_animation_ff {
  -webkit-animation-name: fadeInUp_ff;
  -webkit-animation-duration: .5s;
  -webkit-animation-fill-mode: flash;
  background-color: #fff;
  animation-name: fadeInUp_ff;
  animation-duration: .5s;
  animation-fill-mode: both;
}

.tooltip_inverted_ff {
  padding: 5px 10px;
  color: #475467 !important;
  background: #fcfcfd !important;
  border: 1px solid #eaecf0 !important;
  border-radius: 4px !important;
  font-family: Inter !important;
  font-size: 12px !important;
  box-shadow: 0 2px 2px #1018280a !important;
}

.centered_tooltip_ff {
  font-weight: 600;
  text-align: center !important;
  opacity: 1 !important;
}

.centered_tooltip_ff span {
  background-color: #0000;
}

.toggle_switch_ff.switch {
  float: right;
  background-color: #0000;
  width: 40px;
  height: 20px;
  display: block;
  position: relative;
}

.toggle_switch_ff.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle_switch_ff .slider {
  cursor: pointer;
  background-color: #c4c4c4;
  transition: all .4s;
  position: absolute;
  inset: 0;
}

.toggle_switch_ff .slider:before {
  content: "";
  background-color: #fff;
  width: 12px;
  height: 12px;
  transition: all .4s;
  position: absolute;
  top: 4px;
  bottom: 2px;
  left: 4px;
}

.toggle_switch_ff input:checked + .slider {
  background-color: #6e75ff;
}

.toggle_switch_ff input:checked + .slider:before {
  transform: translateX(19px);
}

.toggle_switch_ff .slider.round {
  border-radius: 10px;
}

.toggle_switch_ff .slider.round:before {
  border-radius: 50%;
}

.popup_settings_container_ff {
  box-sizing: border-box;
  background-color: #0000;
  width: 100%;
  padding: 26px 0 20px;
  box-shadow: 0 5px 20px #1e1e2212;
}

.line_break_ff {
  opacity: .5;
  border: 1px solid #eaedf0;
  margin: 22px 0 0;
}

.popup_btn_panel_ff {
  justify-content: space-between;
  align-items: center;
  margin: 24px 24px 0;
  padding-top: 20px;
  display: flex;
  position: relative;
}

.popup_btn_panel_ff:before {
  content: "";
  background-color: #eaedf0;
  height: 1px;
  position: absolute;
  inset: 0 -24px auto;
}

.popup_prev_icon_ff .btn_canvas_ff {
  display: inline-flex;
}

.popup_prev_icon_ff .popUpBtn_topSettingTxt {
  letter-spacing: -.01em;
  color: #484f56;
  margin: 0 0 0 8px;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
}

.popup_prev_icon_ff {
  align-items: center;
  padding: 0 24px;
  display: flex;
}

.popup_email_panel_ff {
  word-break: break-word;
  letter-spacing: -.01em;
  color: #98a2b3;
  box-shadow: none;
  cursor: pointer;
  background-color: #fff;
  border: none;
  outline: none;
  justify-content: flex-end;
  align-items: center;
  margin-left: auto;
  padding: 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 140%;
  display: flex;
  font-family: Roboto !important;
}

.popup_email_panel_ff:hover, .popup_email_panel_ff:focus {
  box-shadow: none;
  border: none;
  outline: none;
}

.popup_email_panel_ff .helpIconSpan {
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  margin-left: 10.5px;
  display: flex;
}

.popup_email_panel_ff .helpIconSpan img {
  width: 20px;
  height: 20px;
}

.popup_prev_icon_btn_ff {
  cursor: pointer;
  width: 24px;
  height: 24px;
  box-shadow: none;
  background-color: #0000;
  border: none;
  outline: none;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  display: flex;
}

.popup_prev_icon_btn_ff:hover, .popup_prev_icon_btn_ff:focus {
  box-shadow: none;
  background-color: #0000;
  border: none;
  outline: none;
}

.popup_settings_panel_ff {
  background-color: #0000;
  flex-direction: column;
  width: auto;
  display: flex;
}

.auto_capture_canvas_ff {
  width: 100%;
}

.settings_toggle_btn_ff {
  background-color: #0000;
  border-bottom: 1px solid #eaedf0;
  justify-content: space-between;
  padding: 20px 24px;
  display: flex;
}

.askFred_canvas_ff {
  background-color: #0000;
  border-bottom: 1px solid #eaedf0;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px 24px;
  display: flex;
}

.askFred_canvas_ff .no-border-bottom {
  border-bottom: none !important;
}

.askFred_canvas_ff .no-padding {
  padding: 0 !important;
}

.settings_toggle_btn_ff:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.toggle_btn_mainPage_ff {
  max-width: 352px;
  margin: 0;
  padding: 14px 16px;
}

.toggle_btn_mainPage_ff:before {
  content: "";
  background-color: #eaedf0;
  height: 1px;
  position: absolute;
  inset: 0 -24px auto;
}

.toggle_text_ff {
  background-color: #0000;
  flex-direction: column;
  margin-right: 40px;
  display: flex;
}

.settings_toggle_btn_ff .toggle_text_ff .zoomLabel {
  letter-spacing: -.01em;
  color: #101828;
  background-color: #0000;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.settings_toggle_btn_ff .toggle_text_ff .description {
  letter-spacing: -.01em;
  color: #878f97;
  background-color: #0000;
  padding-top: 8px;
  font-family: Roboto;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.settings_toggle_btn_ff .toggle_text_ff .infoIcon {
  background-color: #0000;
  margin-left: 8px;
  display: inline-block;
  position: relative;
  top: 4px;
}

.settings_toggle_btn_ff .toggle_text_ff .infoIcon img {
  width: 18px;
  height: 18px;
}

.toggle_container_ff {
  background: none;
  flex-shrink: 0;
  padding-top: 2px;
  display: flex;
}

.popup_btn_panel_ff .logoutBtn {
  cursor: pointer;
  box-shadow: none;
  color: #878f97;
  background-color: #0000;
  border: none;
  outline: none;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  display: flex;
}

.popup_btn_panel_ff .logoutBtn:hover, .popup_btn_panel_ff .logoutBtn:focus {
  box-shadow: none;
  color: #878f97;
  cursor: pointer;
  opacity: .7;
  background-color: #0000;
  border: none;
  outline: none;
}

.popup_btn_panel_ff .logoutBtn .arrowRightIcon {
  flex-shrink: 0;
  align-items: center;
  margin-left: 8px;
  display: flex;
}

.popup_btn_panel_ff .logoutBtn .arrowRightIcon img {
  width: 16px;
  height: 16px;
}

.popup_btn_panel_ff .userEmail {
  color: #484f56;
  background-color: #0000;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  display: flex;
}

.terms_conditions_ff_container {
  color: #98a2b3;
  letter-spacing: -.01em;
  text-align: center;
  margin: 15px 0;
  font-family: Roboto;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
}

.terms_conditions_ff_container a {
  color: #878f97;
}

.popup_settings_container_ff .popup_video_warning {
  background: #f9fafb;
  border-radius: 4px;
  flex-grow: 0;
  margin: 24px 24px 35px;
  padding: 12px;
  font-family: Roboto !important;
}

.popup_settings_container_ff .popup_video_warning, .popup_settings_container_ff .popup_video_warning a {
  color: #878f97;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  font-family: Roboto !important;
}

.lds-spinner_ff {
  width: 18px;
  height: 18px;
  display: inline-block;
  position: relative;
}

.lds-spinner_ff div {
  transform-origin: 7px 15px;
  animation: 1.2s linear infinite lds-spinner_ff;
}

.lds-spinner_ff div:after, .lds-spinner_ff div:after {
  content: " ";
  background: #000;
  border-radius: 20%;
  width: 2px;
  height: 9px;
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 5px;
}

.lds-spinner_ff div:first-child {
  animation-delay: -1.1s;
  transform: rotate(0);
}

.lds-spinner_ff div:nth-child(2) {
  animation-delay: -1s;
  transform: rotate(30deg);
}

.lds-spinner_ff div:nth-child(3) {
  animation-delay: -.9s;
  transform: rotate(60deg);
}

.lds-spinner_ff div:nth-child(4) {
  animation-delay: -.8s;
  transform: rotate(90deg);
}

.lds-spinner_ff div:nth-child(5) {
  animation-delay: -.7s;
  transform: rotate(120deg);
}

.lds-spinner_ff div:nth-child(6) {
  animation-delay: -.6s;
  transform: rotate(150deg);
}

.lds-spinner_ff div:nth-child(7) {
  animation-delay: -.5s;
  transform: rotate(180deg);
}

.lds-spinner_ff div:nth-child(8) {
  animation-delay: -.4s;
  transform: rotate(210deg);
}

.lds-spinner_ff div:nth-child(9) {
  animation-delay: -.3s;
  transform: rotate(240deg);
}

.lds-spinner_ff div:nth-child(10) {
  animation-delay: -.2s;
  transform: rotate(270deg);
}

.lds-spinner_ff div:nth-child(11) {
  animation-delay: -.1s;
  transform: rotate(300deg);
}

.lds-spinner_ff div:nth-child(12) {
  animation-delay: 0s;
  transform: rotate(330deg);
}

@keyframes lds-spinner_ff {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.centered_spinner_ff, .manual_meeting_popup_fixed_center {
  background-color: #fff9;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  inset: 0;
  position: absolute !important;
}

.manual_meeting_popup_fixed_center {
  height: 56%;
  top: 30%;
}

.popup_loader_spinner_ff {
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  display: flex;
  top: -15px;
  left: -6px;
}

.askFred_banner_main_ff {
  box-sizing: border-box;
  background: #f9fafb;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 16px;
  padding: 6px 8px;
  display: flex;
}

.askFred_banner_main_ff .askFred_logo {
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  display: flex;
}

.askFred_banner_main_ff .askFred_logo img {
  max-width: 100%;
}

.askFred_banner_main_ff .askFred_text {
  letter-spacing: -.01em;
  color: #344054e6;
  text-align: left;
  width: inherit;
  cursor: pointer;
  margin: 0 8px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  text-decoration: underline;
  font-family: PoppinsRegular !important;
}

.askFred_banner_main_ff .askFred_text:hover, .askFred_banner_main_ff .askFred_text:focus {
  text-decoration: underline;
}

.askFred_banner_main_ff .askFred_badge {
  color: #fff;
  text-transform: uppercase;
  background: #32c381;
  border-radius: 16px;
  justify-content: center;
  align-items: center;
  padding: 6px 12px;
  font-size: 12px;
  line-height: 14px;
  display: flex;
  font-family: Roboto !important;
}

.gmeet_container_ff {
  flex-direction: column;
  align-items: center;
  display: flex;
}

.gmeet_actions_container {
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 24px 0;
  display: flex;
}

.view_meetings_Container_ff {
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #eaecf0;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 40px;
  text-decoration: none;
  display: flex;
}

.view_meetings_Container_ff:hover, .view_meetings_Container_ff:focus {
  background-color: #eef1f4;
}

.view_meetings_Container_ff .viewMeetingText {
  color: #353b41;
  background-color: #0000;
  padding-left: 8px;
  padding-right: 10px;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.view_meetings_Container_ff .viewMeetingIcon {
  background-color: #0000;
  flex-shrink: 0;
  align-items: center;
  width: 20px;
  height: 20px;
  margin-right: 16px;
  display: flex;
}

.account_type_container_ff {
  width: 100%;
  height: 40px;
  position: relative;
}

.account_type_loader_ff {
  text-align: center;
  align-items: center;
  width: 100%;
  margin: 24px 0 12px;
}

.account_type_container_ff .getSummaryTextPanel {
  background: #f8f8ff;
  justify-content: space-between;
  align-items: center;
  margin: 0 -24px;
  padding: 10px 24px;
  display: flex;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.account_type_container_ff .getSummaryTextPanel .summaryTextIcon {
  width: 20px;
  height: 20px;
  display: flex;
}

.account_type_container_ff .getSummaryTextPanel .summaryText {
  color: #344054;
  letter-spacing: -.14px;
  width: 100%;
  margin-left: 12px;
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
}

.account_type_container_ff .accountTypeSubText {
  color: #6e75ff;
  background-color: #0000;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-decoration: underline;
}

.account_type_container_ff .accountTypeSubText:hover, .account_type_container_ff .accountTypeSubText:focus {
  color: #1c26eb;
}

.credits_container_ff {
  border: 1px solid #eee;
  border-radius: 4px;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  padding: 16px 0;
  display: flex;
}

.credits_container_ff .creditsCanvas {
  padding-left: 16px;
  display: flex;
}

.credits_container_ff .creditsCanvas .documentIcon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  display: flex;
  overflow: hidden;
}

.credits_container_ff .creditsCanvas .documentIcon img {
  width: 100%;
  height: 100%;
}

.credits_container_ff .creditsCanvas .creditsTextBox {
  letter-spacing: -.04em;
  flex-direction: column;
  justify-content: flex-start;
  margin: -2px 0 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  display: flex;
}

.credits_container_ff .creditsCanvas .creditsTextBox .creditsHead {
  color: #3c4149;
}

.credits_container_ff .creditsCanvas .creditsTextBox .creditsSubHead {
  color: #6b6f76;
  padding-top: 2px;
}

.credits_container_ff .creditsCanvas .creditsTextBox .emptyCredits {
  color: #f08c00 !important;
}

.credits_container_ff .creditsIcon {
  cursor: pointer;
  background-color: #0000;
  flex-shrink: 0;
  align-items: center;
  width: 20px;
  height: 20px;
  margin-right: 16px;
  display: flex;
}

.credit_upgrade_btn_ff {
  box-shadow: none;
  cursor: pointer;
  text-align: center;
  color: #fff;
  background-color: #f08c00;
  border: none;
  border-radius: 5px;
  outline: none;
  justify-content: center;
  align-items: center;
  width: 352px;
  min-height: 48px;
  margin-top: 16px;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
}

.credit_upgrade_btn_ff:hover, .credit_upgrade_btn_ff:focus {
  color: #fff5f5;
  background-color: #b86b00;
}

.centered_tooltip_ff span {
  font-weight: 600;
  text-align: center !important;
  opacity: 1 !important;
  background-color: #0000 !important;
}

.transcribe_box_ff {
  width: 352px;
  position: relative;
}

.gmeet_logo_ff {
  z-index: 1;
  background: #f9fafb;
  border: none;
  border-radius: 4px;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  display: flex;
}

.gmeet_logo_ff img {
  width: 24px;
  height: 24px;
}

.gmeet_heading_ff {
  letter-spacing: -.01em;
  color: #fff;
  text-align: center;
  z-index: 1;
  height: 40px;
  margin: 20px auto 24px;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.gmeet_heading_ff .purple {
  color: #a6f4c5;
}

.start_meeting_disclaimer_ff {
  text-align: center;
  opacity: .7;
  pointer-events: none;
  user-select: none;
  color: red;
  margin: 0;
  font-style: italic;
}

.ff_manual_recording_container {
  border-radius: 8px;
  gap: 24px;
  width: 100%;
  padding: 0;
  display: flex;
}

.ff_manual_recording_container .ff_manual_recording_container_img {
  object-fit: cover;
  width: 32px;
  height: 32px;
}

.ff_manual_recording_container .ff_manual_recording_container_img img {
  width: 100%;
  height: 100%;
}

.ff_manual_recording_container .ff_manual_recording_container_body {
  flex-direction: column;
  gap: 21px;
  width: 100%;
  display: flex;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_data {
  flex-direction: column;
  gap: 2px;
  display: flex;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_data .ff_manual_recording_container_body_title_panel {
  align-items: center;
  display: flex;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_data .ff_manual_recording_container_body_title_panel .ff_manual_recording_container_body_title {
  color: #101828;
  letter-spacing: -.11px;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 300px;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  display: inline-block;
  overflow: hidden;
  font-family: Inter !important;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_data .ff_manual_recording_container_body_title_panel .dot {
  background-color: #b32318;
  border-radius: 50%;
  width: 5px;
  height: 5px;
  margin-left: 8px;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_data .ff_manual_recording_container_body_text {
  color: #98a2b3;
  letter-spacing: -.16px;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  font-family: Inter !important;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_actions {
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  width: 100%;
  display: flex;
}

.recording_body_actions {
  flex-direction: column;
}

.recording_body_actions .add_notetaker {
  width: 100% !important;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_actions .record_btn {
  color: #fff;
  letter-spacing: -.16px;
  cursor: pointer;
  background: #6938ef;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  width: 100%;
  height: 38px;
  padding: 0 8px;
  font-family: DMSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  display: flex;
}

.ff_manual_recording_container_body_actions .record_btn:not([disabled]):hover, .ff_manual_recording_container_body_actions .record_btn:not([disabled]):focus {
  background: #5925dc !important;
}

.ff_manual_recording_container_body_actions .record_btn:disabled {
  opacity: .8;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_actions .separator {
  color: #98a2b3;
  letter-spacing: -.16px;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.ff_manual_recording_container_body_actions .btn_canvas_ff {
  width: 50%;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_actions .add_notetaker {
  cursor: pointer;
  color: #5925dc;
  letter-spacing: -.16px;
  background: #f4f3ff;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  width: 50%;
  height: 38px;
  padding: 6px 12px 6px 8px;
  font-family: DMSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  display: flex;
}

.ff_manual_recording_container_body_actions .add_notetaker:not([disabled]):hover, .ff_manual_recording_container_body_actions .add_notetaker:not([disabled]):focus {
  background: #ebe9fe !important;
}

.ff_manual_recording_container_body_actions .add_notetaker:disabled {
  opacity: .8;
}

.ff_manual_recording_container .ff_manual_recording_container_body .ff_manual_recording_container_body_actions .add_notetaker img {
  background: inherit;
}

.ff_inp_container {
  z-index: 1;
  background: #fff;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  padding: 0;
  display: flex;
  position: absolute;
  inset: 0;
}

.ff_inp_header_container {
  border-bottom: 1px solid #f2f4f7;
  justify-content: flex-start;
  align-items: center;
  padding: 0 16px;
  display: flex;
}

.ff_inp_header_container .ff_inp_header_title {
  color: #101828;
  letter-spacing: -.11px;
  cursor: pointer;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  font-family: Inter !important;
}

.ff_inp_header_button {
  cursor: pointer;
  width: 24px;
  height: 24px;
  box-shadow: none;
  background-color: #0000;
  border: none;
  outline: none;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  display: flex;
}

.ff_inp_header_button:hover, .ff_inp_header_button:focus {
  box-shadow: none;
  background-color: #0000;
  border: none;
  outline: none;
}

.ff_inp_container .ff_inp_img {
  object-fit: cover;
  width: 32px;
  height: 32px;
}

.ff_inp_container .ff_inp_img img {
  width: 100%;
  height: 100%;
}

.ff_inp_container .ff_inp_container_body {
  flex-direction: column;
  gap: 21px;
  height: 100%;
  display: flex;
  overflow-y: auto;
}

.ff_inp_container .ff_inp_container_body_data {
  align-items: flex-start;
  gap: 16px;
  padding: 0 20px;
  display: flex;
}

.ff_inp_container_body .ff_inp_success_container {
  border-bottom: none !important;
  padding-bottom: 0 !important;
}

.ff_inp_container .ff_inp_container_body_title_container {
  flex-direction: column;
  gap: 2px;
  padding-right: 1rem;
  display: flex;
}

.ff_inp_container .ff_inp_container_body_title_panel {
  align-items: center;
  gap: 4px;
  display: flex;
}

.ff_inp_container .ff_inp_container_body_title {
  color: #101828;
  letter-spacing: -.11px;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 300px;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  display: inline-block;
  overflow: hidden;
  font-family: Inter !important;
}

.ff_inp_container .dot {
  background-color: #b32318;
  border-radius: 50%;
  width: 5px;
  height: 5px;
  margin-left: 8px;
}

.ff_inp_container .ff_inp_container_body_text {
  color: #98a2b3;
  letter-spacing: -.16px;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  font-family: Inter !important;
}

.ff_inp_container .ff_inp_container_body_input {
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  align-items: flex-end;
  gap: 24px;
  padding: 0 20px 16px;
  display: flex;
}

.ff_inp_container_body_input_form {
  flex-direction: column;
  align-items: flex-end;
  gap: 24px;
  display: flex;
}

.ff_inp_container .ff_inp_container_body_actions {
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  display: flex;
}

.primary_button {
  color: #fff;
  letter-spacing: -.16px;
  cursor: pointer;
  background: #6938ef;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  height: 32px;
  padding: 0 8px;
  font-family: DMSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  display: flex;
}

.primary_button:not([disabled]):hover, .primary_button:not([disabled]):focus {
  background: #5925dc !important;
}

.primary_button:disabled {
  opacity: .8;
}

.secondary_button {
  cursor: pointer;
  color: #475467;
  letter-spacing: -.16px;
  background: none;
  border: 1px solid #f2f4f7;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  height: 32px;
  padding: 6px 12px 6px 8px;
  font-family: DMSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  display: flex;
}

.secondary_button:not([disabled]):hover, .secondary_button:not([disabled]):focus {
  background: #f9fafb !important;
}

.secondary_button:disabled {
  opacity: .8;
}

.secondary_button img {
  background: inherit;
}

.ff_inp_loading_container {
  justify-content: center;
  align-items: center;
  width: 123px;
  height: 32px;
  display: flex;
  position: relative;
  bottom: 6px;
}

.ff_inp_container .success-actions {
  justify-content: flex-start;
  align-items: center;
  margin-top: 16px;
  display: flex;
}

.ff_invite_notetaker_success_container {
  background: #fff;
  border: 1px solid #eee;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 16px;
  display: flex;
  position: relative;
}

.gmeet_title_wrap_ff {
  border: 1px solid #eaedf0;
  border-radius: 8px;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  display: flex;
}

.gmeet_title_wrap_ff .meeting_detail {
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.gmeet_title_wrap_ff .meeting_detail_withBtn {
  width: fit-content;
  max-width: 70% !important;
}

.gmeet_title_wrap_ff .meeting_detail_withoutBtn {
  max-width: 400px !important;
}

.gmeet_title_wrap_ff .meeting_detail_withBtn.editing {
  width: 80%;
}

.gmeet_title_wrap_ff .meeting_detail .meeting_det_image {
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin-right: 12px;
  display: flex;
}

.gmeet_title_wrap_ff .meeting_detail .meeting_det_image img {
  width: 100%;
  max-width: 100%;
  height: 100%;
}

.gmeet_title_wrap_ff .meeting_detail .meeting_title_box {
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  display: flex;
}

.gmeet_title_wrap_ff .meeting_detail_withBtn .meeting_title_box {
  max-width: 90% !important;
}

.gmeetTitleContainer .meeting_detail .meeting_title_box .title_panel_ff {
  align-items: center;
  gap: 10px;
  width: 100%;
  max-width: 398px;
  display: flex;
}

.title_panel_ff {
  flex-direction: row;
  display: flex;
}

.title_panel_ff .title_panel_edit {
  cursor: pointer;
  border-radius: 30%;
  flex-grow: 1;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 14px;
  max-width: fit-content;
  height: 14px;
  margin-left: 8px;
  padding: 4px;
  display: flex;
}

.title_panel_ff .title_panel_edit:hover, .title_panel_ff .title_panel_edit:focus {
  background-color: #eee;
}

.title_panel_ff .title_panel_edit img {
  object-fit: contain;
  width: 100%;
  height: 100%;
}

.gmeet_title_wrap_ff .meeting_detail .meeting_title_box .meeting_title_text {
  color: #353b41;
  white-space: nowrap;
  text-overflow: ellipsis;
  letter-spacing: .01em;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  overflow: hidden;
  font-family: PoppinsMedium !important;
}

.gmeet_title_wrap_ff .meeting_detail .meeting_title_box .meeting_input_text, .gmeet_title_wrap_ff .meeting_detail .meeting_title_box .meeting_input_text:focus {
  width: 100%;
  box-shadow: none;
  border: 1px solid #afafaf;
  border-radius: 4px;
  outline: none;
  min-height: 24px;
  padding: 0 5px;
  font-size: 14px;
}

.gmeet_title_wrap_ff .meeting_detail .meeting_title_box .meeting_info {
  color: #878f97;
  width: 100%;
  margin: 2px 0 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
}

.gmeet_title_wrap_ff .meeting_handler {
  text-transform: uppercase;
  color: #fff;
  min-width: 80px;
  min-height: 40px;
  box-shadow: none;
  cursor: pointer;
  text-align: center;
  background-color: #6e75ff;
  border: none;
  border-radius: 4px;
  outline: none;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
}

.gmeet_title_wrap_ff .meeting_handler:hover, .gmeet_title_wrap_ff .meeting_handler:focus {
  box-shadow: none;
  background-color: #656ce6;
  border: none;
  outline: none;
}

.gmeet_title_wrap_ff .meeting_handler:disabled {
  pointer-events: none;
  background: #9ea1e0;
}

.gmeet_title_wrap_ff .meeting_handler_stopBtn {
  background-color: #fff5f5;
  min-width: 32px;
  min-height: 32px;
  padding: 6px;
}

.gmeet_title_wrap_ff .meeting_handler_stopBtn:hover, .gmeet_title_wrap_ff .meeting_handler_stopBtn:focus {
  background-color: #ffd4d4;
}

.gmeet_title_wrap_ff .disabled_meeting_handler_startBtn, .gmeet_title_wrap_ff .disabled_meeting_handler_startBtn:hover, .gmeet_title_wrap_ff .disabled_meeting_handler_startBtn:focus {
  cursor: not-allowed;
  background: #9ea1e0;
}

.transcribeSubHead_ff {
  color: #6b6f76;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin: 2px 0 0;
  padding-top: 2px;
  font-size: 14px;
  font-weight: 400;
  line-height: 16px;
  display: flex;
}

.transcribeSubHead_ff .dotSpace {
  margin-left: 7px;
  font-family: Roboto;
}

.transcribeSubHead_ff .transcribingTimer {
  letter-spacing: 1px;
  width: 44px;
  margin-left: 8px;
  display: inline-block;
}

.transcribeSubHead_ff .transcribingTimerExtend {
  width: 70px !important;
}

.dot_ff {
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 8px;
  height: 8px;
  display: flex;
}

.success_dot_ff {
  background-color: #12b76a !important;
}

.warning_dot_ff {
  background-color: #f08c00 !important;
}

.error_dot_ff {
  background-color: red !important;
}

.large_dot_ff {
  width: 12px !important;
  height: 12px !important;
}

.medium_dot_ff {
  width: 10px !important;
  height: 10px !important;
}

.small_dot_ff {
  width: 6px !important;
  height: 6px !important;
}

.ffPopup_recMmeet_wrapper {
  height: 100%;
  padding: 26px 24px 16px;
  overflow: hidden;
}

.ffPopup_recMmeet_wrapper .ffPopup_recMeet_header {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.ffPopup_recMmeet_wrapper .ffPopup_recMeet_header .ff_recent_back_space {
  letter-spacing: -.14px;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  gap: 8px;
  font-family: PoppinsRegular;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
  display: flex;
}

.ffPopup_recMmeet_wrapper .ffPopup_recMeet_header .ff_recent_back_space .ff_recent_back_arrow {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.ffPopup_recMmeet_wrapper .ff_recent_settings_icon {
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
}

.ffPopup_recMmeet_wrapper .ffPopup_recMeet_title_wrapper {
  justify-content: space-between;
  align-items: center;
  margin: 26px 0 8px;
  display: flex;
}

.ffPopup_recMeet_details {
  max-height: 484px;
  overflow: scroll;
}

.recMeet_single_meet_box {
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  display: flex;
}

.recMeet_single_meet_box .recMeet_info_panel {
  flex-direction: column;
  gap: 4px;
  display: flex;
}

.recMeet_single_meet_box .recMeet_info_panel .recMeet_info_title {
  color: var(--Gray-800, #1d2939);
  letter-spacing: -.14px;
  text-overflow: ellipsis;
  text-wrap: nowrap;
  white-space: nowrap;
  width: 190px;
  font-family: PoppinsRegular;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  overflow: hidden;
}

.recMeet_single_meet_box .recMeet_info_panel .recMeet_info_time_panel {
  color: var(--Gray-400, #98a2b3);
  font-family: PoppinsRegular;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.recMeet_empty_meet_box {
  text-transform: uppercase;
  color: #bbb;
  pointer-events: none;
  user-select: none;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100px;
  font-size: 18px;
  display: flex;
}

body {
  margin-top: 0;
}

.welcome_container_ff {
  padding-top: 20px;
  padding-bottom: 56px;
}

.welcome_container_ff * {
  font-family: DMSans !important;
}

.welcome_card_col_ff {
  width: 100%;
  max-width: 388px;
}

.welcome_cards_container_ff {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 50px 120px 120px;
  display: flex;
}

.welcome_main_content_ff {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 660px;
  display: flex;
}

.welcome_main_content_ff .gmeet_logo_ff {
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  display: flex;
}

.welcome_main_content_ff .gmeet_logo_ff img {
  width: 100%;
  height: 100%;
}

.welcome_content_sbHd_ff {
  text-align: center;
  letter-spacing: -.02em;
  color: #101828;
  margin-top: 32px;
  font-size: 40px;
  font-style: normal;
  font-weight: 400;
  line-height: 46px;
}

.welcome_video_warning_ff {
  text-align: center;
  color: #475467;
  margin-top: 20px;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
}

.welcome_card_main_ff {
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 80px;
  display: flex;
}

.welcome_card_ff {
  width: 100%;
}

.welcome_card_ff_img {
  -ms-border-radius: 16px;
  -o-border-radius: 16px;
  border-radius: 16px;
  height: 210px;
  overflow: hidden;
}

.img_initial_ff {
  object-fit: initial;
  width: 100%;
  height: 100%;
}

.welcome_card_ff_content {
  color: #475467;
  margin-top: 28px;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.welcome_card_ff_hd {
  letter-spacing: -.01em;
  color: #101828;
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 500;
  line-height: 20px;
}

.img_tag_ff {
  vertical-align: middle;
  object-fit: scale-down;
  border-style: none;
}

.img_fluid_ff {
  z-index: 1;
  max-width: 100%;
  height: auto;
}

.welcome_card_ff_canvas {
  justify-content: space-between;
  gap: 18px;
  width: 100%;
  max-width: 1400px;
  display: flex;
  overflow: auto;
}

.floating_container_ff {
  position: absolute;
  top: 28px;
  right: 80px;
}

.floating_container_ff .arrowUp {
  justify-content: flex-end;
  margin-right: 46px;
  display: flex;
}

.floating_container_ff .floatingBox {
  box-sizing: border-box;
  background: #fff;
  border: 1px solid #eaedf0;
  border-radius: 8px;
  width: 336px;
  min-height: 90px;
  margin-top: 8px;
  padding: 16px 20px;
  box-shadow: 0 0 10px #0000001a;
}

.floating_container_ff .floatingBox .floatingBoxHeading {
  letter-spacing: -.01em;
  color: #353b41;
  justify-content: flex-start;
  align-items: flex-start;
  font-size: 16px;
  line-height: 26px;
  display: flex;
}

.floating_container_ff .floatingBox .floatingBoxHeading img {
  margin: 3px 8px 0;
}

.floating_container_ff .floatingBox .floatingBoxHeading .floatingLogo {
  margin-top: 5px;
}

.floating_container_ff .floatingBox .floatingBoxBody {
  letter-spacing: -.01em;
  color: #878f97;
  margin-top: 8px;
  font-size: 13px;
  font-weight: 400;
  line-height: 22px;
}

.floating_icon_info_ff {
  background: #f5f5ff;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  margin: 75px 120px 0;
  padding: 12px 16px;
  display: flex;
}

.floating_icon_info_ff .floatingIconLogos, .floating_icon_info_ff .floatingffIcon {
  text-align: center;
  justify-content: center;
  margin-left: auto;
  display: flex;
}

.floating_icon_info_ff .floatingIconLogos img {
  width: 80px;
  height: 32px;
}

.floating_icon_info_ff .floatingIconText {
  letter-spacing: -.01em;
  color: #22262a;
  width: 100%;
  margin-left: 8px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.popup_bottom_info_ff_container_ff {
  color: #878f97;
  justify-content: center;
  align-items: center;
  margin: 64px 120px 0;
  display: flex;
}

.popup_bottom_info_ff_text_ff {
  color: #98a2b3;
  text-transform: uppercase;
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.welcome_card_ff-shimmer .welcome_card_ff_hdr {
  border-radius: 8px;
  height: 216px;
  display: flex;
  overflow: hidden;
}

.welcome_card_ff-shimmer .welcome_card_ff_img {
  border-radius: 8px;
  width: 100%;
}

.welcome-shimmer .welcome_card_ff-shimmer .welcome_card_ff_hd {
  width: 100%;
  max-width: 280px;
  height: 24px;
}

.askFred_cards_container_ff {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.askFred_content_hello_ff_container {
  justify-content: center;
  align-items: center;
  width: 100%;
  display: flex;
}

.askFred_content_hello_ff {
  text-align: center;
  letter-spacing: -.03em;
  color: #6938ef;
  background-color: #6938ef1a;
  border-radius: 2rem;
  margin-top: 93px;
  padding: 1rem 2rem;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 23px;
}

.askFred_cards_container_ff .askFred_content_heading {
  text-align: center;
  letter-spacing: -.02em;
  color: #101828;
  margin-top: 32px;
  font-size: 40px;
  font-style: normal;
  font-weight: 400;
  line-height: 46px;
}

.askFred_cards_container_ff .askFred_content_subHeading {
  text-align: center;
  color: #475467;
  align-items: center;
  max-width: 660px;
  margin-top: 20px;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
}

.askFred_handler_btn_ff {
  color: #fff;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  gap: 9px;
  min-width: 207px;
  padding: 14px 16px;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  display: flex;
}

.askFred_cards_container_ff .askFred_handler_white {
  color: #475467;
  background: #fff;
  gap: 13px;
  margin-top: 8px;
}

.askFred_handler_black_ff {
  background: #000;
  margin-top: 32px;
}

.askFred_marquee_container_ff {
  width: 100%;
  height: 575px;
  margin-top: 60px;
  position: relative;
  overflow: hidden;
}

.askFred_marquee_container_ff .askFred_marquee_bg_image {
  background: linear-gradient(#fff0 0%, #d9d6fe 100%);
  height: 100%;
}

.askFred_marquee_container_ff .askFred_marquee_bg_image img {
  object-fit: cover;
  opacity: .4;
  width: 100%;
  height: 520px;
}

.askFred_marquee_canvas_ff {
  width: 100%;
  position: absolute;
  top: 0;
}

.askFred_marquee_canvas_ff .askFred_marquee {
  margin-top: 70px;
}

.askFred_marquee_canvas_ff .askFred_marquee > div {
  align-items: flex-start;
}

.askFred_icon_panel_ff {
  align-items: center;
  gap: 8px;
  display: flex;
  position: absolute;
  inset: 240px 0 auto auto;
}

.askFred_icon_panel_ff .askFred_icon {
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  display: flex;
}

.askFred_single_marquee_card_ff {
  flex-direction: column;
  width: 288px;
  margin: 0 26px;
  display: flex;
}

.askFred_single_marquee_card_ff .askFred_single_cardHead {
  z-index: 1;
  background: #fff;
  border-radius: 4px 8px 0 0;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  width: fit-content;
  margin-bottom: -1px;
  margin-left: 45px;
  padding: 4px 8px 5px;
  display: flex;
}

.askFred_single_marquee_card_ff .askFred_single_cardHead .single_card_image {
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
  display: flex;
}

.askFred_single_marquee_card_ff .askFred_single_cardHead .single_card_image img {
  max-width: 100%;
}

.askFred_single_marquee_card_ff .askFred_single_cardHead .span {
  letter-spacing: -.02em;
  color: #1d2939;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
  font-family: Roboto !important;
}

.askFred_single_marquee_card_ff .askFred_single_cardBody {
  background: #fff;
  border: 1px solid #eaecf0;
  border-radius: 8px;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  display: flex;
}

.askFred_single_marquee_card_ff .askFred_single_cardBody .askFred_single_suggestion_icon {
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  display: flex;
}

.askFred_single_marquee_card_ff .askFred_single_cardBody .askFred_single_suggestion_icon img {
  max-width: 100%;
}

.askFred_single_marquee_card_ff .askFred_single_cardBody .askFred_single_text {
  letter-spacing: -.02em;
  color: #1d2939;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  font-family: Roboto !important;
}

.askFred_images_panel_container {
  z-index: 2;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  display: flex;
  position: absolute;
  bottom: 0;
}

.askFred_rtp_panel_image {
  z-index: 3;
  height: 499px;
  margin-right: -1.5rem;
}

.askFred_gmeet_panel_image {
  height: 397px;
  margin-top: 2rem;
}

@media screen and (width <= 768px) {
  .askFred_images_panel_container {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .askFred_marquee_container_ff {
    height: 935px;
  }

  .askFred_rtp_panel_image {
    margin-right: 0;
  }

  .askFred_gmeet_panel_image {
    margin-top: 0;
    margin-bottom: 2rem;
  }
}

.askFred_video_modal_wrapper_ff {
  z-index: 2;
  background: #00000080;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  display: flex;
  position: fixed;
  inset: 0;
}

.askFred_video_modal_wrapper_ff .askFred_video_modal {
  background: #fff;
  border-radius: 10px;
  flex-direction: column;
  width: 80%;
  height: 80%;
  padding: 26px;
  display: flex;
}

.askFred_video_modal_wrapper_ff .askFred_video_modal .askFred_video_modal_header {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.askFred_video_modal_wrapper_ff .askFred_video_modal .askFred_video_modal_header .askFred_video_header_title {
  font-size: 22px;
  font-weight: bold;
  line-height: 30px;
}

.askFred_video_modal_wrapper_ff .askFred_video_modal .askFred_video_modal_header .askFred_video_header_cross {
  cursor: pointer;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  display: flex;
}

.askFred_video_modal_wrapper_ff .askFred_video_modal .askFred_video_modal_header .askFred_video_header_cross img {
  max-width: 100%;
}

.askFred_video_modal_wrapper_ff .askFred_video_modal .askFred_video_iframe {
  border: 1px solid #000;
  border-radius: 10px;
  width: 100%;
  height: 100%;
  margin-top: 20px;
}

