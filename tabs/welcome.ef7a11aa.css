#plasmo-shadow-container > div {
  pointer-events: none !important;
  background: none !important;
  position: fixed !important;
  inset: 0 !important;
}

.main_top_container_ff {
  width: 400px;
}

.popup_container_wrapper_ff {
  background-color: #fff;
}

.popup_container_wrapper_ff * {
  font-family: PoppinsRegular;
}

.popup_container_wrapper_ff img {
  background-color: #0000;
}

.popup_container_ff {
  box-shadow: 0 5px 20px #1e1e2212;
}

.popup_div_ff {
  box-sizing: border-box;
  background-color: #fff;
  width: 100%;
  padding: 24px;
}

.main_component_ff {
  width: 100%;
  padding-bottom: 0 !important;
}

.popup_top_icon_div_ff {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  display: flex;
}

.popup_top_icon_div_ff .btn_canvas_ff {
  display: inline-flex;
}

.popup_top_icon_div_ff .ff_popup_top_image {
  z-index: 0;
  width: 100%;
  height: auto;
  position: absolute;
  inset: 0 0 auto;
}

.popup_top_icon_btn_ff {
  cursor: pointer;
  z-index: 1;
  background: none;
  border: none;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  padding: 0;
  font-size: 20px;
  display: inline-flex;
}

.popup_top_icon_btn_ff img {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.popup_top_icon_btn_ff:focus {
  outline: none;
}

.dot_ff {
  background-color: red;
  border-radius: 50%;
  width: 15px;
  height: 15px;
}

.dot_ff-top-right {
  position: absolute;
  top: 33%;
  right: 16px;
}

@media (prefers-reduced-motion: reduce) {
  .dot_ff {
    animation: none;
  }
}

.topbar_cross_icon_ff {
  cursor: pointer;
  background: #eaeaea;
  border: 2px solid #fff;
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  padding: 0;
  display: flex;
}

.topbar_cross_icon_ff img {
  width: 12px;
  height: 12px;
}

.btn_canvas_ff .topbar_detail_submit_btn_ff {
  cursor: pointer;
  color: #6e75ff;
  background: #f0f0ff;
  border: none;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  min-width: 108px;
  min-height: 40px;
  margin: 0;
  padding: 10px 30px;
  font-family: PoppinsRegular;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  display: flex;
}

.btn_canvas_ff .topbar_detail_submit_btn_ff:hover {
  box-shadow: none;
  color: #fff;
  cursor: pointer;
  background: #6e75ff;
  border: none;
  outline: none;
}

.btn_canvas_ff .topbar_detail_okay_btn_ff {
  cursor: pointer;
  color: #555;
  background: #eee;
  border: none;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  min-width: 108px;
  min-height: 40px;
  margin: 0;
  padding: 10px 30px;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  display: flex;
}

.btn_canvas_ff .topbar_detail_okay_btn_ff:hover, .btn_canvas_ff .topbar_detail_okay_btn_ff:focus {
  box-shadow: none;
  color: #eee;
  cursor: pointer;
  background: #555;
  border: none;
  outline: none;
}

.btn_canvas_ff .topbar_detail_submit_btn_ff:disabled, .btn_canvas_ff .topbar_detail_okay_btn_ff:disabled {
  color: #696969 !important;
  background-color: #d3d3d3 !important;
}

.spaced_icon_ff {
  margin-left: 5px;
  background-color: #0000 !important;
}

.toggle_btn_ff {
  justify-content: center;
  align-items: center;
  padding: 6px 0 18px;
  display: flex;
  position: relative;
}

.create_notification_container_ff {
  cursor: auto;
  box-sizing: border-box;
  z-index: 99999;
  pointer-events: initial;
  background-color: #fff;
  border: none;
  border-radius: 8px;
  flex-direction: column;
  align-items: flex-start;
  width: 512px;
  margin-top: 16px;
  padding: 24px;
  position: absolute;
  inset: 20px 100px auto auto;
  box-shadow: 0 0 10px;
}

plasmo-csui#ff-overlay-container, plasmo-csui#real-time-panel {
  z-index: 999;
  pointer-events: none;
  position: absolute;
  inset: 0;
}

.ff_input_container {
  background: none;
  border: none;
  border-radius: 4px;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  width: -webkit-fill-available;
  display: flex;
}

.ff_input_container .input_input {
  resize: none;
  background: none;
  border: 1px solid #f2f4f7;
  border-radius: 8px;
  width: -webkit-fill-available;
  height: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  box-shadow: none !important;
  color: #475467 !important;
  padding: 8px 16px !important;
  font-family: Roboto !important;
  font-size: 14px !important;
}

.ff_input_container .input_input:focus-visible {
  outline: none;
}

.ff_input_container .input_label {
  letter-spacing: -.01em;
  color: #101828;
  background-color: #0000;
  padding-bottom: 8px;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}

.ff_input_container .input_description {
  letter-spacing: -.01em;
  color: #878f97;
  background-color: #0000;
  margin: 0;
  padding-bottom: 8px;
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.input_icon_container {
  border: 1px solid #f2f4f7;
  border-radius: 8px;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  width: -webkit-fill-available;
  padding-left: 16px;
  display: flex;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.input_icon_container .input_input {
  border: none;
  padding-left: 0 !important;
}

.ff_select_container {
  background: none;
  border: none;
  border-radius: 4px;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  width: -webkit-fill-available;
  display: flex;
  position: relative;
}

.ff_select_option_list_container {
  z-index: 1;
  background: #fff;
  border: 1px solid #f2f4f7;
  border-top: 0;
  border-radius: 0 0 8px 8px;
  width: -webkit-fill-available;
  max-height: 80px;
  margin: 0;
  padding: 10px;
  position: absolute;
  top: 100%;
  left: 0;
  overflow-y: scroll;
}

.ff_select_option_list {
  width: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
  overflow-x: hidden;
}

.ff_select_option_item {
  color: #475467;
  text-overflow: ellipsis;
  cursor: pointer;
  align-self: stretch;
  align-items: center;
  gap: 8px;
  min-height: 16px;
  padding: 8px;
  display: flex;
  overflow: hidden;
}

.ff_select_option_item:hover, .ff_select_option_item:focus {
  background: #f9fafb;
  border-radius: 8px;
}

.ff_select_option_item span {
  white-space: nowrap;
  text-overflow: ellipsis;
  background: none;
  overflow: hidden;
}

.ff_select_search {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.ff_select_dropdown_container {
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 100%;
  margin-top: 10px;
  margin-bottom: 55px;
  display: flex;
  position: relative;
}

